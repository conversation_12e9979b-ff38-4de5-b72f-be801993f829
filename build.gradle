plugins {
    id 'io.spring.dependency-management' version '1.0.11.RELEASE' apply false
    id 'org.springframework.boot' version '2.6.15' apply false
    id 'com.github.johnrengelman.shadow' version '2.0.4' apply false
    id 'com.github.ben-manes.versions' version "0.38.0"
    id 'se.patrikerdes.use-latest-versions' version '0.2.16'
    id 'idea'
    id 'eclipse'
}

ext {
    artifactProjects = [
            project(":cpe-products-monitor-backend")
    ]
    springCloudVersion = "2021.0.3"
    mavenUserHome = "MAVEN_USER_HOME"
}
Map<String, String> versionMap = getVersionMap();
Map<String, List<String>> branchVersionMap = getBranchVersionMap();
allprojects {
    apply plugin: 'java'
    apply plugin: 'eclipse-wtp'
    apply plugin: 'java-library'
    apply plugin: 'io.spring.dependency-management'
    apply plugin: 'com.github.ben-manes.versions'
    // gradle useLatestVersions
    apply plugin: 'se.patrikerdes.use-latest-versions'

    group 'com.cairh'
    version '0.1.0-21'

    repositories {
        //优先maven本地仓库(url需指定为实际的本地仓库地址(必需指定到仓库顶层,不止到M2_HOME级,形如:D:\soft\maven\repo),亦可见于IDEA的Maven - Local repository,对动态版本无影响)
//        mavenLocal {url System.getenv(mavenUserHome) }

        // mavenCentral()
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven {
            allowInsecureProtocol = true
            url 'http://maven.cairenhui.com/nexus/content/repositories/crh_dev'
        }
        maven {
            allowInsecureProtocol = true
            url 'http://maven.cairenhui.com/nexus/content/repositories/thirdparty'
        }
        maven {
            allowInsecureProtocol = true
            url 'http://maven.cairenhui.com/nexus/content/repositories/3rd_gtja'
        }
    }

    tasks.withType(JavaCompile) {
        options.encoding = "UTF-8"
        // 添加参数名称编译的支持， 可以运行时获取
        options.compilerArgs << '-parameters'
        // 关闭编译警告
        options.compilerArgs << "-Xlint:-unchecked" << "-Xlint:-deprecation" << '-Xlint:-varargs'
    }

    jar {
        manifest {
            attributes('Manifest-Version': project.version,
                    'Implementation-Git-Version': getGitVersion(),
                    'Implementation-Time': buildTime("yyyy-MM-dd HH:mm"),
                    'Implementation-Title': project.name,
                    'Implementation-Vendor': '杭州财人汇网络股份有限公司',
                    'Implementation-Vendor-Id': 'CRH',
                    'Build-Author-Id': getAuthor(),
                    'Build-Author-Email': getAuthorEmail())
        }
    }

    test {
        useJUnitPlatform()
    }

    dependencies {
        compileOnly('org.projectlombok:lombok')
        annotationProcessor('org.projectlombok:lombok')
        annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")
        testImplementation('org.junit.jupiter:junit-jupiter-api')
        testRuntimeOnly('org.junit.jupiter:junit-jupiter-engine')
        List<String> includes = branchVersionMap.get("includes")
        includes.forEach { String e -> api e }
    }

    configurations.all {
        List<String> excludes = branchVersionMap.get("excludes")
        excludes.forEach { String e ->
            String[] splits = e.split(":")
            exclude group: splits[0], module: splits[1]
        }
        resolutionStrategy {
            eachDependency { DependencyResolveDetails details ->
                String groupVersion = versionMap.get(details.requested.group);
                if (groupVersion != null && groupVersion != '') {
                    details.useVersion groupVersion;
                    return
                }
                String moduleVersion = versionMap.get(details.requested.group + ':' + details.requested.name)
                if (moduleVersion != null && moduleVersion != '') {
                    details.useVersion moduleVersion;
                    return
                }

            }

            cacheDynamicVersionsFor 0, 'seconds'
            cacheChangingModulesFor 0, 'seconds'
        }
    }

    dependencyManagement {
        imports {
            mavenBom "com.cairh:cpe-starter-parent:0.3.85"
        }
        dependencies {
            // cpe infra
            dependency "com.cairh:cpe-util:0.3.85"
            dependency "com.cairh:cpe-context:0.3.85"
            dependency "com.cairh:cpe-trace:0.3.85"
            dependency "com.cairh:cpe-auth:0.3.85"
            dependency "com.cairh:cpe-mem:0.3.85"
            dependency "com.cairh:cpe-config:0.3.85"
            dependency "com.cairh:cpe-db:0.3.85"
            dependency "com.cairh:cpe-rpc:0.3.85"

            // cpe scatter
            dependency "com.cairh:cpe-job-core:0.0.1-beta.169"
            dependency "com.cairh:cpe-common-backend:0.1.57"
            dependency "com.cairh:cpe-esb-basedata-server-api:0.1.31-gt+"
            dependency 'com.cairh:cpe-esb-component-server-api:0.1.19-gj+'

            dependency 'com.alibaba.spring:spring-context-support:1.0.11'

            // tongweb
            dependency 'com.gtja:tongweb-embed:7.0.E.2_P1'
            dependency 'com.gtja:tongweb-spring-boot-starter:2.x.0.RELEASE_P1'
        }
    }
}

task deleteRemoteJar {
    if (!project.hasProperty("remoteJar")) {
        return
    }
    artifactProjects.each {
        def command = "curl -v -u ${rootProject.nexus_username}:${rootProject.nexus_password} -X DELETE http://maven.cairenhui.com/nexus/content/repositories/crh_dev/com/cairh/${it.getName()}/${version}"
        println "command: ${command}"
        try {
            exec {
                ExecSpec execSpec ->
                    executable 'bash'
                    args '-c', command
            }
            println "deleteArtifacts success~"
        } catch (Exception e) {
            e.printStackTrace()
        }
    }
}

configure(artifactProjects) { project ->
    apply from: "$rootDir/gradle/publications.gradle"
}

def static buildTime(String time) {
    def date = new Date()
    def formattedDate = date.format(time)
    return formattedDate
}

def static getGitVersion() {
    return 'git rev-parse --short HEAD'.execute().text.trim()
}

def static getAuthor() {
    return 'git config user.name'.execute().text.trim()
}

def static getAuthorEmail() {
    return 'git config user.email'.execute().text.trim()
}

def static getVersionMap() {
    Map<String, String> versionMap = new HashMap<>();
    String filePath = "cpe-starter%2Fcpe-starter-parent%2Fbuild-global-jar.txt";
    URL url = new URL("http://git.cairenhui.com/api/v4/projects/1538/repository/files/" + filePath + "/raw?ref=development");
    HttpURLConnection connection = (HttpURLConnection) url.openConnection();
    connection.setRequestMethod("GET");
    connection.setRequestProperty("Private-Token", "k2dEGUVG32JYhcM5d82u");
    int responseCode = connection.getResponseCode();
    if (responseCode != HttpURLConnection.HTTP_OK) {
        return
    }
    BufferedReader reader = null;
    try {
        reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
        String line;
        while ((line = reader.readLine()) != null) {
            if (line == '' || line.trim() == '') {
                continue;
            }
            line = line.trim();
            if (line.contains("//")) {
                continue
            }
            if (line.contains('#')) {
                String[] str = line.split('#');
                if (str.length != 2) {
                    continue;
                }
                versionMap.put(str[0], str[1]);
            }
            if (line.contains(':')) {
                String[] str = line.split(':');
                if (str.length != 3) {
                    continue;
                }
                versionMap.put(str[0] + ':' + str[1], str[2]);
            }
        }
    } catch (IOException e) {
        e.printStackTrace();
    } finally {
        if (reader != null) {
            try {
                reader.close();
            } catch (IOException e1) {
                e1.printStackTrace();
            }
        }
    }
    println 'versionMap大小：' + versionMap.size()
    return versionMap
}

// 获取不同分支机构的不同jar包配置
static Map<String, List<String>> getBranchVersionMap() {
    // gradle build -DsecurityAlias=htzq
    def securityAlias = System.getProperty("securityAlias");
    println("当前券商：" + securityAlias)
    Map<String, List<String>> versionMap = new HashMap<>();
    List<String> excludes = new ArrayList<>()
    List<String> includes = new ArrayList<>()
    versionMap.put("excludes", excludes)
    versionMap.put("includes", includes)
    if (securityAlias == null || securityAlias == '') return versionMap
    String filePath = "cpe-starter%2Fcpe-starter-parent%2Fbranch-jar-config.txt";
    URL url = new URL("http://git.cairenhui.com/api/v4/projects/1538/repository/files/" + filePath + "/raw?ref=development");
    HttpURLConnection connection = (HttpURLConnection) url.openConnection();
    connection.setRequestMethod("GET");
    connection.setRequestProperty("Private-Token", "3kqPw33mSycXW6z9jvPA");
    int responseCode = connection.getResponseCode();
    if (responseCode != HttpURLConnection.HTTP_OK) return versionMap
    BufferedReader reader = null;
    try {
        reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
        String line;
        while ((line = reader.readLine()) != null) {
            if (line == '' || line.trim() == '') continue
            line = line.trim();
            if (line.contains("//")) continue
            if (line.contains('=')) {
                String[] str = line.split('=');
                if (str.length != 2) continue
                if (str[0].trim() == securityAlias + "." + "exclude") excludes.add(str[1].trim())
                if (str[0].trim() == securityAlias + "." + "include") includes.add(str[1].trim())
            }
        }
    } finally {
        if (reader != null) reader.close()
    }
    return versionMap
}
