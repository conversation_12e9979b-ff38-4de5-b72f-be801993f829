plugins {
    id 'war'
    id 'org.springframework.boot'
}

version ''

archivesBaseName = 'cpe-products-monitor-backend'

dependencies {

    api(project(":cpe-products-monitor-common"))

    api('com.cairh:cpe-trace')
    api('com.cairh:cpe-auth')
    api('com.cairh:cpe-context')
    api('com.cairh:cpe-mem')
    api('com.cairh:cpe-db')
    api('com.cairh:cpe-config')

    api('com.cairh:cpe-esb-basedata-server-api')
    api('org.apache.dubbo:dubbo')
    api('org.apache.dubbo:dubbo-spring-boot-starter')

    api('io.micrometer:micrometer-registry-prometheus')
    api('mysql:mysql-connector-java')
    api('com.oracle:ojdbc6')
    api('com.oceanbase:oceanbase-client:2.4.0')
    api('dom4j:dom4j')

    api('com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery')
    api('com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config')
    api('org.springframework.cloud:spring-cloud-starter-consul-discovery')
    api('org.springframework.cloud:spring-cloud-starter-consul-config')

    api('com.alibaba.spring:spring-context-support')

    api('com.cairh:cpe-common-backend')

    api('com.gtja:tongweb-embed')
    api('com.gtja:tongweb-spring-boot-starter')

    implementation 'io.jsonwebtoken:jjwt'
}

test {
    useJUnitPlatform()
}