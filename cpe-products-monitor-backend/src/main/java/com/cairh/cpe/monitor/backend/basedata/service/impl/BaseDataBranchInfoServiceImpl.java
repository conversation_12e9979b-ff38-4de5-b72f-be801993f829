package com.cairh.cpe.monitor.backend.basedata.service.impl;

import com.cairh.cpe.common.constant.Constant;
import com.cairh.cpe.esb.base.rpc.IVBaseAllBranchDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseAllBranchQryRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseAllBranchQryResponse;
import com.cairh.cpe.monitor.backend.basedata.service.IBaseDataService;
import com.cairh.cpe.monitor.backend.constant.BaseDataServiceConstant;
import com.cairh.cpe.monitor.backend.form.commom.BaseDataVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service(BaseDataServiceConstant.BASE_DATA_SERVER_1001)
public class BaseDataBranchInfoServiceImpl implements IBaseDataService {

    @DubboReference(lazy = true, check = false)
    IVBaseAllBranchDubboService allBranchDubboService;

    @Override
    public Object queryData() {
        VBaseAllBranchQryRequest vBaseAllBranchQryRequest = new VBaseAllBranchQryRequest();
        vBaseAllBranchQryRequest.setStatus(Constant.COMMON_VALID_STATUS);

        log.info("dubbo接口 baseDataBranchInfoService请求参数:{}", vBaseAllBranchQryRequest);
        List<VBaseAllBranchQryResponse> vBaseAllBranchQryResponses
                = allBranchDubboService.baseDataQryAllBranch(vBaseAllBranchQryRequest);
        log.info("dubbo接口 baseDataBranchInfoService响应参数:{}", vBaseAllBranchQryResponses);

        return convert(vBaseAllBranchQryResponses);
    }

    private List<BaseDataVo> convert(List<VBaseAllBranchQryResponse> responses) {
        return responses.stream().map(v -> {
            BaseDataVo baseDataVo = new BaseDataVo();
            baseDataVo.setKey(v.getBranch_name());
            baseDataVo.setValue(v.getBranch_no());
            return baseDataVo;
        }).collect(Collectors.toList());
    }
}
