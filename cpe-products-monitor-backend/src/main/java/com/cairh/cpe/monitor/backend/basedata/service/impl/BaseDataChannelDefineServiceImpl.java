package com.cairh.cpe.monitor.backend.basedata.service.impl;

import com.cairh.cpe.monitor.backend.basedata.service.IBaseDataService;
import com.cairh.cpe.monitor.backend.basedata.service.IChannelDefineService;
import com.cairh.cpe.monitor.backend.constant.BaseDataServiceConstant;
import com.cairh.cpe.monitor.backend.entity.ChannelDefine;
import com.cairh.cpe.monitor.backend.form.commom.BaseDataVo;
import com.cairh.cpe.monitor.backend.mapper.ChannelDefineMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service(BaseDataServiceConstant.BASE_DATA_SERVER_1002)
public class BaseDataChannelDefineServiceImpl implements IBaseDataService {

    @Autowired
    private IChannelDefineService channelDefineService;

    @Autowired
    private ChannelDefineMapper channelDefineMapper;

    @Override
    public Object queryData() {
        /*LambdaQueryWrapper<ChannelDefine> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ChannelDefine::getStatus, Constant.COMMON_VALID_STATUS);
        wrapper.select(ChannelDefine::getChannel_code, ChannelDefine::getChannel_name);
        List<ChannelDefine> channelDefineList = channelDefineService.list(wrapper);*/
        List<ChannelDefine> channelDefineList = channelDefineMapper.getChannelDefineList();
        return convert(channelDefineList);
    }

    private List<BaseDataVo> convert(List<ChannelDefine> channelDefineList) {
        log.info("channelDefineList: {}", channelDefineList);
        List<BaseDataVo> voList = channelDefineList.stream().map(v -> {
            BaseDataVo baseDataVo = new BaseDataVo();
            baseDataVo.setKey(v.getChannel_name());
            baseDataVo.setValue(v.getChannel_code());
            return baseDataVo;
        }).collect(Collectors.toList());
        return voList;
    }
}
