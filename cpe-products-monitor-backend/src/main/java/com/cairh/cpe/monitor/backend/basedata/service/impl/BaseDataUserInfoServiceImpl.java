package com.cairh.cpe.monitor.backend.basedata.service.impl;

import com.cairh.cpe.esb.base.rpc.IVBaseUserInfoDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseQryUserInfosRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseQryUserInfosResponse;
import com.cairh.cpe.monitor.backend.basedata.service.IBaseDataService;
import com.cairh.cpe.monitor.backend.constant.BaseDataServiceConstant;
import com.cairh.cpe.monitor.backend.form.commom.BaseDataVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service(BaseDataServiceConstant.BASE_DATA_SERVER_1003)
public class BaseDataUserInfoServiceImpl implements IBaseDataService {

    @DubboReference(lazy = true, check = false)
    IVBaseUserInfoDubboService baseUserInfoDubboService;

    @Override
    public Object queryData() {
        VBaseQryUserInfosRequest vBaseQryUserInfosRequest = new VBaseQryUserInfosRequest();
        log.info("dubbo接口 baseDataUserInfoService请求参数:{}", vBaseQryUserInfosRequest);
        List<VBaseQryUserInfosResponse> vBaseQryUserInfosResponses
                = baseUserInfoDubboService.baseUserQryUserInfoList(vBaseQryUserInfosRequest);
        log.info("dubbo接口 baseDataUserInfoServicex响应参数:{}", vBaseQryUserInfosResponses);

        return convert(vBaseQryUserInfosResponses);
    }

    private List<BaseDataVo> convert(List<VBaseQryUserInfosResponse> responses) {
        return responses.stream().map(v -> {
            BaseDataVo baseDataVo = new BaseDataVo();
            baseDataVo.setKey(v.getUser_name());
            baseDataVo.setValue(v.getStaff_no());
            return baseDataVo;
        }).collect(Collectors.toList());
    }
}
