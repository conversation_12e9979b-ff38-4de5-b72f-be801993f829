package com.cairh.cpe.monitor.backend.basedata.service.impl;

import com.cairh.cpe.esb.base.rpc.IVBaseDictDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseDictQryRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseDictQryResponse;
import com.cairh.cpe.monitor.backend.basedata.service.IBaseDataService;
import com.cairh.cpe.monitor.backend.constant.BaseDataServiceConstant;
import com.cairh.cpe.monitor.backend.form.commom.BaseDataVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service(BaseDataServiceConstant.BASE_DATA_SERVER_1009)
public class BaseDataWtAppIdServiceImpl implements IBaseDataService {

    @DubboReference(check = false)
    IVBaseDictDubboService dictDubboService;

    @Override
    public Object queryData() {
        VBaseDictQryRequest baseDictQryRequest = new VBaseDictQryRequest();
        baseDictQryRequest.setDict_code("wt_app_id");
        log.info("dubbo接口 baseDataOpenTypeService请求参数:{}", baseDictQryRequest);
        List<VBaseDictQryResponse> vBaseDictQryResponses = dictDubboService.baseDataQryDict(baseDictQryRequest);
        log.info("dubbo接口 baseDataOpenTypeService响应参数:{}", vBaseDictQryResponses);

        return convert(vBaseDictQryResponses);
    }

    private List<BaseDataVo> convert(List<VBaseDictQryResponse> responses) {
        return responses.stream().map(v -> {
            BaseDataVo baseDataVo = new BaseDataVo();
            baseDataVo.setKey(v.getSub_name());
            baseDataVo.setValue(v.getSub_code());
            return baseDataVo;
        }).collect(Collectors.toList());
    }
}
