package com.cairh.cpe.monitor.backend.config;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.wrapper.MapWrapper;
import org.bouncycastle.util.Strings;

import java.util.Map;

public class MybatisMapKeyLowerWrapper extends MapWrapper {

    public MybatisMapKeyLowerWrapper(MetaObject metaObject, Map<String, Object> map) {
        super(metaObject, map);
    }

    @Override
    public String findProperty(String name, boolean useCamelCaseMapping) {
        //Map 映射 key值都转为小写
        if (StringUtils.isNotBlank(name)) {
            return Strings.toLowerCase(name);
        }
        return name;
    }
}
