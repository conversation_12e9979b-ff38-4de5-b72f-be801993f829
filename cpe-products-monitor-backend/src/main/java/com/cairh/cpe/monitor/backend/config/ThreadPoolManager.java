package com.cairh.cpe.monitor.backend.config;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 *
 * <AUTHOR>
 * @create 2022-11-30
 *
 */
public class ThreadPoolManager {

    @SuppressWarnings({"rawtypes", "unchecked"})
    private final ThreadPoolExecutor threadPool = new ThreadPoolExecutor(50, 400, 60L, TimeUnit.SECONDS, new ArrayBlockingQueue(200), new ThreadFactory() {

        private final AtomicInteger threadNumber = new AtomicInteger(1);

        @Override
        public Thread newThread(Runnable runnable) {
            return new Thread(runnable, "monitor-executor-service-" + threadNumber.getAndIncrement());
        }
    });

    private static final ThreadPoolManager threadPoolEtlManager = new ThreadPoolManager();

    public static ThreadPoolManager getInstance() {
        return threadPoolEtlManager;
    }

    /**
     * 执行线程方法
     */
    public void execute(Runnable task) {
        this.threadPool.execute(task);
    }

    /**
     * 关闭所有的线程
     *
     */
    public void shutdown() {
        try {
            this.threadPool.shutdown();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 关闭所有的线程
     */
    protected void finalize() throws Throwable {
        try {
            this.threadPool.shutdownNow();
        } catch (Exception e) {
            e.printStackTrace();
        }
        super.finalize();
    }

}
