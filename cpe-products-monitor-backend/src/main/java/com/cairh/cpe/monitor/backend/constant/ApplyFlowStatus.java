package com.cairh.cpe.monitor.backend.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Optional;

/**
 * 0 - kafka 接收通过
 * 1-  绑定策略-手动激活 并生成计划表
 * 2-  生成回访任务
 * 3-  推送全渠道后 处理中
 * 4-  处置状态 (全渠道返回-总部管理岗核实)后- 待核实
 * 5-  外包管理岗处理后 再核实
 * 6-  全渠道返回 回访通过   终态
 * 7-  全渠道返回 回访不通过 -存在不合规答案  终态
 * 8-  全渠道返回 回访不通过 -接通但不配合回访  终态
 * 9-  总部管理岗 手动结束     终态
 * 10- 全渠道返回 拨打超限     终态
 * 申请状态
 * <AUTHOR>
 */
@Getter
public enum ApplyFlowStatus {

    APPLY_ACCEPT("0", "已接收"),
    APPLY_PLAN_PROCESSING("1", "已计划"),
    APPLY_PENDING_PROCESSING("2", "未下发"),

    APPLY_PUSH_OMNICHANNEL("3", "已下发未处理"),
    APPLY_IN_PROCESSING("4", "已下发处理中"),
    APPLY_PENDING_VERIFY("5", "待核实"),
    APPLY_AGAIN_VERIFY("6", "再核实"),
    APPLY_PASS_FINISH("7", "回访通过"),
    APPLY_NON_COMPLIANCE("8", "回访完成不合规"),
    APPLY_REACHED_UNCOOPERATIVE("9", "回访结束(拒绝回访)"),
    APPLY_MANUALLY_END("10", "手动结束"),
    APPLY_CALL_EXCEEDED("11", "策略执行完成"),
    APPLY_MANUAL_RECOVERY("12", "手动回收");

    private final String code;

    private final String desc;

    ApplyFlowStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(String code) {
        if (null == code) {
            return StringUtils.EMPTY;
        }
        Optional<ApplyFlowStatus> optional =
            Arrays.stream(ApplyFlowStatus.values()).filter(item -> item.getCode().equals(code)).findFirst();
        if (optional.isPresent()) {
            return optional.get().getDesc();
        }
        return StringUtils.EMPTY;
    }

    public static ApplyFlowStatus getApplyFlowStatusByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        for (ApplyFlowStatus applyFlowStatus : values()) {
            if (StringUtils.equals(applyFlowStatus.code, code)) {
                return applyFlowStatus;
            }
        }
        return null;
    }
}
