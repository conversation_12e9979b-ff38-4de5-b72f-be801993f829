package com.cairh.cpe.monitor.backend.constant;

import com.cairh.cpe.monitor.backend.form.resp.AiAuditRuleTopResp;
import com.cairh.cpe.monitor.backend.form.resp.RejectReasonTopResp;
import lombok.Getter;



@Getter
public enum MonitorDataEnum {

    REJECT_REASON_TOP5(QueryStatisticsDataConstant.REDIS_REJECT_REASON_KEY, "驳回原因TOP5", RejectReasonTopResp.class),
    AI_AUDIT_RULE_TOP5(QueryStatisticsDataConstant.AUDIT_RULE_TOP_KEY, "智能审核命中规则", AiAuditRuleTopResp .class)
    ;


    final String funcId;
    final String name;
    final Class<?> clazz;

    MonitorDataEnum(String funcId, String name, Class<?> clazz) {
        this.funcId = funcId;
        this.name = name;
        this.clazz = clazz;
    }

}
