package com.cairh.cpe.monitor.backend.constant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.cairh.cpe.monitor.backend.form.resp.PageResp;
import com.cairh.cpe.monitor.backend.service.NewApiResultDataConvertService;
import com.cairh.cpe.monitor.backend.service.impl.DefaultResultDataConvertServiceImpl;
import com.cairh.cpe.monitor.backend.service.impl.FollowUpTaskResultDataConvertServiceImpl;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 新的api结果数据转换的枚举
 */
@AllArgsConstructor
@Getter
@Slf4j
public enum NewApiResultDataConvertEnum {

    DEFAULT("默认的结果转换处理逻辑", "默认的结果转换处理逻辑", DefaultResultDataConvertServiceImpl.class),

    MONITOR10004("MONITOR10004", "回访流水报表", FollowUpTaskResultDataConvertServiceImpl.class),


    ;
    final String funcId;
    final String name;
    final Class<? extends NewApiResultDataConvertService> convertServiceClass;

    /**
     * 根据功能id获取枚举包含默认枚举
     *
     * @param functionId
     * @return
     */
    public static List<NewApiResultDataConvertEnum> getWithDefaultByFuncationId(String functionId) {
        return Arrays.stream(values())
                .filter(value -> Objects.equals(value.getFuncId(), functionId) || Objects.equals(value, DEFAULT)).collect(Collectors.toList());
    }

    /**
     * 数据转换
     * @param finalHaveSum 有合并列
     * @param pageResp 返回数据
     */
    public void handleData(Boolean finalHaveSum, PageResp<?> pageResp) {
        if (Objects.isNull(pageResp) || CollUtil.isEmpty(pageResp.getRecords())) {
            return;
        }
        try {
            log.info("result data handle start funcId = {} name = {}", this.getFuncId(), this.getName());
            Class<? extends NewApiResultDataConvertService> convertServiceClazz = this.getConvertServiceClass();
            if (convertServiceClazz == null) {
                log.info("convertServiceClazz is null handle end funcId = {} name = {}", this.getFuncId(), this.getName());
                return;
            }
            SpringUtil.getBean(convertServiceClazz).handle(finalHaveSum,pageResp);
            log.info("result data handle end  funcId = {} name = {}", this.getFuncId(), this.getName());
        } catch (Exception e) {
            log.error("data convert error", e);
        }
    }

}
