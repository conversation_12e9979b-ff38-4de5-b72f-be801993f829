package com.cairh.cpe.monitor.backend.constant;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 在线状态枚举
 */
@AllArgsConstructor
@Getter
public enum OnlineStatusEnum {

    ONLINE_RECORD_LOGIN("1", "签入"),
    ONLINE_RECORD_LOGOUT("2", "签出");


    final String value;
    final String name;

    public static String getNameByValue(String value) {
        if (StrUtil.isBlank(value)) {
            return "";
        }
        for (OnlineStatusEnum onlineStatusEnum : OnlineStatusEnum.values()) {
            if (onlineStatusEnum.getValue().equals(value)) {
                return onlineStatusEnum.getName();
            }
        }
        return "";
    }
}
