package com.cairh.cpe.monitor.backend.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Optional;

/**
 * 任务流程状态
 *
 * <AUTHOR>
 */
@Getter
public enum TaskFlowStatus {

    TASK_PENDING_PROCESSING("10", "已生成未下发"),
    TASK_DELIVERED("15", "已下发未处理"),
    TASK_IN_PROCESSING("20", "已下发处理中"),
    TASK_PENDING_VERIFIED("30", "待核实"),
    TASK_CONFIRM_VERIFIED("40", "再核实"),
    TASK_FINISHED("50", "已完成"),
    TASK_CANCEL("60", "已关闭");

    private final String code;

    private final String desc;

    TaskFlowStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(String code) {
        if (null == code) {
            return StringUtils.EMPTY;
        }
        Optional<TaskFlowStatus> optional =
                Arrays.stream(TaskFlowStatus.values()).filter(item -> item.getCode().equals(code)).findFirst();
        if (optional.isPresent()) {
            return optional.get().getDesc();
        }
        return StringUtils.EMPTY;
    }
}
