package com.cairh.cpe.monitor.backend.constant;

import cn.hutool.core.collection.CollUtil;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseAllBranchQryResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

import java.util.List;
import java.util.Map;

/**
 * 翻译字段的枚举
 */
@AllArgsConstructor
@Getter
@Slf4j
public enum TranslationEnum {
    branch_no("branch_no", "营业部编号") {
        @Override
        public void translation(Map<String, Object> result, List<String> excludes, Map<String, VBaseAllBranchQryResponse> responseMap) {
            if (CollUtil.isNotEmpty(excludes) && excludes.contains(this.getFieldName())) {
                return;
            }

            if (ObjectUtils.isNotEmpty(responseMap.get(result.get("branch_no")))) {
                result.put("branch_name", (responseMap.get(result.get("branch_no")).getBranch_name()));
            }
            if ("合计".equals(result.get("branch_no"))) {
                result.put("branch_name", "合计");
            }

        }
    },
    up_branch_no("up_branch_no", "分公司编号") {
        @Override
        public void translation(Map<String, Object> result, List<String> excludes, Map<String, VBaseAllBranchQryResponse> responseMap) {
            if (CollUtil.isNotEmpty(excludes) && excludes.contains(this.getFieldName())) {
                return;
            }

            if (ObjectUtils.isNotEmpty(responseMap.get(result.get("up_branch_no")))) {
                result.put("up_branch_name", (responseMap.get(result.get("up_branch_no")).getBranch_name()));
            }
            if ("合计".equals(result.get("up_branch_no"))) {
                result.put("up_branch_name", "合计");
            }

        }
    },
    rate("rate", "百分比") {
        @Override
        public void translation(Map<String, Object> result, List<String> excludes, Map<String, VBaseAllBranchQryResponse> responseMap) {
            if (CollUtil.isNotEmpty(excludes) && excludes.contains(this.getFieldName())) {
                return;
            }

            result.forEach((key, value) -> {
                // 如果 key 以 "rate" 或 "Rate" 结尾，并且 value 不是 null，则拼接 "%"
                if (key != null && (key.endsWith("rate") || key.endsWith("Rate"))) {
                    result.put(key, value.toString() + "%");
                }
            });
        }
    },
    ;
    //字段名称
    final String fieldName;
    final String desc;

    public abstract void translation(Map<String, Object> result, List<String> excludes, Map<String, VBaseAllBranchQryResponse> responseMap);


    public static void handle(Map<String, Object> result, List<String> excludes, Map<String, VBaseAllBranchQryResponse> responseMap) {
        for (TranslationEnum value : values()) {
            if (CollUtil.isNotEmpty(excludes) && excludes.contains(value.getFieldName())) {
                continue;
            }
            value.translation(result, excludes, responseMap);
        }
    }
}
