package com.cairh.cpe.monitor.backend.controller;

import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.monitor.backend.form.req.BasicStatsRequest;
import com.cairh.cpe.monitor.backend.service.IBaseDataRouteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 *
 * 基础数据路由控制器
 * <AUTHOR>
 * @create 2023-06-16
 *
 */
@Slf4j
@RestController
//@RequestMapping("/monitor")
public class BaseDataController {

    @Autowired
    private IBaseDataRouteService baseDataRouteService;

    /**
     * 基础数据查询
     */
//    @PostMapping("/baseDate")
    @RequestMapping(value = {"/monitor/baseDate", "/authless/monitor/baseDate"}, method = RequestMethod.POST)
    public Result<?> queryByList(@AuthenticationPrincipal BaseUser baseUser
            , @Valid @RequestBody BasicStatsRequest basicStatsRequest) {
        return Result.success(baseDataRouteService.queryData(basicStatsRequest.getFunction_id()));
    }

}
