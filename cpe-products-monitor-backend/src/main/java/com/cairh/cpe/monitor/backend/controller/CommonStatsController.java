package com.cairh.cpe.monitor.backend.controller;

import com.cairh.cpe.common.util.SysParamService;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.monitor.backend.form.req.BasicStatsRequest;
import com.cairh.cpe.monitor.backend.form.req.MonitorDataReq;
import com.cairh.cpe.monitor.backend.form.resp.MonthlyTeamReviewRankingResp;
import com.cairh.cpe.monitor.backend.service.ICommonStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
//@RequestMapping("/monitor")
public class CommonStatsController {


    private static final int DEFAULT_PAGE_NUM = 1;

    private static final int DEFAULT_PAGE_SIZE = 10;

    @Autowired
    private ICommonStatsService commonStatsService;

    @Autowired
    private SysParamService sysParamService;

    //    @RequestMapping(value = "/api", method = RequestMethod.POST)
    @RequestMapping(value = {"/monitor/api", "/authless/monitor/api"}, method = RequestMethod.POST)
    public Result<?> commonStatsEntrance(@AuthenticationPrincipal BaseUser baseUser, @Valid @RequestBody BasicStatsRequest basicStatsRequest) {
        return Result.success(commonStatsService.commonStatsEntrance(baseUser, basicStatsRequest));
    }

    @RequestMapping(value = {"/monitor/newApi", "/authless/monitor/newApi"}, method = RequestMethod.POST)
    public Result<?> newCommonStatsEntrance(@AuthenticationPrincipal BaseUser baseUser, @Valid @RequestBody BasicStatsRequest basicStatsRequest) {
        paramCheck(basicStatsRequest);
        return Result.success(commonStatsService.newCommonStatsEntrance(baseUser, basicStatsRequest));
    }

    /**
     * 新增接口，支持字段翻译
     *
     * @param baseUser
     * @param basicStatsRequest
     * @return
     */
    @RequestMapping(value = {"/monitor/translation/api", "/authless/monitor/translation/api"}, method = RequestMethod.POST)
    public Result<?> commonStatsEntranceWithFieldTranslation(@AuthenticationPrincipal BaseUser baseUser, @Valid @RequestBody BasicStatsRequest basicStatsRequest) {
        paramCheck(basicStatsRequest);
        return Result.success(commonStatsService.commonStatsEntranceWithFieldTranslation(baseUser, basicStatsRequest));
    }

    /**
     * 本月小组人均审核量排名
     * 映射-> MONITOR2028
     */
    @RequestMapping(value = {"/getMonthlyTeamReviewRanking", "/authless/getMonthlyTeamReviewRanking"}, method = {RequestMethod.POST, RequestMethod.GET})
    public Result<List<MonthlyTeamReviewRankingResp>> getMonthlyTeamReviewRanking() {
        return Result.success(commonStatsService.getMonthlyTeamReviewRanking());
    }

    /**
     * 运营数据大屏查询入口
     */
    @PostMapping(value = {"/monitor/monitorData", "/authless/monitor/monitorData"})
    public Result<List<?>> getMonitorData(@Valid @RequestBody MonitorDataReq req) {
        return Result.success(commonStatsService.getMonitorData(req));
    }

    /**
     * 分页参数检查
     */
    private void paramCheck(BasicStatsRequest basicStatsRequest) {
        Integer pageNum = basicStatsRequest.getPage_num();
        if (pageNum == null || pageNum <= 0) {
            basicStatsRequest.setPage_num(DEFAULT_PAGE_NUM);
        }
        Integer pageSize = basicStatsRequest.getPage_size();
        if (pageSize == null || pageSize <= 0) {
            String uploadMaxSize = sysParamService.getUploadMaxSize();
            basicStatsRequest.setPage_size(Integer.valueOf(uploadMaxSize));
        }
    }
}
