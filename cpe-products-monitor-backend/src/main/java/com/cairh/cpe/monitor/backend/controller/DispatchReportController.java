package com.cairh.cpe.monitor.backend.controller;

import com.cairh.cpe.common.constant.MonitorErrorCode;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.monitor.backend.form.req.BasicStatsRequest;
import com.cairh.cpe.monitor.backend.form.resp.OperatorOnlineOverviewResp;
import com.cairh.cpe.monitor.backend.form.resp.TodayFollowUpTaskExecutionDataResp;
import com.cairh.cpe.monitor.backend.form.resp.TodayOperatorCapacityDistributionResp;
import com.cairh.cpe.monitor.backend.form.resp.TodayOperatorExecuteStatusResp;
import com.cairh.cpe.monitor.backend.service.IAdsReportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 基础数据路由控制器
 *
 * <AUTHOR>
 * @create 2023-06-16
 */
@Slf4j
@RestController
//@RequestMapping("/monitor")
public class DispatchReportController {

    @Autowired
    private IAdsReportService adsReportService;

    /**
     * 见证操作员实时状态汇总
     */
//    @PostMapping("/ADS9001")
    @RequestMapping(value = {"/monitor/ADS9001", "/authless/monitor/ADS9001"}, method = RequestMethod.POST)
    public Result<?> queryADS9001(@AuthenticationPrincipal BaseUser baseUser, @RequestBody BasicStatsRequest basicStatsRequest) {
        return Result.success(adsReportService.queryADS9001());
    }

    /**
     * 查询优先级队列概况
     */
//    @PostMapping("/ADS9002")
    @RequestMapping(value = {"/monitor/ADS9002", "/authless/monitor/ADS9002"}, method = RequestMethod.POST)
    public Result<?> queryADS9002(@AuthenticationPrincipal BaseUser baseUser, @RequestBody BasicStatsRequest basicStatsRequest) {
        return Result.success(adsReportService.queryADS9002());
    }

    /**
     * 查询派单概况
     */
//    @PostMapping("/ADS9003")
    @RequestMapping(value = {"/monitor/ADS9003", "/authless/monitor/ADS9003"}, method = RequestMethod.POST)
    public Result<?> queryADS9003(@AuthenticationPrincipal BaseUser baseUser, @RequestBody BasicStatsRequest basicStatsRequest) {
        return Result.success(adsReportService.queryADS9003());
    }

    /**
     * 重新统计见证系统操作员某一日的在线时长
     */
//    @PostMapping("/ADS9004")
    @RequestMapping(value = {"/monitor/ADS9004", "/authless/monitor/ADS9004"}, method = RequestMethod.POST)
    public Result<?> queryADS9004(@AuthenticationPrincipal BaseUser baseUser, @RequestBody BasicStatsRequest basicStatsRequest) {
        if (StringUtils.isBlank(basicStatsRequest.getStart_time())) {
            throw new BizException(MonitorErrorCode.PARAM_VALUE_IS_NULL, "入参start_time为空!");
        }
        if (StringUtils.isBlank(basicStatsRequest.getEnd_time())) {
            throw new BizException(MonitorErrorCode.PARAM_VALUE_IS_NULL, "入参end_time为空!");
        }
        return Result.success(adsReportService.queryADS9004(basicStatsRequest.getStart_time(), basicStatsRequest.getEnd_time()));
    }

    /**
     * 查询回访派单概况
     */
    @RequestMapping(value = {"/monitor/ADS9005", "/authless/monitor/ADS9005"}, method = RequestMethod.POST)
    public Result<?> queryADS9005(@AuthenticationPrincipal BaseUser baseUser, @RequestBody BasicStatsRequest basicStatsRequest) {
        return Result.success(adsReportService.queryADS9005());
    }

    /**
     * 坐席在线概况
     */
    @RequestMapping(value = {"/monitor/ADS9006", "/authless/monitor/ADS9006"}, method = RequestMethod.POST)
    public Result<OperatorOnlineOverviewResp> queryADS9006(@AuthenticationPrincipal BaseUser baseUser, @RequestBody BasicStatsRequest basicStatsRequest) {
        return Result.success(adsReportService.queryADS9006());
    }

    /**
     * 今日坐席产能分布
     */
    @RequestMapping(value = {"/monitor/ADS9007", "/authless/monitor/ADS9007"}, method = RequestMethod.POST)
    public Result<TodayOperatorCapacityDistributionResp> queryADS9007(@AuthenticationPrincipal BaseUser baseUser, @RequestBody BasicStatsRequest basicStatsRequest) {
        return Result.success(adsReportService.queryADS9007());
    }

    /**
     * 今日回访任务执行数据
     */
    @RequestMapping(value = {"/monitor/ADS9008", "/authless/monitor/ADS9008"}, method = RequestMethod.POST)
    public Result<TodayFollowUpTaskExecutionDataResp> queryADS9008(@AuthenticationPrincipal BaseUser baseUser, @RequestBody BasicStatsRequest basicStatsRequest) {
        return Result.success(adsReportService.queryADS9008());
    }

    /**
     * 坐席执行情况
     */
    @RequestMapping(value = {"/monitor/ADS9009", "/authless/monitor/ADS9009"}, method = RequestMethod.POST)
    public Result<List<TodayOperatorExecuteStatusResp>> queryADS9009(@AuthenticationPrincipal BaseUser baseUser, @RequestBody BasicStatsRequest basicStatsRequest) {
        return Result.success(adsReportService.queryADS9009());
    }

}
