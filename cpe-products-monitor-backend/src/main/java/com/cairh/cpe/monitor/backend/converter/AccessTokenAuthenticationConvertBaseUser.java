package com.cairh.cpe.monitor.backend.converter;

import com.alibaba.fastjson.JSON;
import com.cairh.cpe.auth.authentication.AccessTokenAuthentication;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.convert.Converter;
import org.springframework.stereotype.Component;

/**
 * {@link AccessTokenAuthentication} -> {@link BaseUser}
 *
 * <AUTHOR>
 */
@Component
public class AccessTokenAuthenticationConvertBaseUser
        implements Converter<AccessTokenAuthentication, BaseUser> {

    @Override
    public BaseUser convert(AccessTokenAuthentication source, BaseUser target) {
        Object principal = source.getPrincipal();

        return JSON.parseObject(JSON.toJSONString(principal), target.getClass());
    }
}
