package com.cairh.cpe.monitor.backend.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 渠道定义表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("crh_wskh.ChannelDefine")
public class ChannelDefine implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 渠道代码
     */
    @TableId("channel_code")
    private String channel_code;

    /**
     * 渠道名称
     */
    @TableField("channel_name")
    private String channel_name;

    /**
     * 渠道短连接
     */
    @TableField("short_url")
    private String short_url;

    /**
     * 渠道类型
     */
    @TableField("channel_type")
    private String channel_type;

    /**
     * 渠道二维码logo
     */
    @TableField("qrcode_logo")
    private String qrcode_logo;
    /**
     * 渠道二维码链接
     */
    @TableField("qrcode_url")
    private String qrcode_url;

    /**
     * 机构标志
     */
    @TableField("organ_flag")
    private String organ_flag;

    /**
     * 父渠道code
     */
    @TableField("parent_code")
    private String parent_code;

    /**
     * 渠道所属营业部
     */
    @TableField("branch_no")
    private String branch_no;

    /**
     * 经纪人编号
     */
    @TableField("broker_no")
    private String broker_no;

    /**
     * 允许银行代码
     */
    @TableField("en_bank_no")
    private String en_bank_no;

    /**
     * 允许接入方式
     */
    @TableField("en_app_id")
    private String en_app_id;

    /**
     * 默认营业部编号
     */
    @TableField("default_branch")
    private String default_branch;

    /**
     * 默认银行编号
     */
    @TableField("default_bank")
    private String default_bank;

    /**
     * 客户模板
     */
    @TableField("model_id")
    private String model_id;

    /**
     * 有效期限
     */
    @TableField("invalid_datetime")
    private Date invalid_datetime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String create_by;

    /**
     * 创建日期时间
     */
    @TableField("create_datetime")
    private Date create_datetime;

    /**
     * 修改人
     */
    @TableField("update_by")
    private String update_by;

    /**
     * 更新日期时间
     */
    @TableField("update_datetime")
    private Date update_datetime;

    /**
     * 状态  8 正常 9失效
     */
    @TableField("status")
    private String status;
}
