package com.cairh.cpe.monitor.backend.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 渠道定义表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DispatchTaskOverview implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 未入队数量
     */
    private String noqueue_sum;

    /**
     * 入队数量
     */
    private String queue_sum;

    /**
     * 未处理数量
     */
    private String nodeal_sum;

    /**
     * 处理中数量
     */
    private String dealing_sum;
}
