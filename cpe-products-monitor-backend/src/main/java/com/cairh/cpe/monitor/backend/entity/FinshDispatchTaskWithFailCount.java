package com.cairh.cpe.monitor.backend.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 渠道定义表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class FinshDispatchTaskWithFailCount implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分配失败次数
     */
    private Integer dispatch_fail_count;

    /**
     * 失败次数对应任务数
     */
    private Integer dispatch_fail_count_task_count;
}
