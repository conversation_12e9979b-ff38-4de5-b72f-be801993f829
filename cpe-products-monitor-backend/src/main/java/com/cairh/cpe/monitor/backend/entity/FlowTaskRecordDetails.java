package com.cairh.cpe.monitor.backend.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
public class FlowTaskRecordDetails implements Serializable {

    /**
     * 操作员编号
     */
    private String operator_no;

    /**
     * 操作员名称
     */
    private String operator_name;

    /**
     * 任务处理数量
     */
    private Integer task_deal_count;

}
