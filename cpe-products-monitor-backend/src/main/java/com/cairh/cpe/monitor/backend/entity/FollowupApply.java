package com.cairh.cpe.monitor.backend.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 客户申请表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "FOLLOWUPAPPLY", schema = "crh_fus")
public class FollowupApply implements Serializable {

    /**
     * 主键ID
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 开户任务ID
     */
    @TableField("portal_biz_id")
    private String portal_biz_id;

    /**
     * 回访类型
     */
    @TableField("hf_type")
    private String hf_type;

    /**
     * 任务类型
     */
    @TableField("busin_type")
    private String busin_type;

    /**
     * 客户名称
     */
    @TableField("client_name")
    private String client_name;

    /**
     * 客户代码
     */
    @TableField("client_id")
    private String client_id;

    /**
     * 手机号码
     */
    @TableField("mobile_tel")
    private String mobile_tel;

    /**
     * 开户创建时间
     */
    @TableField("portal_create_time")
    private Date portal_create_time;

    /**
     * 性别
     */
    @TableField("sex")
    private String sex;

    /**
     * 分公司
     */
    @TableField("up_branch_no")
    private String up_branch_no;

    /**
     * 营业部
     */
    @TableField("branch_no")
    private String branch_no;

    /**
     * 首次处理时间
     */
    @TableField("first_deal_time")
    private Date first_deal_time;

    /**
     * 首次推送时间
     */
    @TableField("first_push_time ")
    private Date first_push_time;

    /**
     * 完成时间
     */
    @TableField("finish_datetime")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finish_datetime;

    /**
     * 最新的处理人编号
     */
    @TableField("last_operator_no")
    private String last_operator_no;

    /**
     * 最新的处理人名称
     */
    @TableField("last_operator_name")
    private String last_operator_name;

    /**
     * 最后处理结果
     */
    @TableField("last_handler_result")
    private String last_handler_result;

    /**
     * 拨打总次数
     */
    @TableField("call_total_num")
    private Long call_total_num;

    /**
     * 虚拟拨打总次数（辅任务拨打总次数）
     */
    @TableField("virtual_call_total_num")
    private Long virtual_call_total_num;

    /**
     * 拨打次数
     */
    @TableField("call_num")
    private Long call_num;

    /**
     * 申请状态
     */
    @TableField("status")
    private String status;

    /**
     * 策略ID
     */
    @TableField("policy_id")
    private String policy_id;

    /**
     * 营销团队
     */
    @TableField("marketing_team")
    private String marketing_team;

    /**
     * 开放渠道
     */
    @TableField("open_channel")
    private String open_channel;

    /**
     * 推广人
     */
    @TableField("broker_name")
    private String broker_name;

    /**
     * 活动码
     */
    @TableField("activity_no")
    private String activity_no;

    /**
     * 活动名称
     */
    @TableField("activity_name")
    private String activity_name;

    /**
     * 渠道码
     */
    @TableField("channel_code")
    private String channel_code;

    /**
     * 渠道名称
     */
    @TableField("channel_name")
    private String channel_name;

    /**
     * 是否激活过 0-否 1-是
     */
    @TableField("is_temporary")
    private String is_temporary;

    /**
     * 是否预约过 0-否 1-是
     */
    @TableField("is_reserve")
    private String is_reserve;

    /**
     * 是否核实过 0-否 1-是
     */
    @TableField("is_verify")
    private String is_verify;

    /**
     * 是否接通过 0-否 1-是
     */
    @TableField("is_answer")
    private String is_answer;

    /**
     * 是否手动结束过 0-否 1-是
     */
    @TableField("is_over")
    private String is_over;

    /**
     * 是否合并过 0-否 1-是
     */
    @TableField("is_merge")
    private String is_merge;

    /**
     * 是否空流水 0-否 1-是
     */
    @TableField("is_empty_call")
    private String is_empty_call;
    /**
     * 标签多项以逗号隔开
     */
    @TableField("label_options")
    private String label_options;


    /**
     * 最新问卷信息
     */
    @TableField("last_qnaire_content")
    private String last_qnaire_content;


    /**
     * 开户提交备注
     */
    @TableField("portal_notes")
    private String portal_notes;

    /**
     * 创建时间
     */
    @TableField("create_datetime")
    private Date create_datetime;

    /**
     * 更新时间
     */
    @TableField("update_datetime")
    private Date update_datetime;

}
