package com.cairh.cpe.monitor.backend.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class FollowupSurvey implements Serializable {

    /**
     * 主键ID
     */
    private String serial_id;

    /**
     * 申请ID
     */
    private String apply_id;

    /**
     * 任务ID
     */
    private String task_id;

    /**
     * 试题类别
     */
    private String survey_cate;

    /**
     * 试题结果 0-否 1-是 2-空
     */
    private String survey_result;

    /**
     * 创建时间
     */
    private Date create_datetime;

}
