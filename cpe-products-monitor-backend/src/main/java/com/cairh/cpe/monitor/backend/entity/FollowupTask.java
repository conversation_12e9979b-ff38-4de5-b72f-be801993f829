package com.cairh.cpe.monitor.backend.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 回访任务表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "FOLLOWUPTASK", schema = "crh_fus")
public class FollowupTask implements Serializable {

    /**
     * 主键ID
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 开户任务ID
     */
    @TableField("portal_biz_id")
    private String portal_biz_id;

    /**
     * 申请ID
     */
    @TableField("apply_id")
    private String apply_id;

    /**
     * 回访类型
     */
    @TableField("hf_type")
    private String hf_type;

    /**
     * 业务类型
     */
    @TableField("busin_type")
    private String busin_type;

    /**
     * 策略ID
     */
    @TableField("policy_id")
    private String policy_id;


    /**
     * 客户名称
     */
    @TableField("client_name")
    private String client_name;

    /**
     * 客户代码
     */
    @TableField("client_id")
    private String client_id;

    /**
     * 手机号码
     */
    @TableField("mobile_tel")
    private String mobile_tel;

    /**
     * 开户创建时间
     */
    @TableField("portal_create_time")
    private Date portal_create_time;

    /**
     * 性别
     */
    @TableField("sex")
    private String sex;

    /**
     * 分公司
     */
    @TableField("up_branch_no")
    private String up_branch_no;

    /**
     * 营业部
     */
    @TableField("branch_no")
    private String branch_no;

    /**
     * 营销团队
     */
    @TableField("marketing_team")
    private String marketing_team;

    /**
     * 开放渠道
     */
    @TableField("open_channel")
    private String open_channel;

    /**
     * 推广人
     */
    @TableField("broker_name")
    private String broker_name;

    /**
     * 活动码
     */
    @TableField("activity_no")
    private String activity_no;

    /**
     * 活动名称
     */
    @TableField("activity_name")
    private String activity_name;

    /**
     * 渠道码
     */
    @TableField("channel_code")
    private String channel_code;

    /**
     * 渠道名称
     */
    @TableField("channel_name")
    private String channel_name;

    /**
     * 拨打总次数
     */
    @TableField("call_total_num")
    private Long call_total_num;

    /**
     * 拨打次数
     */
    @TableField("call_num")
    private Long call_num;

    /**
     * 操作人编码
     */
    @TableField("operator_no")
    private String operator_no;

    /**
     * 操作人名称
     */
    @TableField("operator_name")
    private String operator_name;

    /**
     * 实际操作人编码
     */
    @TableField("actual_operator_no")
    private String actual_operator_no;

    /**
     * 实际操作人名称
     */
    @TableField("actual_operator_name")
    private String actual_operator_name;

    /**
     * 接通状态 0- 初始化 1-未接通 2-已接通
     */
    @TableField("answer_status")
    private String answer_status;

    /**
     * 任务状态 1- 待处理 2- 处理中 3- 已完成 4- 已关闭
     */
    @TableField("task_status")
    private String task_status;

    /**
     * 处置状态 1- 通过 2- 需要联系 3- 未接通 （判断拨打限制，是否重新生成回访任务）4- 待核实 5- 不通过
     */
    @TableField("handle_status")
    private String handle_status;

    /**
     * 任务分配时间 （任务分配操作员时间）
     */
    @TableField("allocation_datetime")
    private Date allocation_datetime;

    /**
     * 任务下发时间 （推送给全渠道时间）
     */
    @TableField("push_datetime")
    private Date push_datetime;

    /**
     * 任务处理时间
     */
    @TableField("deal_datetime")
    private Date deal_datetime;

    /**
     * 任务完成时间
     */
    @TableField("finish_datetime")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date finish_datetime;

    /**
     * 是否需要派单 0-不需要 1-需要
     */
    @TableField("need_dispatch")
    private String need_dispatch;

    /**
     * 是否预约 0-否 1-派单
     */
    @TableField("is_reserve")
    private String is_reserve;

    /**
     * 是否超时 0-否 1-是
     */
    @TableField("is_overtime")
    private String is_overtime;

    /**
     * 是否激活 0-否 1-是
     */
    @TableField("is_temporary")
    private String is_temporary;

    /**
     * 是否绿通 0-否 1-是
     */
    @TableField("is_white")
    private String is_white;

    /**
     * 队列级别
     */
    @TableField("queue_level")
    private Integer queue_level;

    /**
     * 问卷信息
     */
    @TableField("qnaire_content")
    private String qnaire_content;

    /**
     * 派单ID
     */
    @TableField("dis_task_id")
    private String dis_task_id;

    /**
     * 推送全渠道状态 0 -初始化 1-待推送 2-已推送 3-已关闭
     */
    @TableField("push_status")
    private String push_status;

    /**
     * 推送派单状态 0-未推送 1-已推送
     */
    @TableField("dispatch_status")
    private String dispatch_status;

    /**
     * 合并主任务ID
     */
    @TableField("parent_task_id")
    private String parent_task_id;

    /**
     * 合并类型 0-无合并 1-合并主任务 2-合并辅任务
     */
    @TableField("merge_type")
    private String merge_type;

    /**
     * 创建时间
     */
    @TableField("create_datetime")
    private Date create_datetime;

    /**
     * 更新时间
     */
    @TableField("update_datetime")
    private Date update_datetime;

    /**
     * 全渠道返回备注
     */
    @TableField("omnichannel_remark")
    private String omnichannel_remark;
}
