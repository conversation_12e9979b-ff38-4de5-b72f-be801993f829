package com.cairh.cpe.monitor.backend.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 渠道定义表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class LevelDispatchTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务id
     */
    private String task_id;

    /**
     * 证件号码
     */
    private String id_no;

    /**
     * 手机号码
     */
    private String mobile_tel;

    /**
     * 队列级别
     */
    private String queue_level;

    /**
     * 人物类型
     */
    private String task_type;

    /**
     * 置顶参数
     */
    private String top_pos;

    /**
     * 预置白名单
     */
    private String serial_id;
}
