package com.cairh.cpe.monitor.backend.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @since 2025/2/25 10:52
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class OperatorInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户编号
     */
    private String staff_no;

    /**
     * 用户所属营业部编号
     */
    private String branch_no;

    /**
     * 可操作营业部
     */
    private String en_branch_nos;

}
