package com.cairh.cpe.monitor.backend.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工作中的用户信息
 *
 * <AUTHOR>
 * @since 2023/11/8 09:56
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkingUserInfo implements Serializable {
    private static final long serialVersionUID = -2223350049426235680L;

    /**
     * 操作员
     */
    private String operatorNo;

    /**
     * 操作员加入工作的时间
     */
    private LocalDateTime workingTime;

}
