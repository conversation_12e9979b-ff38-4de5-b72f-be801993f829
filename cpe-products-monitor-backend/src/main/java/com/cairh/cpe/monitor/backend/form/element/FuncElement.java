package com.cairh.cpe.monitor.backend.form.element;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * xml func Element
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class FuncElement {

	/**
	 * 功能号
	 */
	private String id;

	/**
	 * 功能名
	 */
	private String moduleName;

	/**
	 * 操作类型 增删改查
	 */
	private String type;

	/**
	 * 功能描述
	 */
	private String desc;

	/**
	 * 数据库类型
	 */
	private String sourceType;
}
