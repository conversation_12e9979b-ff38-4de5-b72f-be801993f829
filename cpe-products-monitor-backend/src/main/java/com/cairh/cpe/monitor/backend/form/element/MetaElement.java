package com.cairh.cpe.monitor.backend.form.element;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * xml root Element
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MetaElement {

	private FuncElement func;

	private List<ParamElement> paramElements;

	private List<ContentElement> contentElements;

	private List<ResultElement> resultElements;

}
