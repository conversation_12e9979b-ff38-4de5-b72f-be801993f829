package com.cairh.cpe.monitor.backend.form.element;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * xml OutParam Element
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ResultElement {

	/**
	 * 出参key
	 */
	private String code;

	/**
	 * 出参value
	 */
	private String name;

	/**
	 * 出参默认值
	 */
	private String defaultValue;

}
