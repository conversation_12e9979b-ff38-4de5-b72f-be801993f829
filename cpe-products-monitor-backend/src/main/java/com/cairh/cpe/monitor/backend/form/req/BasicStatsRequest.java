package com.cairh.cpe.monitor.backend.form.req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 统计接口 请求入参
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BasicStatsRequest {

    /**
     * 接口功能号
     */
    @NotBlank(message = "function_id is not null")
    private String function_id;

    /**
     * 当前时间
     */
    private String date_time;

    /**
     * 开始时间
     */
    private String start_time;

    /**
     * 结束时间
     */
    private String end_time;


    /**
     * 开始小时 分钟
     * 例: 9:00
     */
    private String start_minute;

    /**
     * 结束小时 分钟
     * 例: 18:00
     */
    private String end_minute;

    /**
     * 渠道编号
     */
    private String channel_code;

    /**
     * 营业部编号
     */
    private String branch_no;

    /**
     * 可操作营业部
     */
    private String en_branch_nos;

    /**
     * 操作员编号
     */
    private String operator_no;

    /**
     * 操作员姓名
     */
    private String operator_name;

    /**
     * 客户姓名
     */
    private String client_name;

    /**
     * 日期类型 1.按日分组, 2.按月分组, 3.按周分组
     * 省市区类型 1.省, 2.市, 3.区
     * 完成转化时间类型 1. 天  2. 7天  3. 月
     * 开户时长分析-视频类型 1. 双向  0. 单向
     * 统计方式 1。按次统计 2.按人统计
     */
    private String stat_flag;

    /**
     * 视频类型 1. 双向  0. 单向
     */
    private String video_type;

    /**
     * 工作量过滤标志 1.过滤 2.不过滤
     */
    private String filter_flag;

    /**
     * 开户状态
     */
    private String request_status;

    /**
     * 开户类型
     */
    private String open_type;

    /**
     * 营销关系 1.有 2.无
     */
    private String market_relation_flag;

    /**
     * 接入方式
     */
    private String app_id;

    /**
     * 客户标签
     */
    private String client_tags;

    /**
     * 推送结果 1.推送cc 2.本地数据
     */
    private String push_flag;

    /**
     * 一次性办理标志
     * 一次性开户 1. 单次完成开户 0.非单次完成开户
     */
    private String one_time_flag;

    /**
     * 手机号
     */
    private String mobile_tel;

    /**
     * 修改项目
     */
    private String modify_item;

    /**
     * 断点节点
     */
    private String anode_id;

    /**
     * 每页记录数
     */
    private Integer page_size;

    /**
     * 页号
     */
    private Integer page_num;

    /**
     * 业务类型
     */
    private String busin_type;

    /**
     * 状态
     */
    private String status;

    /**
     * 时间属性
     */
    private String time_property;

    /**
     * 上级机构
     */
    private String up_branch_no;

    /**
     * 任务类型
     */
    private String task_type;

    /**
     * 申请起始日期
     */
    private String request_start_time;

    /**
     * 申请结束日期
     */
    private String request_end_time;

    /**
     * 见证起始日期
     */
    private String audit_start_time;

    /**
     * 见证结束日期
     */
    private String audit_end_time;

    /**
     * 复核起始日期
     */
    private String review_start_time;

    /**
     * 复核结束日期
     */
    private String review_end_time;


    /**
     * 二次复核起始日期
     */
    private String secondary_review_start_time;

    /**
     * 二次复核结束日期
     */
    private String secondary_review_end_time;

    /**
     * 推荐人工号
     */
    private String broker_code;

    /**
     * 推荐人
     */
    private String broker_name;

    /**
     * 活动码
     */
    private String activity_no;

    /**
     * 证件号码
     */
    private String id_no;

    /**
     * 客户姓名
     */
    private String user_name;

    /**
     * 见证人工号
     */
    private String audit_operator_no;

    /**
     * 见证人姓名
     */
    private String audit_operator_name;


    /**
     * 复核人工号
     */
    private String review_operator_no;

    /**
     * 复核人姓名
     */
    private String review_operator_name;

    /**
     * 二次复核人工号
     */
    private String secondary_review_operator_no;

    /**
     * 二次复核人姓名
     */
    private String secondary_review_operator_name;

    /**
     * 子系统
     */
    private String subsys_no;

    /**
     * 员工号
     */
    private String staff_no;

    /**
     * 排序信息
     */
    private String sort_info;

    /**
     * 业务类型
     */
    private String business_type;

    /**
     * 开户营业部
     */
    private String op_branch_no;

    /**
     * 外呼标志
     */
    private String call_flag;

    /**
     * 增加标签标志
     */
    private String match_labels_flag;

    //**************************回访相关参数****************************//

    /**
     * 是否首次统计
     */
    private Integer first_task;

    /**
     * 执行人工号
     */
    private String actual_operator_no;

    /**
     * 执行人姓名
     */
    private String actual_operator_name;

    //前端入参的时间

    /**
     * 回访申请创建的开始时间
     */
    private String apply_start_time;

    /**
     * 回访申请创建的结束时间
     */
    private String apply_end_time;

    /**
     * 任务下发开始时间
     */
    private String push_start_time;

    /**
     * 任务下发结束时间
     */
    private String push_end_time;
    /**
     * 任务处理时间查询开始时间
     */
    private String deal_start_time;

    /**
     * 任务处理时间查询结束时间
     */
    private String deal_end_time;

    /**
     * 任务结束时间查询开始时间
     */
    private String finish_start_time;

    /**
     * 任务结束时间查询结束时间
     */
    private String finish_end_time;

    /**
     * 任务生成查询开始时间
     */
    private String task_create_start_time;

    /**
     * 任务生成查询结束时间
     */
    private String task_create_end_time;

    /**
     * 有合计列
     */
    private Boolean haveSum;

    /**
     * 标签
     */
    private String label;

    /**
     *  活动名称
     */
    private String activity_name;

    /**
     * 渠道名称
     */
    private String channel_name;

    /**
     * 营销团队
     */
    private String marketing_team;

    /**
     * 接通情况
     */
    private String answer_status;

    /**
     *  一级驳回原因
     */
    private String reject_reason;


    //**************************回访相关参数****************************//

    //**************************回访质检参数****************************//

    /**
     * 排除的翻译字段
     */
    private List<String> excludeTransField;
    /**
     * 质检分配时间开始
     */
    private String distribute_datetime_start;
    /**
     * 质检分配时间结束
     */
    private String distribute_datetime_end;
    /**
     * 质检完成时间开始
     */
    private String qcr_finish_datetime_start;
    /**
     * 质检完成时间结束
     */
    private String qcr_finish_datetime_end;
    /**
     * 任务类型
     */
    private String quality_task_type;
    /**
     * 标签
     */
    private String label_options;
    /**
     * 初检人工号
     */
    private String quality_operator_no;
    /**
     * 初检人姓名
     */
    private String quality_operator_name;
    /**
     * 复检人工号
     */
    private String review_quality_operator_no;
    /**
     * 复检人姓名
     */
    private String review_quality_operator_name;
    /**
     * 被检人姓名
     */
    private String last_operator_name;
    /**
     * 被检人工号
     */
    private String last_operator_no;
    /**
     * 抽检类型 1: "人工抽检",2: "AI智能质检"
     */
    private String quality_task_source;
    //**************************回访质检参数****************************//

}
