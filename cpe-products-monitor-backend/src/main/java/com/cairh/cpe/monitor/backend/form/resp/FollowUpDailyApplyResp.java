package com.cairh.cpe.monitor.backend.form.resp;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 回访每日申请报表
 */
@Data
public class FollowUpDailyApplyResp {
    // 回访申请量
    private Integer apply_count;

    // 完成量
    private Integer complete_count;

    // 完成率
    private BigDecimal complete_rate;

    // 回访通过量
    private Integer pass_count;

    // 回访通过率
    private BigDecimal pass_rate;

    // 回访不合规量
    private Integer invalid_count;

    // 回访不合规率
    private BigDecimal invalid_rate;

    // 拒绝回访量
    private Integer reject_count;

    // 拒绝回访率
    private BigDecimal reject_rate;

    // 策略执行完成量
    private Integer strategy_complete_count;

    // 策略执行完成率
    private BigDecimal strategy_complete_rate;

    // 手动结束量
    private Integer manual_end_count;

    // 手动结束率
    private BigDecimal manual_end_rate;

    // 完成问卷量
    private Integer survey_complete_count;

    // 完成问卷率
    private BigDecimal survey_complete_rate;

    // 平均首次响应时间
    private Double avg_first_response_time;
}
