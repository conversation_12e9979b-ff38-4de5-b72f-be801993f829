package com.cairh.cpe.monitor.backend.form.resp;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 每日报表(任务维度)
 */
@Data
public class FollowUpDailyTaskResp {
    // 任务生成量
    private Integer task_create_count;

    // 任务下发量
    private Integer task_push_count;

    // 任务完成量
    private Integer task_complete_count;

    // 任务完成率
    private BigDecimal task_complete_rate;

    // 拨打总次数
    private Integer call_total_count;

    // 接通总次数
    private Integer connect_total_count;

    // 上线人数
    private Integer online_user_count;

    // 日均产能=拨打总次数/上线任务
    private Double daily_capacity;

    // 平均响应时长
    private Double avg_response_time;
}