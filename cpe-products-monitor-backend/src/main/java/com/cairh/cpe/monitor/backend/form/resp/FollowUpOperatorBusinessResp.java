package com.cairh.cpe.monitor.backend.form.resp;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 坐席个人业务报表
 */
@Data
public class FollowUpOperatorBusinessResp {
    // 执行人姓名
    private String operator_name;

    // 执行人工号
    private String operator_no;

    // 任务完成量
    private Integer task_complete_count;

    // 外呼次数
    private Integer call_count;

    // 外呼接通次数
    private Integer call_connect_count;

    // 外呼接通率
    private BigDecimal call_connect_rate;

    // 平均响应时长
    private BigDecimal avg_response_duration;

    // 平均处理时长
    private BigDecimal avg_handling_duration;

    // 回访通过率
    private BigDecimal visit_pass_rate;

    // 不合规率
    private BigDecimal invalid_rate;

    // 拒绝回访率
    private BigDecimal reject_rate;

    private BigDecimal survey_completion_rate;

    // 0-5分响应率
    private BigDecimal response_0_5m_rate;

    // 5-10分响应率
    private BigDecimal response_5_10m_rate;

    // 10-15分响应率
    private BigDecimal response_10_15m_rate;

    // 15分钟以上响应率
    private BigDecimal response_over_15m_rate;

    // 30分钟以上响应率
    private BigDecimal response_over_30m_rate;

    // 45分钟以上响应率
    private BigDecimal response_over_45m_rate;
}
