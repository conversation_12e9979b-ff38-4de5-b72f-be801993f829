package com.cairh.cpe.monitor.backend.form.resp;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 回访坐席在岗统计报表
 */

@Data
public class FollowUpOperatorWorkResp {
    // 执行人姓名
    private String operator_name;

    // 执行人工号
    private String operator_no;

    // 当天最早上线时间
    @JsonFormat(pattern = "yyyy-MM-dd HH-mm-ss", timezone = "GMT+8")
    private LocalDateTime first_login_time;

    // 当天最晚下线时间
    @JsonFormat(pattern = "yyyy-MM-dd HH-mm-ss", timezone = "GMT+8")
    private LocalDateTime last_logout_time;

    // 全天在线时长（小时）
    private Double online_duration_hours;

    // 当天第一笔处理时间
    @JsonFormat(pattern = "yyyy-MM-dd HH-mm-ss", timezone = "GMT+8")
    private LocalDateTime first_process_time;

    // 当天最后一笔处理时间
    @JsonFormat(pattern = "yyyy-MM-dd HH-mm-ss", timezone = "GMT+8")
    private LocalDateTime last_process_time;

    // 全天处理总时长
    private Double total_process_duration;

    // 坐席工作效率
    private BigDecimal work_efficiency;

    // 坐席外呼效率
    private BigDecimal call_efficiency;
}
