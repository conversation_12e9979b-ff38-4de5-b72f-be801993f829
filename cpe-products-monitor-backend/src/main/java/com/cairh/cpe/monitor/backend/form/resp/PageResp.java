package com.cairh.cpe.monitor.backend.form.resp;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PageResp<T> {


    /**
     * 总数
     */
    protected long total = 0;



    /**
     * 每页多少条
     */
    protected long size = 10;

    /**
     * 当前页
     */
    protected long current = 1;

    /**
     * 列表数据
     */
    private List<T> records;


    public static PageResp ofEmptyPage(Page<?> page) {
        return new PageResp(0L,page.getCurrent(), page.getSize(), Collections.emptyList());
    }

    public static PageResp ofEmptyPage(long current,long size) {
        return new PageResp(0L,current, size, Collections.emptyList());
    }

    public static PageResp ofPage(Page<?> page) {
        return new PageResp(page);
    }

    public static <T> PageResp<T> ofPage(Page<?> page, List<T> list) {
        return new PageResp(page,list);
    }

    public static PageResp ofPage(long total,long current,long size, List<?> list) {
        return new PageResp(total,current,size,list);
    }

    private PageResp(Page<?> page) {
        this.total = page.getTotal();
        this.size = page.getSize();
        this.current = page.getCurrent();
    }

    private PageResp(Page<T> page, List<T> records) {
        this.total = page.getTotal();
        this.size = page.getSize();
        this.current = page.getCurrent();
        this.records = records;
    }

    private PageResp(long total, long current, long size, List<T> records) {
        this.total = total;
        this.size = size;
        this.current = current;
        this.records = records;
    }

    private PageResp() {
    }
}
