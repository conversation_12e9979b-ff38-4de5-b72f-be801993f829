package com.cairh.cpe.monitor.backend.form.resp;

import lombok.Data;

/**
 * 今日跟进任务执行数据响应实体
 */
@Data
public class TodayFollowUpTaskExecutionDataResp {
    /**
     * 待处理任务总量
     * @see #unrespondedToBeProcessedTask
     * @see #respondedToBeProcessedTask
     */
    private Long toBeProcessedTaskTotal;

    /**
     * 待处理任务量（未首响）
     * <p>指尚未进行首次响应的待处理任务数量</p>
     */
    private Long unrespondedToBeProcessedTask;

    /**
     * 待处理任务量（已首响）
     * <p>指已完成首次响应但未闭环的任务数量</p>
     */
    private Long respondedToBeProcessedTask;

    /**
     * 任务生成总量
     * @since v1.0.1 新增统计维度
     */
    private Long taskGeneratedTotal;

    /**
     * 任务下发总量
     * <p>包含成功下发和失败重试的累计值</p>
     */
    private Long taskIssuedTotal;

    /**
     * 已处理任务量
     */
    private Long processedTaskAmount;

    /**
     * 任务处理率（百分比）
     * @value 示例：98.75 表示98.75%
     */
    private String taskProcessingRate;

    /**
     * 在线坐席数
     * @apiNote 实时统计的登录系统坐席数
     */
    private Long onlineDispatchOperatorNum;

    /**
     * 承压指数（保留两位小数）
     * <p>计算公式：(待处理量 / 坐席数) × 分钟产能</p>
     */
    private String pressureIndex;

    /**
     * 分钟产能
     * <p>单个坐席每分钟可处理的任务数</p>
     * @unit 件/分钟
     */
    private Long minuteCapacity;
}
