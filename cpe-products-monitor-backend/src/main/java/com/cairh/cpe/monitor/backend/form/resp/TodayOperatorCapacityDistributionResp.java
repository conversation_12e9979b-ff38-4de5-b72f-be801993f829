package com.cairh.cpe.monitor.backend.form.resp;

import lombok.Data;

/**
 * 今日坐席产能分布
 */
@Data
public class TodayOperatorCapacityDistributionResp {
    /**
     * 0-60笔
     */
    private Integer level_1_number;

    /**
     * 0-60笔人数占比
     */
    private String level_1_rate;

    /**
     * 60 - 100笔
     */
    private Integer level_2_number;

    /**
     * 60 - 100笔人数占比
     */
    private String level_2_rate;


    /**
     * 100 - 160笔
     */
    private Integer level_3_number;

    /**
     * 100 - 160笔人数占比
     */
    private String level_3_rate;

    /**
     * 160 - 200笔
     */
    private Integer level_4_number;

    /**
     * 160 - 200笔人数占比
     */
    private String level_4_rate;

    /**
     * 200 - 260笔
     */
    private Integer level_5_number;

    /**
     * 200 - 260笔人数占比
     */
    private String level_5_rate;

    /**
     * 260 - 300笔
     */
    private Integer level_6_number;

    /**
     * 260 - 300笔人数占比
     */
    private String level_6_rate;

    /**
     * 300笔以上
     */
    private Integer level_7_number;

    /**
     * 300笔以上人数占比
     */
    private String level_7_rate;

}
