package com.cairh.cpe.monitor.backend.form.resp;

import lombok.Data;

/**
 * 今日坐席执行状态
 */
@Data
public class TodayOperatorExecuteStatusResp {

    /**
     * 坐席编号
     */
    private String operatorNo;

    /**
     * 坐席姓名
     */
    private String operatorName;

    /**
     * 在线状态 "1" 签入 "2" 签出
     */
    private String status;

    /**
     * 在线状态名称
     */
    private String statusName;

    /**
     * 任务执行量
     */
    private Integer taskExecuteNum;

    /**
     * 未执行量
     */
    private Integer taskNoExecuteNum;

    /**
     * 处理超时次数
     */
    private Integer overTimeNum;

    /**
     * 展示告警图标
     */
    private Boolean overIcon;

    /**
     * 颜色
     *("1", "红色"),
     *("2", "黄色"),
     *("3", "灰色"),
     */
    private String color;
}
