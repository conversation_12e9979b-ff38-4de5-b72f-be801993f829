package com.cairh.cpe.monitor.backend.handler;

import com.alibaba.fastjson.JSON;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.Result;
import com.cairh.cpe.context.constant.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.HashMap;
import java.util.Map;

import static com.cairh.cpe.common.constant.MonitorErrorCode.VALID_PARAM_IS_ERROR;


@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {

    @ResponseBody
    @ExceptionHandler(BizException.class)
    public Result<?> globalException(BizException e) {
        log.error("业务异常:", e.getCause());
        return Result.fail(e.getError_no(), e.getError_info());
    }

    /**
     * 拦截违反唯一约束异常
     */
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler
    public Result<?> DuplicateKeyException(DuplicateKeyException e) {
        log.error("添加失败:{}", e.getMessage());
        return Result.fail(ErrorCode.ERR_PARAM_IN_ERROR, "添加失败, 存在重复添加的项");
    }

    /**
     * 不符合参数校验规则
     */
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler
    public Result<?> MethodArgumentNotValidException(MethodArgumentNotValidException e) {
        BindingResult bindingResult = e.getBindingResult();
        Map<String, String> resMap = new HashMap<>();
        bindingResult.getFieldErrors().forEach(item -> resMap.put(item.getField(), item.getDefaultMessage()));
        return Result.fail(VALID_PARAM_IS_ERROR, JSON.toJSONString(resMap));
    }

    /**
     * 拦截未知的运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Result<?> notFount(RuntimeException e) {
        e.printStackTrace();
        log.error("运行时异常:", e);
        return Result.fail("-99", e.getMessage());
    }

}
