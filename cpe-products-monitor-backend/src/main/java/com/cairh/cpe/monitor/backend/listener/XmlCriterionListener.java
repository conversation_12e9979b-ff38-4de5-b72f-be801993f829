package com.cairh.cpe.monitor.backend.listener;

import com.alibaba.fastjson.JSON;
import com.cairh.cpe.common.constant.CacheKeyConfig;
import com.cairh.cpe.config.support.ConfigData;
import com.cairh.cpe.config.support.ConfigDataUrlListener;
import com.cairh.cpe.config.url.ConfigDataUrl;
import com.cairh.cpe.monitor.backend.constant.Fields;
import com.cairh.cpe.monitor.backend.constant.ParseTypeEnum;
import com.cairh.cpe.monitor.backend.form.element.MetaElement;
import com.cairh.cpe.monitor.backend.util.XmlConvertEntityUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Objects;

@Slf4j
@Component
public class XmlCriterionListener implements ConfigDataUrlListener {

    private static final String url = "nacos://cpe-products-monitor-backend.xml?group=DEFAULT_GROUP";

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public ConfigDataUrl getConfigDataUrl() {
        return ConfigDataUrl.simple(url);
    }

    @SneakyThrows
    @Override
    public void configChanged(ConfigData configData) {

        //1.获取xml配置信息
        String xmlCriterionValue = configData.getNewValue();
        if (StringUtils.isBlank(xmlCriterionValue)) {
            return;
        }

        //2.解析xml成实体类
        SAXReader saxReader = new SAXReader();
        Document document = null;
        try {
            //xml配置信息不要包含 <?xml version="1.0" encoding="GB2312"?> 内容，且不要设置encoding
            document = saxReader.read(new ByteArrayInputStream(xmlCriterionValue.getBytes()));
        } catch (DocumentException e) {
            log.error("XmlCriterionListener,saxReader读取xml配置信息转换异常");
            e.printStackTrace();
        }

        if (Objects.isNull(document)) {
            return;
        }

        HashMap<String, String> fundEntry = new HashMap<>();
        Element rootElement = document.getRootElement();

        for (Iterator i = rootElement.elementIterator(); i.hasNext(); ) {
            Element element = (Element) i.next();
            MetaElement metaElement = new MetaElement();
            // 获取func_id
            String func_id = element.attribute(Fields.ID).getValue();
            XmlConvertEntityUtil.deal(metaElement, element, ParseTypeEnum.Func);
            // 遍历func元素子节点
            for (Iterator j = element.elementIterator(); j.hasNext(); ) {
                Element sonElement = (Element) j.next();
                ParseTypeEnum parseTypeEnum = ParseTypeEnum.valueOf(sonElement.getName());
                XmlConvertEntityUtil.deal(metaElement, sonElement, parseTypeEnum);
            }
            fundEntry.put(func_id, JSON.toJSONString(metaElement));
        }

        //3.转换成json,写入到redis
        redisTemplate.delete(CacheKeyConfig.XML_CRITERION_CACHE_KEY);
        redisTemplate.opsForHash().putAll(CacheKeyConfig.XML_CRITERION_CACHE_KEY, fundEntry);
    }
}
