package com.cairh.cpe.monitor.backend.mapper;

import com.cairh.cpe.common.entity.AcOperatorInfo;
import com.cairh.cpe.common.entity.AcOperatorTaskCount;
import com.cairh.cpe.common.entity.AcOperatorTodayTaskCount;
import com.cairh.cpe.monitor.backend.entity.DispatchTaskOverview;
import com.cairh.cpe.monitor.backend.entity.FinshDispatchTaskWithFailCount;
import com.cairh.cpe.monitor.backend.entity.LevelDispatchTask;
import com.cairh.cpe.monitor.backend.entity.OperatorTaskHandleNum;
import com.cairh.cpe.monitor.backend.form.resp.TodayOperatorExecuteStatusResp;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2023-06-16
 */
public interface AdsReportMapper {

    List<AcOperatorInfo> getAcOperatorInfoList();

    Integer getUnDealTaskCount();

    List<AcOperatorTaskCount> getAcOperatorTaskCount(String unitTime);

    Integer getTaskCountPerSecond(String unitTime);

    List<AcOperatorTodayTaskCount> getAcOperatorTodayTaskCount();

    List<LevelDispatchTask> getLevelDispatchTask();

    List<FinshDispatchTaskWithFailCount> getFinshDispatchTaskWithFailCount();

    List<DispatchTaskOverview> getDispatchTaskOverview();

    /**
     * 获取回访派单概况
     *
     * @return
     */
    Map<String, Object> getHfDispatchTaskOverview();

    /**
     * 获取坐席数量
     *
     * @return
     */
    Integer getAllOperatorNum();

    /**
     * 获取今日上线过的坐席数量
     *
     * @return
     */
    Integer getTodayOnlineOperatorNum();

    /**
     * 获取今日上线过的坐席工号
     *
     * @return
     */
    List<String> getTodayOnlineOperatorNoList();

    /**
     * 获取今日上线过的坐席
     *
     * @return
     */
    List<TodayOperatorExecuteStatusResp> getTodayOnlineOperatorList();

    /**
     * 获取今日上线过的坐席任务处理量
     *
     * @return
     */
    List<OperatorTaskHandleNum> getTodayOnlineOperatorTaskHandleNum();
}
