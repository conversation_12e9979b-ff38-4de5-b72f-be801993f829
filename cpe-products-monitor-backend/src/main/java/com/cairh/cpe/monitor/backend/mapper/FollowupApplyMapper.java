package com.cairh.cpe.monitor.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cairh.cpe.monitor.backend.entity.FollowupApply;
import com.cairh.cpe.monitor.backend.form.req.BasicStatsRequest;
import com.cairh.cpe.monitor.backend.form.resp.FollowUpDailyApplyResp;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface FollowupApplyMapper extends BaseMapper<FollowupApply> {
    FollowUpDailyApplyResp dailyApply(@Param("param") BasicStatsRequest basicStatsRequest);
}
