package com.cairh.cpe.monitor.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.monitor.backend.entity.FollowupOperatorStatistics;
import com.cairh.cpe.monitor.backend.entity.FollowupTask;
import com.cairh.cpe.monitor.backend.form.req.BasicStatsRequest;
import com.cairh.cpe.monitor.backend.form.resp.FollowUpOperatorWorkResp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FollowupOperatorStatisticsMapper extends BaseMapper<FollowupOperatorStatistics> {
    List<FollowUpOperatorWorkResp> operatorWork(Page<FollowupTask> pageTask, @Param("param") BasicStatsRequest basicStatsRequest);

    FollowUpOperatorWorkResp operatorWorkTotal(@Param("param") BasicStatsRequest basicStatsRequest);

    Integer getOnlineOperatorNum(@Param("param") BasicStatsRequest basicStatsRequest);
}
