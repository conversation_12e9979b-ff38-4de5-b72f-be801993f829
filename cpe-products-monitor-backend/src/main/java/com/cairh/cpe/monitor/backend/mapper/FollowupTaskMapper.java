package com.cairh.cpe.monitor.backend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.monitor.backend.entity.FollowupTask;
import com.cairh.cpe.monitor.backend.entity.OperatorGroupAndCount;
import com.cairh.cpe.monitor.backend.form.req.BasicStatsRequest;
import com.cairh.cpe.monitor.backend.form.resp.FollowUpDailyTaskResp;
import com.cairh.cpe.monitor.backend.form.resp.FollowUpOperatorBusinessResp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface FollowupTaskMapper extends BaseMapper<FollowupTask> {
    FollowUpDailyTaskResp dailyTask(@Param("param") BasicStatsRequest basicStatsRequest);

    List<FollowUpOperatorBusinessResp> operatorBusiness(Page<FollowupTask> pageTask, @Param("param") BasicStatsRequest basicStatsRequest);

    FollowUpOperatorBusinessResp operatorBusinessTotal(@Param("param") BasicStatsRequest basicStatsRequest);

    List<OperatorGroupAndCount> groupAndCountTodayByStatus(@Param("param") List<String> status);

    List<OperatorGroupAndCount> groupAndCountTodayByStatusOperatorNo(@Param("param") List<String> status);

    List<OperatorGroupAndCount> groupAndCountTodayOverTime();

    List<OperatorGroupAndCount> groupAndCountTodayNoHandleAndOverTime();

}
