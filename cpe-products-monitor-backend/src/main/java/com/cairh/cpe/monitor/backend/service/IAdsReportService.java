package com.cairh.cpe.monitor.backend.service;

import com.cairh.cpe.monitor.backend.form.resp.OperatorOnlineOverviewResp;
import com.cairh.cpe.monitor.backend.form.resp.TodayFollowUpTaskExecutionDataResp;
import com.cairh.cpe.monitor.backend.form.resp.TodayOperatorCapacityDistributionResp;
import com.cairh.cpe.monitor.backend.form.resp.TodayOperatorExecuteStatusResp;

import java.util.List;
import java.util.Map;

public interface IAdsReportService {

    Map<String, Object> queryADS9001();

    Map<String, Object> queryADS9002();

    Map<String, Object> queryADS9003();

    Map<String, Object> queryADS9004(String start_time, String end_time);

    Map<String, Object> queryADS9005();

    OperatorOnlineOverviewResp queryADS9006();

    TodayOperatorCapacityDistributionResp queryADS9007();

    TodayFollowUpTaskExecutionDataResp queryADS9008();

    List<TodayOperatorExecuteStatusResp> queryADS9009();

}

