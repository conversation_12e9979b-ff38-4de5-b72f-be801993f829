package com.cairh.cpe.monitor.backend.service;

import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.monitor.backend.form.req.BasicStatsRequest;
import com.cairh.cpe.monitor.backend.form.req.MonitorDataReq;
import com.cairh.cpe.monitor.backend.form.resp.MonthlyTeamReviewRankingResp;

import java.util.List;

/**
 * <AUTHOR>
 */

public interface ICommonStatsService {

    Object commonStatsEntrance(BaseUser baseUser, BasicStatsRequest basicStatsRequest);

    List<MonthlyTeamReviewRankingResp> getMonthlyTeamReviewRanking();

    List<?> getMonitorData(MonitorDataReq req);

    Object newCommonStatsEntrance(BaseUser baseUser, BasicStatsRequest basicStatsRequest);

    /**
     * 新增接口，支持字段翻译
     * @param baseUser
     * @param basicStatsRequest
     * @return
     */
    Object commonStatsEntranceWithFieldTranslation(BaseUser baseUser, BasicStatsRequest basicStatsRequest);
}
