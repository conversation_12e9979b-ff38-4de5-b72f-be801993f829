package com.cairh.cpe.monitor.backend.service;

import com.cairh.cpe.monitor.backend.form.req.BasicStatsRequest;
import com.cairh.cpe.monitor.backend.form.resp.*;

public interface IFollowUpReportService {
    PageResp<FollowUpOperatorBusinessResp> operatorBusiness(BasicStatsRequest basicStatsRequest);

    FollowUpOperatorBusinessResp operatorBusinessTotal(BasicStatsRequest basicStatsRequest);

    PageResp<FollowUpOperatorWorkResp> operatorWork(BasicStatsRequest basicStatsRequest);

    FollowUpOperatorWorkResp operatorWorkTotal(BasicStatsRequest basicStatsRequest);

    FollowUpDailyApplyResp dailyApply(BasicStatsRequest basicStatsRequest);

    FollowUpDailyTaskResp dailyTask(BasicStatsRequest basicStatsRequest);
}

