package com.cairh.cpe.monitor.backend.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cairh.cpe.common.constant.CacheKeyConfig;
import com.cairh.cpe.common.constant.Constant;
import com.cairh.cpe.common.constant.MonitorErrorCode;
import com.cairh.cpe.common.entity.AcOperatorInfo;
import com.cairh.cpe.common.entity.AcOperatorTaskCount;
import com.cairh.cpe.common.entity.AcOperatorTodayTaskCount;
import com.cairh.cpe.common.util.DateUtil;
import com.cairh.cpe.common.util.RedisService;
import com.cairh.cpe.common.util.SysParamService;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.esb.base.rpc.IVBaseOnlineUserDubboService;
import com.cairh.cpe.esb.base.rpc.dto.resp.OnlineUserResponse;
import com.cairh.cpe.monitor.backend.constant.*;
import com.cairh.cpe.monitor.backend.entity.*;
import com.cairh.cpe.monitor.backend.form.resp.OperatorOnlineOverviewResp;
import com.cairh.cpe.monitor.backend.form.resp.TodayFollowUpTaskExecutionDataResp;
import com.cairh.cpe.monitor.backend.form.resp.TodayOperatorCapacityDistributionResp;
import com.cairh.cpe.monitor.backend.form.resp.TodayOperatorExecuteStatusResp;
import com.cairh.cpe.monitor.backend.mapper.AdsReportMapper;
import com.cairh.cpe.monitor.backend.mapper.FollowupTaskMapper;
import com.cairh.cpe.monitor.backend.mapper.GeneralXmlMapper;
import com.cairh.cpe.monitor.backend.mapper.OperatorOnlineTimeMapper;
import com.cairh.cpe.monitor.backend.service.DefaultRespInitService;
import com.cairh.cpe.monitor.backend.service.IAdsReportService;
import com.cairh.cpe.monitor.backend.service.IFollowupApplyService;
import com.cairh.cpe.monitor.backend.util.NumberCalculateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Slf4j
@Service
public class AdsReportServiceImpl implements IAdsReportService {

    /**
     * 绿色通道生成的白名单(证件)
     */
    public static final String WHITE_LIST_ID_NO = "whiteListIdNo";
    /**
     * 绿色通道生成的白名单(手机号)
     */
    public static final String WHITE_LIST_MOBILE_TEL = "whiteListMobileTel";

    @Autowired
    private AdsReportMapper adsReportMapper;

    @Autowired
    private RedisService redisService;

    @Autowired
    private SysParamService sysParamService;

    @Autowired
    private GeneralXmlMapper generalXmlMapper;

    @Autowired
    private OperatorOnlineTimeMapper operatorOnlineTimeMapper;

    @Autowired
    private DefaultRespInitService defaultRespInitService;

    @Autowired
    private IFollowupApplyService followupApplyService;

    @Autowired
    private FollowupTaskMapper followupTaskMapper;

    @DubboReference(check = false, lazy = true)
    private IVBaseOnlineUserDubboService baseOnlineUserDubboService;

    @Autowired
    @Qualifier("monitorExecutor")
    private ThreadPoolTaskExecutor monitorExecutor;

    public static String getRate(int v1, int v2) {
        return BigDecimal.valueOf(v1)
                .divide(BigDecimal.valueOf(v2), 12, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100))
                .setScale(2, BigDecimal.ROUND_DOWN).toString();
    }

    @Override
    public Map<String, Object> queryADS9001() {
        Map<String, Object> resultMap = new HashMap<>();
        List<AcOperatorInfo> list = adsReportMapper.getAcOperatorInfoList();
        List<AcOperatorInfo> headAcOperatorNolist = list.stream().filter(item -> StringUtils.equals(item.getBranch_no(), "3")).collect(Collectors.toList());
        List<AcOperatorInfo> tgAcOperatorNolist = list.stream().filter(item -> !StringUtils.equals(item.getBranch_no(), "3")).collect(Collectors.toList());
        List<OnlineUserResponse> onlineUserResponseList = baseOnlineUserDubboService.onlineUserInfo();
        Set<String> onlineOperatorNoSet = onlineUserResponseList.stream().map(OnlineUserResponse::getStaff_no).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Set<String> headAcOperatorNoSet = headAcOperatorNolist.stream().map(AcOperatorInfo::getStaff_no).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        Set<String> tgAcOperatorNoSet = tgAcOperatorNolist.stream().map(AcOperatorInfo::getStaff_no).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        log.debug("在线操作员集合: {}, 总部派单操作员集合: {}, 托管派单操作员集合: {}", onlineOperatorNoSet, headAcOperatorNoSet, tgAcOperatorNoSet);
        ArrayList<String> headOnlineAcOperatorNolist = new ArrayList<>(CollectionUtils.intersection(headAcOperatorNoSet, onlineOperatorNoSet));
        ArrayList<String> tgOnlineAcOperatorNolist = new ArrayList<>(CollectionUtils.intersection(tgAcOperatorNoSet, onlineOperatorNoSet));
        List<WorkingUserInfo> workingUserInfoList = this.getWorkingUserInfoList();
        Set<String> workingOperatorNoSet = workingUserInfoList.stream().map(WorkingUserInfo::getOperatorNo).filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        log.debug("工作队列中操作员集合: {}", workingOperatorNoSet);
        ArrayList<String> headWorkingAcOperatorNolist = new ArrayList<>(CollectionUtils.intersection(new HashSet<>(headOnlineAcOperatorNolist), workingOperatorNoSet));
        ArrayList<String> tgWorkingAcOperatorNolist = new ArrayList<>(CollectionUtils.intersection(new HashSet<>(tgOnlineAcOperatorNolist), workingOperatorNoSet));
        log.debug("总部工作中操作员集合: {}, 托管工作中操作员集合: {}", headWorkingAcOperatorNolist, tgWorkingAcOperatorNolist);
        double headOnlinePercent = headAcOperatorNolist.size() == 0 ? 0 : (double) headOnlineAcOperatorNolist.size() / headAcOperatorNolist.size();
        double tgOnlinePercent = tgAcOperatorNolist.size() == 0 ? 0 : (double) tgOnlineAcOperatorNolist.size() / tgAcOperatorNolist.size();
        String headOnlinePercentStr = String.format("%.2f", headOnlinePercent * 100) + "%";
        String tgOnlinePercentStr = String.format("%.2f", tgOnlinePercent * 100) + "%";
        resultMap.put("headOnlinePercentStr", headOnlinePercentStr);
        resultMap.put("tgOnlinePercentStr", tgOnlinePercentStr);

        resultMap.put("headAcOperatorNum", headAcOperatorNolist.size());
        resultMap.put("headAcOperatorOnlineNum", headOnlineAcOperatorNolist.size());
        resultMap.put("headAcOperatorOfflineNum", headAcOperatorNolist.size() - headOnlineAcOperatorNolist.size());
        resultMap.put("headAcOperatorOnlineWorkingNum", headWorkingAcOperatorNolist.size());
        double headAcOperatorOnlieRadio = 0L;
        double headAcOperatorOfflieRadio = 0L;
        double headAcOperatorOnlieWorkingRadio = 0L;
        if (headAcOperatorNolist.size() != 0) {
            headAcOperatorOnlieRadio = (double) headOnlineAcOperatorNolist.size() / headAcOperatorNolist.size();
            headAcOperatorOfflieRadio = (double) (headAcOperatorNolist.size() - headOnlineAcOperatorNolist.size()) / headAcOperatorNolist.size();
            headAcOperatorOnlieWorkingRadio = (double) headWorkingAcOperatorNolist.size() / headAcOperatorNolist.size();
        }
        String headAcOperatorOnlieRadioStr = String.format("%.2f", headAcOperatorOnlieRadio * 100) + "%";
        resultMap.put("headAcOperatorOnlieRadioStr", headAcOperatorOnlieRadioStr);
        String headAcOperatorOfflieRadioStr = String.format("%.2f", headAcOperatorOfflieRadio * 100) + "%";
        resultMap.put("headAcOperatorOfflieRadioStr", headAcOperatorOfflieRadioStr);
        String headAcOperatorOnlieWorkingRadioStr = String.format("%.2f", headAcOperatorOnlieWorkingRadio * 100) + "%";
        resultMap.put("headAcOperatorOnlieWorkingRadioStr", headAcOperatorOnlieWorkingRadioStr);

        resultMap.put("tgAcOperatorNum", tgAcOperatorNolist.size());
        resultMap.put("tgAcOperatorOnlineNum", tgOnlineAcOperatorNolist.size());
        resultMap.put("tgAcOperatorOfflineNum", tgAcOperatorNolist.size() - tgOnlineAcOperatorNolist.size());
        resultMap.put("tgAcOperatorOnlineWorkingNum", tgWorkingAcOperatorNolist.size());
        double tgAcOperatorOnlieRadio = 0L;
        double tgAcOperatorOfflieRadio = 0L;
        double tgAcOperatorOnlieWorkingRadio = 0L;
        if (tgAcOperatorNolist.size() != 0) {
            tgAcOperatorOnlieRadio = (double) tgOnlineAcOperatorNolist.size() / tgAcOperatorNolist.size();
            tgAcOperatorOfflieRadio = (double) (tgAcOperatorNolist.size() - tgOnlineAcOperatorNolist.size()) / tgAcOperatorNolist.size();
            tgAcOperatorOnlieWorkingRadio = (double) tgWorkingAcOperatorNolist.size() / tgAcOperatorNolist.size();
        }
        String tgAcOperatorOnlieRadioStr = String.format("%.2f", tgAcOperatorOnlieRadio * 100) + "%";
        resultMap.put("tgAcOperatorOnlieRadioStr", tgAcOperatorOnlieRadioStr);
        String tgAcOperatorOfflieRadioStr = String.format("%.2f", tgAcOperatorOfflieRadio * 100) + "%";
        resultMap.put("tgAcOperatorOfflieRadioStr", tgAcOperatorOfflieRadioStr);
        String tgAcOperatorOnlieWorkingRadioStr = String.format("%.2f", tgAcOperatorOnlieWorkingRadio * 100) + "%";
        resultMap.put("tgAcOperatorOnlieWorkingRadioStr", tgAcOperatorOnlieWorkingRadioStr);

        Integer headOnlineAcOperatorNoCount = headOnlineAcOperatorNolist.size();
        Integer tgOnlineAcOperatorNoCount = tgOnlineAcOperatorNolist.size();
        Integer alllineAcOperatorNoCount = headOnlineAcOperatorNoCount + tgOnlineAcOperatorNoCount;
        Integer unDealTaskCount = adsReportMapper.getUnDealTaskCount();
        double pressureRadio = 0L;
        if (alllineAcOperatorNoCount != 0) {
            pressureRadio = (double) unDealTaskCount / alllineAcOperatorNoCount;
        }
        String pressureRadioStr = String.format("%.2f", pressureRadio);
        resultMap.put("alllineAcOperatorNoCount", alllineAcOperatorNoCount);
        resultMap.put("unDealTaskCount", unDealTaskCount);
        //承压指数
        resultMap.put("pressureRadioStr", pressureRadioStr);

        String unitTime = sysParamService.getMonitorAcStatisticsUnitTime();
        List<AcOperatorTaskCount> taskCountList = adsReportMapper.getAcOperatorTaskCount(unitTime);
        List<AcOperatorTaskCount> pressTaskCountList = taskCountList.stream().filter(item -> item.getNum() >= 10).collect(Collectors.toList());
        List<AcOperatorTaskCount> busyTaskCountList = taskCountList.stream().filter(item -> item.getNum() >= 5 && item.getNum() < 10).collect(Collectors.toList());
        List<AcOperatorTaskCount> nomalTaskCountList = taskCountList.stream().filter(item -> item.getNum() >= 1 && item.getNum() < 5).collect(Collectors.toList());
        List<AcOperatorTaskCount> headPressOnlineAcOperatorNolist = pressTaskCountList.stream().filter(item -> headAcOperatorNoSet.contains(item.getStaff_no())).collect(Collectors.toList());
        List<AcOperatorTaskCount> headBusyOnlineAcOperatorNolist = busyTaskCountList.stream().filter(item -> headAcOperatorNoSet.contains(item.getStaff_no())).collect(Collectors.toList());
        List<AcOperatorTaskCount> headNormalOnlineAcOperatorNolist = nomalTaskCountList.stream().filter(item -> headAcOperatorNoSet.contains(item.getStaff_no())).collect(Collectors.toList());
        resultMap.put("headPressOnlineCount", headPressOnlineAcOperatorNolist.size());
        resultMap.put("headBuzyOnlineCount", headBusyOnlineAcOperatorNolist.size());
        resultMap.put("headNormalOnlineCount", headNormalOnlineAcOperatorNolist.size());
        resultMap.put("headIdleOnlineCount", headOnlineAcOperatorNolist.size() - headPressOnlineAcOperatorNolist.size() - headBusyOnlineAcOperatorNolist.size() - headNormalOnlineAcOperatorNolist.size());
        List<AcOperatorTaskCount> tgPressOnlineAcOperatorNolist = pressTaskCountList.stream().filter(item -> tgAcOperatorNoSet.contains(item.getStaff_no())).collect(Collectors.toList());
        List<AcOperatorTaskCount> tgBusyOnlineAcOperatorNolist = busyTaskCountList.stream().filter(item -> tgAcOperatorNoSet.contains(item.getStaff_no())).collect(Collectors.toList());
        List<AcOperatorTaskCount> tgNormalOnlineAcOperatorNolist = nomalTaskCountList.stream().filter(item -> tgAcOperatorNoSet.contains(item.getStaff_no())).collect(Collectors.toList());
        resultMap.put("tgPressOnlineCount", tgPressOnlineAcOperatorNolist.size());
        resultMap.put("tgBuzyOnlineCount", tgBusyOnlineAcOperatorNolist.size());
        resultMap.put("tgNormalOnlineCount", tgNormalOnlineAcOperatorNolist.size());
        resultMap.put("tgIdleOnlineCount", tgOnlineAcOperatorNolist.size() - tgPressOnlineAcOperatorNolist.size() - tgBusyOnlineAcOperatorNolist.size() - tgNormalOnlineAcOperatorNolist.size());
        resultMap.put("offlineAcOperatorNum", list.size() - headOnlineAcOperatorNolist.size() - tgOnlineAcOperatorNolist.size());
        Integer taskCountPerSecond = adsReportMapper.getTaskCountPerSecond(unitTime);
        resultMap.put("taskCountPerSecond", taskCountPerSecond);

        List<AcOperatorTodayTaskCount> todayTaskCountList = adsReportMapper.getAcOperatorTodayTaskCount();
        List<AcOperatorTodayTaskCount> todayTaskCount500List = todayTaskCountList.stream().filter(item -> item.getTotal_num() >= 500).collect(Collectors.toList());
        List<AcOperatorTodayTaskCount> todayTaskCount400List = todayTaskCountList.stream().filter(item -> item.getTotal_num() >= 400 && item.getTotal_num() < 500).collect(Collectors.toList());
        List<AcOperatorTodayTaskCount> todayTaskCount300List = todayTaskCountList.stream().filter(item -> item.getTotal_num() >= 300 && item.getTotal_num() < 400).collect(Collectors.toList());
        List<AcOperatorTodayTaskCount> todayTaskCount200List = todayTaskCountList.stream().filter(item -> item.getTotal_num() >= 200 && item.getTotal_num() < 300).collect(Collectors.toList());
        List<AcOperatorTodayTaskCount> todayTaskCount100List = todayTaskCountList.stream().filter(item -> item.getTotal_num() >= 100 && item.getTotal_num() < 200).collect(Collectors.toList());
        List<AcOperatorTodayTaskCount> todayTaskCount0List = todayTaskCountList.stream().filter(item -> item.getTotal_num() >= 0 && item.getTotal_num() < 100).collect(Collectors.toList());
        List<AcOperatorTodayTaskCount> headTodayTaskCount500List = todayTaskCount500List.stream().filter(item -> headAcOperatorNoSet.contains(item.getStaff_no())).collect(Collectors.toList());
        List<AcOperatorTodayTaskCount> headTodayTaskCount400List = todayTaskCount400List.stream().filter(item -> headAcOperatorNoSet.contains(item.getStaff_no())).collect(Collectors.toList());
        List<AcOperatorTodayTaskCount> headTodayTaskCount300List = todayTaskCount300List.stream().filter(item -> headAcOperatorNoSet.contains(item.getStaff_no())).collect(Collectors.toList());
        List<AcOperatorTodayTaskCount> headTodayTaskCount200List = todayTaskCount200List.stream().filter(item -> headAcOperatorNoSet.contains(item.getStaff_no())).collect(Collectors.toList());
        List<AcOperatorTodayTaskCount> headTodayTaskCount100List = todayTaskCount100List.stream().filter(item -> headAcOperatorNoSet.contains(item.getStaff_no())).collect(Collectors.toList());
        List<AcOperatorTodayTaskCount> headTodayTaskCount0List = todayTaskCount0List.stream().filter(item -> headAcOperatorNoSet.contains(item.getStaff_no())).collect(Collectors.toList());
        Integer headTodayTaskCount = headTodayTaskCount500List.size() + headTodayTaskCount400List.size() + headTodayTaskCount300List.size() + headTodayTaskCount200List.size() + headTodayTaskCount100List.size() + headTodayTaskCount0List.size();
        double headTodayTaskCount500Radio = 0L;
        double headTodayTaskCount400Radio = 0L;
        double headTodayTaskCount300Radio = 0L;
        double headTodayTaskCount200Radio = 0L;
        double headTodayTaskCount100Radio = 0L;
        double headTodayTaskCount0Radio = 0L;
        if (headTodayTaskCount > 0) {
            headTodayTaskCount500Radio = (double) headTodayTaskCount500List.size() / headTodayTaskCount;
            headTodayTaskCount400Radio = (double) headTodayTaskCount400List.size() / headTodayTaskCount;
            headTodayTaskCount300Radio = (double) headTodayTaskCount300List.size() / headTodayTaskCount;
            headTodayTaskCount200Radio = (double) headTodayTaskCount200List.size() / headTodayTaskCount;
            headTodayTaskCount100Radio = (double) headTodayTaskCount100List.size() / headTodayTaskCount;
            headTodayTaskCount0Radio = (double) headTodayTaskCount0List.size() / headTodayTaskCount;
        }
        String headTodayTaskCount500RadioStr = String.format("%.2f", headTodayTaskCount500Radio * 100) + "%";
        String headTodayTaskCount400RadioStr = String.format("%.2f", headTodayTaskCount400Radio * 100) + "%";
        String headTodayTaskCount300RadioStr = String.format("%.2f", headTodayTaskCount300Radio * 100) + "%";
        String headTodayTaskCount200RadioStr = String.format("%.2f", headTodayTaskCount200Radio * 100) + "%";
        String headTodayTaskCount100RadioStr = String.format("%.2f", headTodayTaskCount100Radio * 100) + "%";
        String headTodayTaskCount0RadioStr = String.format("%.2f", headTodayTaskCount0Radio * 100) + "%";
        resultMap.put("headTodayTaskCount500RadioStr", headTodayTaskCount500RadioStr);
        resultMap.put("headTodayTaskCount400RadioStr", headTodayTaskCount400RadioStr);
        resultMap.put("headTodayTaskCount300RadioStr", headTodayTaskCount300RadioStr);
        resultMap.put("headTodayTaskCount200RadioStr", headTodayTaskCount200RadioStr);
        resultMap.put("headTodayTaskCount100RadioStr", headTodayTaskCount100RadioStr);
        resultMap.put("headTodayTaskCount0RadioStr", headTodayTaskCount0RadioStr);
        resultMap.put("headTodayTaskCount500", headTodayTaskCount500List.size());
        resultMap.put("headTodayTaskCount400", headTodayTaskCount400List.size());
        resultMap.put("headTodayTaskCount300", headTodayTaskCount300List.size());
        resultMap.put("headTodayTaskCount200", headTodayTaskCount200List.size());
        resultMap.put("headTodayTaskCount100", headTodayTaskCount100List.size());
        resultMap.put("headTodayTaskCount0", headTodayTaskCount0List.size());

        List<AcOperatorTodayTaskCount> tgTodayTaskCount500List = todayTaskCount500List.stream().filter(item -> tgAcOperatorNoSet.contains(item.getStaff_no())).collect(Collectors.toList());
        List<AcOperatorTodayTaskCount> tgTodayTaskCount400List = todayTaskCount400List.stream().filter(item -> tgAcOperatorNoSet.contains(item.getStaff_no())).collect(Collectors.toList());
        List<AcOperatorTodayTaskCount> tgTodayTaskCount300List = todayTaskCount300List.stream().filter(item -> tgAcOperatorNoSet.contains(item.getStaff_no())).collect(Collectors.toList());
        List<AcOperatorTodayTaskCount> tgTodayTaskCount200List = todayTaskCount200List.stream().filter(item -> tgAcOperatorNoSet.contains(item.getStaff_no())).collect(Collectors.toList());
        List<AcOperatorTodayTaskCount> tgTodayTaskCount100List = todayTaskCount100List.stream().filter(item -> tgAcOperatorNoSet.contains(item.getStaff_no())).collect(Collectors.toList());
        List<AcOperatorTodayTaskCount> tgTodayTaskCount0List = todayTaskCount0List.stream().filter(item -> tgAcOperatorNoSet.contains(item.getStaff_no())).collect(Collectors.toList());
        Integer tgTodayTaskCount = tgTodayTaskCount500List.size() + tgTodayTaskCount400List.size() + tgTodayTaskCount300List.size() + tgTodayTaskCount200List.size() + tgTodayTaskCount100List.size() + tgTodayTaskCount0List.size();
        double tgTodayTaskCount500Radio = 0L;
        double tgTodayTaskCount400Radio = 0L;
        double tgTodayTaskCount300Radio = 0L;
        double tgTodayTaskCount200Radio = 0L;
        double tgTodayTaskCount100Radio = 0L;
        double tgTodayTaskCount0Radio = 0L;
        if (tgTodayTaskCount > 0) {
            tgTodayTaskCount500Radio = (double) tgTodayTaskCount500List.size() / tgTodayTaskCount;
            tgTodayTaskCount400Radio = (double) tgTodayTaskCount400List.size() / tgTodayTaskCount;
            tgTodayTaskCount300Radio = (double) tgTodayTaskCount300List.size() / tgTodayTaskCount;
            tgTodayTaskCount200Radio = (double) tgTodayTaskCount200List.size() / tgTodayTaskCount;
            tgTodayTaskCount100Radio = (double) tgTodayTaskCount100List.size() / tgTodayTaskCount;
            tgTodayTaskCount0Radio = (double) tgTodayTaskCount0List.size() / tgTodayTaskCount;
        }
        String tgTodayTaskCount500RadioStr = String.format("%.2f", tgTodayTaskCount500Radio * 100) + "%";
        String tgTodayTaskCount400RadioStr = String.format("%.2f", tgTodayTaskCount400Radio * 100) + "%";
        String tgTodayTaskCount300RadioStr = String.format("%.2f", tgTodayTaskCount300Radio * 100) + "%";
        String tgTodayTaskCount200RadioStr = String.format("%.2f", tgTodayTaskCount200Radio * 100) + "%";
        String tgTodayTaskCount100RadioStr = String.format("%.2f", tgTodayTaskCount100Radio * 100) + "%";
        String tgTodayTaskCount0RadioStr = String.format("%.2f", tgTodayTaskCount0Radio * 100) + "%";
        resultMap.put("tgTodayTaskCount500RadioStr", tgTodayTaskCount500RadioStr);
        resultMap.put("tgTodayTaskCount400RadioStr", tgTodayTaskCount400RadioStr);
        resultMap.put("tgTodayTaskCount300RadioStr", tgTodayTaskCount300RadioStr);
        resultMap.put("tgTodayTaskCount200RadioStr", tgTodayTaskCount200RadioStr);
        resultMap.put("tgTodayTaskCount100RadioStr", tgTodayTaskCount100RadioStr);
        resultMap.put("tgTodayTaskCount0RadioStr", tgTodayTaskCount0RadioStr);
        resultMap.put("tgTodayTaskCount500", tgTodayTaskCount500List.size());
        resultMap.put("tgTodayTaskCount400", tgTodayTaskCount400List.size());
        resultMap.put("tgTodayTaskCount300", tgTodayTaskCount300List.size());
        resultMap.put("tgTodayTaskCount200", tgTodayTaskCount200List.size());
        resultMap.put("tgTodayTaskCount100", tgTodayTaskCount100List.size());
        resultMap.put("tgTodayTaskCount0", tgTodayTaskCount0List.size());

        List<AcOperatorTodayTaskCount> headTodayTaskCountList = todayTaskCountList.stream().filter(item -> headAcOperatorNoSet.contains(item.getStaff_no())).collect(Collectors.toList());
        List<AcOperatorTodayTaskCount> tgTodayTaskCountList = todayTaskCountList.stream().filter(item -> tgAcOperatorNoSet.contains(item.getStaff_no())).collect(Collectors.toList());

        //排序
        Collections.sort(headTodayTaskCountList, (a, b) -> (Integer.valueOf(b.getTotal_num().toString())).compareTo(Integer.valueOf(a.getTotal_num().toString())));
        Collections.sort(tgTodayTaskCountList, (a, b) -> (Integer.valueOf(b.getTotal_num().toString())).compareTo(Integer.valueOf(a.getTotal_num().toString())));
        //取top5
        List<AcOperatorTodayTaskCount> headTodayTaskCountTop5List = null;
        if (headTodayTaskCountList.size() > 5) {
            headTodayTaskCountTop5List = headTodayTaskCountList.subList(0, 5);
        } else {
            headTodayTaskCountTop5List = headTodayTaskCountList;
        }
        resultMap.put("headTodayTaskCountTop5List", headTodayTaskCountTop5List);
        List<AcOperatorTodayTaskCount> tgTodayTaskCountTop5List = null;
        if (tgTodayTaskCountList.size() > 5) {
            tgTodayTaskCountTop5List = tgTodayTaskCountList.subList(0, 5);
        } else {
            tgTodayTaskCountTop5List = tgTodayTaskCountList;
        }
        resultMap.put("tgTodayTaskCountTop5List", tgTodayTaskCountTop5List);
        return resultMap;
    }

    /**
     * 获取工作队列信息
     *
     * @return List<com.cairh.cpe.dispatch.common.service.model.WorkingUserInfo>
     * <AUTHOR>
     * @since 2023/11/8 09:59
     */
    public List<WorkingUserInfo> getWorkingUserInfoList() {
        Set<ZSetOperations.TypedTuple<String>> typedTuples = redisService.zSetGetByScoreWithAll(CacheKeyConfig.DISPATCH_WORKING_OPERATOR_QUEUE);
        if (CollectionUtils.isEmpty(typedTuples)) {
            return Collections.emptyList();
        }
        return typedTuples.stream()
                .map(typedTuple -> WorkingUserInfo.builder().operatorNo(typedTuple.getValue()).workingTime(LocalDateTime.ofEpochSecond(Objects.requireNonNull(typedTuple.getScore()).longValue() / Constant.ONE_THOUSAND, Constant.ZERO, ZoneOffset.UTC)).build())
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> queryADS9002() {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        List<LevelDispatchTask> levelDispatchTaskList = adsReportMapper.getLevelDispatchTask();
        List<LevelDispatchTask> level0DispatchTaskList = levelDispatchTaskList.stream().filter(item -> StringUtils.equals(item.getQueue_level(), "1")).collect(Collectors.toList());
        List<LevelDispatchTask> level1DispatchTaskList = levelDispatchTaskList.stream().filter(item -> StringUtils.equals(item.getQueue_level(), "2")).collect(Collectors.toList());
        List<LevelDispatchTask> level2DispatchTaskList = levelDispatchTaskList.stream().filter(item -> StringUtils.equals(item.getQueue_level(), "3")).collect(Collectors.toList());
        List<LevelDispatchTask> level3DispatchTaskList = levelDispatchTaskList.stream().filter(item -> StringUtils.equals(item.getQueue_level(), "4")).collect(Collectors.toList());
        List<LevelDispatchTask> level4DispatchTaskList = levelDispatchTaskList.stream().filter(item -> StringUtils.equals(item.getQueue_level(), "5")).collect(Collectors.toList());

        List<LevelDispatchTask> level0AuditDispatchTaskList = level0DispatchTaskList.stream().filter(item -> StringUtils.equals(item.getTask_type(), "5")).collect(Collectors.toList());
        List<LevelDispatchTask> level0ReviewDispatchTaskList = level0DispatchTaskList.stream().filter(item -> !StringUtils.equals(item.getTask_type(), "5")).collect(Collectors.toList());
        List<LevelDispatchTask> level1AuditDispatchTaskList = level1DispatchTaskList.stream().filter(item -> StringUtils.equals(item.getTask_type(), "5")).collect(Collectors.toList());
        List<LevelDispatchTask> level1ReviewDispatchTaskList = level1DispatchTaskList.stream().filter(item -> !StringUtils.equals(item.getTask_type(), "5")).collect(Collectors.toList());
        List<LevelDispatchTask> level2AuditDispatchTaskList = level2DispatchTaskList.stream().filter(item -> StringUtils.equals(item.getTask_type(), "5")).collect(Collectors.toList());
        List<LevelDispatchTask> level2ReviewDispatchTaskList = level2DispatchTaskList.stream().filter(item -> !StringUtils.equals(item.getTask_type(), "5")).collect(Collectors.toList());
        List<LevelDispatchTask> level3AuditDispatchTaskList = level3DispatchTaskList.stream().filter(item -> StringUtils.equals(item.getTask_type(), "5")).collect(Collectors.toList());
        List<LevelDispatchTask> level3ReviewDispatchTaskList = level3DispatchTaskList.stream().filter(item -> !StringUtils.equals(item.getTask_type(), "5")).collect(Collectors.toList());
        List<LevelDispatchTask> level4AuditDispatchTaskList = level4DispatchTaskList.stream().filter(item -> StringUtils.equals(item.getTask_type(), "5")).collect(Collectors.toList());
        List<LevelDispatchTask> level4ReviewDispatchTaskList = level4DispatchTaskList.stream().filter(item -> !StringUtils.equals(item.getTask_type(), "5")).collect(Collectors.toList());

        Set<String> mobileTelSet = redisService.setGetAll(WHITE_LIST_MOBILE_TEL + "24");
        Set<String> idNoSet = redisService.setGetAll(WHITE_LIST_ID_NO + "24");
        List<LevelDispatchTask> level0AuditTopDispatchTaskList = level0AuditDispatchTaskList.stream().filter(item -> !StringUtils.equals(item.getTop_pos(), "0") && StringUtils.isBlank(item.getSerial_id()) && !mobileTelSet.contains(item.getMobile_tel()) && !idNoSet.contains(item.getId_no())).collect(Collectors.toList());
        List<LevelDispatchTask> level0ReviewTopDispatchTaskList = level0ReviewDispatchTaskList.stream().filter(item -> !StringUtils.equals(item.getTop_pos(), "0") && StringUtils.isBlank(item.getSerial_id()) && !mobileTelSet.contains(item.getMobile_tel()) && !idNoSet.contains(item.getId_no())).collect(Collectors.toList());
        List<LevelDispatchTask> level1AuditTopDispatchTaskList = level1AuditDispatchTaskList.stream().filter(item -> !StringUtils.equals(item.getTop_pos(), "0") && StringUtils.isBlank(item.getSerial_id()) && !mobileTelSet.contains(item.getMobile_tel()) && !idNoSet.contains(item.getId_no())).collect(Collectors.toList());
        List<LevelDispatchTask> level1ReviewTopDispatchTaskList = level1ReviewDispatchTaskList.stream().filter(item -> !StringUtils.equals(item.getTop_pos(), "0") && StringUtils.isBlank(item.getSerial_id()) && !mobileTelSet.contains(item.getMobile_tel()) && !idNoSet.contains(item.getId_no())).collect(Collectors.toList());
        List<LevelDispatchTask> level2AuditTopDispatchTaskList = level2AuditDispatchTaskList.stream().filter(item -> !StringUtils.equals(item.getTop_pos(), "0") && StringUtils.isBlank(item.getSerial_id()) && !mobileTelSet.contains(item.getMobile_tel()) && !idNoSet.contains(item.getId_no())).collect(Collectors.toList());
        List<LevelDispatchTask> level2ReviewTopDispatchTaskList = level2ReviewDispatchTaskList.stream().filter(item -> !StringUtils.equals(item.getTop_pos(), "0") && StringUtils.isBlank(item.getSerial_id()) && !mobileTelSet.contains(item.getMobile_tel()) && !idNoSet.contains(item.getId_no())).collect(Collectors.toList());
        List<LevelDispatchTask> level3AuditTopDispatchTaskList = level3AuditDispatchTaskList.stream().filter(item -> !StringUtils.equals(item.getTop_pos(), "0") && StringUtils.isBlank(item.getSerial_id()) && !mobileTelSet.contains(item.getMobile_tel()) && !idNoSet.contains(item.getId_no())).collect(Collectors.toList());
        List<LevelDispatchTask> level3ReviewTopDispatchTaskList = level3ReviewDispatchTaskList.stream().filter(item -> !StringUtils.equals(item.getTop_pos(), "0") && StringUtils.isBlank(item.getSerial_id()) && !mobileTelSet.contains(item.getMobile_tel()) && !idNoSet.contains(item.getId_no())).collect(Collectors.toList());
        List<LevelDispatchTask> level4AuditTopDispatchTaskList = level4AuditDispatchTaskList.stream().filter(item -> !StringUtils.equals(item.getTop_pos(), "0") && StringUtils.isBlank(item.getSerial_id()) && !mobileTelSet.contains(item.getMobile_tel()) && !idNoSet.contains(item.getId_no())).collect(Collectors.toList());
        List<LevelDispatchTask> level4ReviewTopDispatchTaskList = level4ReviewDispatchTaskList.stream().filter(item -> !StringUtils.equals(item.getTop_pos(), "0") && StringUtils.isBlank(item.getSerial_id()) && !mobileTelSet.contains(item.getMobile_tel()) && !idNoSet.contains(item.getId_no())).collect(Collectors.toList());

        List<LevelDispatchTask> level0AuditWhiteListDispatchTaskList = level0AuditDispatchTaskList.stream().filter(item -> StringUtils.isNotBlank(item.getSerial_id())).collect(Collectors.toList());
        List<LevelDispatchTask> level0ReviewWhiteListDispatchTaskList = level0ReviewDispatchTaskList.stream().filter(item -> StringUtils.isNotBlank(item.getSerial_id())).collect(Collectors.toList());
        List<LevelDispatchTask> level1AuditWhiteListDispatchTaskList = level1AuditDispatchTaskList.stream().filter(item -> StringUtils.isNotBlank(item.getSerial_id())).collect(Collectors.toList());
        List<LevelDispatchTask> level1ReviewWhiteListDispatchTaskList = level1ReviewDispatchTaskList.stream().filter(item -> StringUtils.isNotBlank(item.getSerial_id())).collect(Collectors.toList());
        List<LevelDispatchTask> level2AuditWhiteListDispatchTaskList = level2AuditDispatchTaskList.stream().filter(item -> StringUtils.isNotBlank(item.getSerial_id())).collect(Collectors.toList());
        List<LevelDispatchTask> level2ReviewWhiteListDispatchTaskList = level2ReviewDispatchTaskList.stream().filter(item -> StringUtils.isNotBlank(item.getSerial_id())).collect(Collectors.toList());
        List<LevelDispatchTask> level3AuditWhiteListDispatchTaskList = level3AuditDispatchTaskList.stream().filter(item -> StringUtils.isNotBlank(item.getSerial_id())).collect(Collectors.toList());
        List<LevelDispatchTask> level3ReviewWhiteListDispatchTaskList = level3ReviewDispatchTaskList.stream().filter(item -> StringUtils.isNotBlank(item.getSerial_id())).collect(Collectors.toList());
        List<LevelDispatchTask> level4AuditWhiteListDispatchTaskList = level4AuditDispatchTaskList.stream().filter(item -> StringUtils.isNotBlank(item.getSerial_id())).collect(Collectors.toList());
        List<LevelDispatchTask> level4ReviewWhiteListDispatchTaskList = level4ReviewDispatchTaskList.stream().filter(item -> StringUtils.isNotBlank(item.getSerial_id())).collect(Collectors.toList());

        List<LevelDispatchTask> level0AuditWhiteListPreSetDispatchTaskList = level0AuditDispatchTaskList.stream().filter(item -> mobileTelSet.contains(item.getMobile_tel()) || idNoSet.contains(item.getId_no())).collect(Collectors.toList());
        List<LevelDispatchTask> level0ReviewWhiteListPreSetDispatchTaskList = level0ReviewDispatchTaskList.stream().filter(item -> mobileTelSet.contains(item.getMobile_tel()) || idNoSet.contains(item.getId_no())).collect(Collectors.toList());
        List<LevelDispatchTask> level1AuditWhiteListPreSetDispatchTaskList = level1AuditDispatchTaskList.stream().filter(item -> mobileTelSet.contains(item.getMobile_tel()) || idNoSet.contains(item.getId_no())).collect(Collectors.toList());
        List<LevelDispatchTask> level1ReviewWhiteListPreSetDispatchTaskList = level1ReviewDispatchTaskList.stream().filter(item -> mobileTelSet.contains(item.getMobile_tel()) || idNoSet.contains(item.getId_no())).collect(Collectors.toList());
        List<LevelDispatchTask> level2AuditWhiteListPreSetDispatchTaskList = level2AuditDispatchTaskList.stream().filter(item -> mobileTelSet.contains(item.getMobile_tel()) || idNoSet.contains(item.getId_no())).collect(Collectors.toList());
        List<LevelDispatchTask> level2ReviewWhiteListPreSetDispatchTaskList = level2ReviewDispatchTaskList.stream().filter(item -> mobileTelSet.contains(item.getMobile_tel()) || idNoSet.contains(item.getId_no())).collect(Collectors.toList());
        List<LevelDispatchTask> level3AuditWhiteListPreSetDispatchTaskList = level3AuditDispatchTaskList.stream().filter(item -> mobileTelSet.contains(item.getMobile_tel()) || idNoSet.contains(item.getId_no())).collect(Collectors.toList());
        List<LevelDispatchTask> level3ReviewWhiteListPreSetDispatchTaskList = level3ReviewDispatchTaskList.stream().filter(item -> mobileTelSet.contains(item.getMobile_tel()) || idNoSet.contains(item.getId_no())).collect(Collectors.toList());
        List<LevelDispatchTask> level4AuditWhiteListPreSetDispatchTaskList = level4AuditDispatchTaskList.stream().filter(item -> mobileTelSet.contains(item.getMobile_tel()) || idNoSet.contains(item.getId_no())).collect(Collectors.toList());
        List<LevelDispatchTask> level4ReviewWhiteListPreSetDispatchTaskList = level4ReviewDispatchTaskList.stream().filter(item -> mobileTelSet.contains(item.getMobile_tel()) || idNoSet.contains(item.getId_no())).collect(Collectors.toList());

        List<LevelDispatchTask> level0AuditTotalDispatchTaskList = level0AuditDispatchTaskList.stream().filter(item -> mobileTelSet.contains(item.getMobile_tel()) || idNoSet.contains(item.getId_no()) || !StringUtils.equals(item.getTop_pos(), "0") || StringUtils.isNotBlank(item.getSerial_id())).collect(Collectors.toList());
        List<LevelDispatchTask> level0ReviewTotalDispatchTaskList = level0ReviewDispatchTaskList.stream().filter(item -> mobileTelSet.contains(item.getMobile_tel()) || idNoSet.contains(item.getId_no()) || !StringUtils.equals(item.getTop_pos(), "0") || StringUtils.isNotBlank(item.getSerial_id())).collect(Collectors.toList());
        List<LevelDispatchTask> level1AuditTotalDispatchTaskList = level1AuditDispatchTaskList.stream().filter(item -> mobileTelSet.contains(item.getMobile_tel()) || idNoSet.contains(item.getId_no()) || !StringUtils.equals(item.getTop_pos(), "0") || StringUtils.isNotBlank(item.getSerial_id())).collect(Collectors.toList());
        List<LevelDispatchTask> level1ReviewTotalDispatchTaskList = level1ReviewDispatchTaskList.stream().filter(item -> mobileTelSet.contains(item.getMobile_tel()) || idNoSet.contains(item.getId_no()) || !StringUtils.equals(item.getTop_pos(), "0") || StringUtils.isNotBlank(item.getSerial_id())).collect(Collectors.toList());
        List<LevelDispatchTask> level2AuditTotalDispatchTaskList = level2AuditDispatchTaskList.stream().filter(item -> mobileTelSet.contains(item.getMobile_tel()) || idNoSet.contains(item.getId_no()) || !StringUtils.equals(item.getTop_pos(), "0") || StringUtils.isNotBlank(item.getSerial_id())).collect(Collectors.toList());
        List<LevelDispatchTask> level2ReviewTotalDispatchTaskList = level2ReviewDispatchTaskList.stream().filter(item -> mobileTelSet.contains(item.getMobile_tel()) || idNoSet.contains(item.getId_no()) || !StringUtils.equals(item.getTop_pos(), "0") || StringUtils.isNotBlank(item.getSerial_id())).collect(Collectors.toList());
        List<LevelDispatchTask> level3AuditTotalDispatchTaskList = level3AuditDispatchTaskList.stream().filter(item -> mobileTelSet.contains(item.getMobile_tel()) || idNoSet.contains(item.getId_no()) || !StringUtils.equals(item.getTop_pos(), "0") || StringUtils.isNotBlank(item.getSerial_id())).collect(Collectors.toList());
        List<LevelDispatchTask> level3ReviewTotalDispatchTaskList = level3ReviewDispatchTaskList.stream().filter(item -> mobileTelSet.contains(item.getMobile_tel()) || idNoSet.contains(item.getId_no()) || !StringUtils.equals(item.getTop_pos(), "0") || StringUtils.isNotBlank(item.getSerial_id())).collect(Collectors.toList());
        List<LevelDispatchTask> level4AuditTotalDispatchTaskList = level4AuditDispatchTaskList.stream().filter(item -> mobileTelSet.contains(item.getMobile_tel()) || idNoSet.contains(item.getId_no()) || !StringUtils.equals(item.getTop_pos(), "0") || StringUtils.isNotBlank(item.getSerial_id())).collect(Collectors.toList());
        List<LevelDispatchTask> level4ReviewTotalDispatchTaskList = level4ReviewDispatchTaskList.stream().filter(item -> mobileTelSet.contains(item.getMobile_tel()) || idNoSet.contains(item.getId_no()) || !StringUtils.equals(item.getTop_pos(), "0") || StringUtils.isNotBlank(item.getSerial_id())).collect(Collectors.toList());

        resultMap.put("level0AuditTotal", level0AuditTotalDispatchTaskList.size());
        resultMap.put("level0AuditTop", level0AuditTopDispatchTaskList.size());
        resultMap.put("level0AuditWhiteList", level0AuditWhiteListDispatchTaskList.size());
        resultMap.put("level0AuditPreSetWhiteList", level0AuditWhiteListPreSetDispatchTaskList.size());
        resultMap.put("level0ReviewTotal", level0ReviewTotalDispatchTaskList.size());
        resultMap.put("level0ReviewTop", level0ReviewTopDispatchTaskList.size());
        resultMap.put("level0ReviewWhiteList", level0ReviewWhiteListDispatchTaskList.size());
        resultMap.put("level0ReviewPreSetWhiteList", level0ReviewWhiteListPreSetDispatchTaskList.size());

        resultMap.put("level1AuditTotal", level1AuditTotalDispatchTaskList.size());
        resultMap.put("level1AuditTop", level1AuditTopDispatchTaskList.size());
        resultMap.put("level1AuditWhiteList", level1AuditWhiteListDispatchTaskList.size());
        resultMap.put("level1AuditPreSetWhiteList", level1AuditWhiteListPreSetDispatchTaskList.size());
        resultMap.put("level1ReviewTotal", level1ReviewTotalDispatchTaskList.size());
        resultMap.put("level1ReviewTop", level1ReviewTopDispatchTaskList.size());
        resultMap.put("level1ReviewWhiteList", level1ReviewWhiteListDispatchTaskList.size());
        resultMap.put("level1ReviewPreSetWhiteList", level1ReviewWhiteListPreSetDispatchTaskList.size());

        resultMap.put("level2AuditTotal", level2AuditTotalDispatchTaskList.size());
        resultMap.put("level2AuditTop", level2AuditTopDispatchTaskList.size());
        resultMap.put("level2AuditWhiteList", level2AuditWhiteListDispatchTaskList.size());
        resultMap.put("level2AuditPreSetWhiteList", level2AuditWhiteListPreSetDispatchTaskList.size());
        resultMap.put("level2ReviewTotal", level2ReviewTotalDispatchTaskList.size());
        resultMap.put("level2ReviewTop", level2ReviewTopDispatchTaskList.size());
        resultMap.put("level2ReviewWhiteList", level2ReviewWhiteListDispatchTaskList.size());
        resultMap.put("level2ReviewPreSetWhiteList", level2ReviewWhiteListPreSetDispatchTaskList.size());

        resultMap.put("level3AuditTotal", level3AuditTotalDispatchTaskList.size());
        resultMap.put("level3AuditTop", level3AuditTopDispatchTaskList.size());
        resultMap.put("level3AuditWhiteList", level3AuditWhiteListDispatchTaskList.size());
        resultMap.put("level3AuditPreSetWhiteList", level3AuditWhiteListPreSetDispatchTaskList.size());
        resultMap.put("level3ReviewTotal", level3ReviewTotalDispatchTaskList.size());
        resultMap.put("level3ReviewTop", level3ReviewTopDispatchTaskList.size());
        resultMap.put("level3ReviewWhiteList", level3ReviewWhiteListDispatchTaskList.size());
        resultMap.put("level3ReviewPreSetWhiteList", level3ReviewWhiteListPreSetDispatchTaskList.size());

        resultMap.put("level4AuditTotal", level4AuditTotalDispatchTaskList.size());
        resultMap.put("level4AuditTop", level4AuditTopDispatchTaskList.size());
        resultMap.put("level4AuditWhiteList", level4AuditWhiteListDispatchTaskList.size());
        resultMap.put("level4AuditPreSetWhiteList", level4AuditWhiteListPreSetDispatchTaskList.size());
        resultMap.put("level4ReviewTotal", level4ReviewTotalDispatchTaskList.size());
        resultMap.put("level4ReviewTop", level4ReviewTopDispatchTaskList.size());
        resultMap.put("level4ReviewWhiteList", level4ReviewWhiteListDispatchTaskList.size());
        resultMap.put("level4ReviewPreSetWhiteList", level4ReviewWhiteListPreSetDispatchTaskList.size());

        List<LevelDispatchTask> topDispatchTaskList = levelDispatchTaskList.stream().filter(item -> !StringUtils.equals(item.getTop_pos(), "0") && StringUtils.isBlank(item.getSerial_id()) && !mobileTelSet.contains(item.getMobile_tel()) && !idNoSet.contains(item.getId_no())).collect(Collectors.toList());
        List<LevelDispatchTask> preSetDispatchTaskList = levelDispatchTaskList.stream().filter(item -> StringUtils.isNotBlank(item.getSerial_id())).collect(Collectors.toList());
        List<LevelDispatchTask> whiteListDispatchTaskList = levelDispatchTaskList.stream().filter(item -> mobileTelSet.contains(item.getMobile_tel()) || idNoSet.contains(item.getId_no())).collect(Collectors.toList());
        resultMap.put("topTotal", topDispatchTaskList.size());
        resultMap.put("preSetWhiteListTotal", preSetDispatchTaskList.size());
        resultMap.put("whiteListTotal", whiteListDispatchTaskList.size());
        return resultMap;
    }

    @Override
    public Map<String, Object> queryADS9003() {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        List<DispatchTaskOverview> dispatchTaskOverviewList = adsReportMapper.getDispatchTaskOverview();
        if (CollectionUtils.isNotEmpty(dispatchTaskOverviewList)) {
            resultMap.put("noqueueSum", dispatchTaskOverviewList.get(0).getNoqueue_sum());
            resultMap.put("queueSum", dispatchTaskOverviewList.get(0).getQueue_sum());
            resultMap.put("nodealSum", dispatchTaskOverviewList.get(0).getNodeal_sum());
            resultMap.put("dealingSum", dispatchTaskOverviewList.get(0).getDealing_sum());
        } else {
            resultMap.put("noqueueSum", 0);
            resultMap.put("queueSum", 0);
            resultMap.put("nodealSum", 0);
            resultMap.put("dealingSum", 0);
        }

        List<FinshDispatchTaskWithFailCount> finshDispatchTaskWithFailCountList = adsReportMapper.getFinshDispatchTaskWithFailCount();
        int dispatch5TimesNum = finshDispatchTaskWithFailCountList.stream().filter(item -> item.getDispatch_fail_count() >= 4).mapToInt(FinshDispatchTaskWithFailCount::getDispatch_fail_count_task_count).sum();
        int dispatch3TimesNum = finshDispatchTaskWithFailCountList.stream().filter(item -> item.getDispatch_fail_count() >= 2).mapToInt(FinshDispatchTaskWithFailCount::getDispatch_fail_count_task_count).sum();
        int dispatch2TimesNum = finshDispatchTaskWithFailCountList.stream().filter(item -> item.getDispatch_fail_count() == 1).mapToInt(FinshDispatchTaskWithFailCount::getDispatch_fail_count_task_count).sum();
        int dispatch1TimesNum = finshDispatchTaskWithFailCountList.stream().filter(item -> item.getDispatch_fail_count() == 0).mapToInt(FinshDispatchTaskWithFailCount::getDispatch_fail_count_task_count).sum();
        resultMap.put("dispatch5Times", dispatch5TimesNum);
        resultMap.put("dispatch3Times", dispatch3TimesNum);
        resultMap.put("dispatch2Times", dispatch2TimesNum);
        resultMap.put("dispatch1Times", dispatch1TimesNum);
        int totalCount = finshDispatchTaskWithFailCountList.stream().mapToInt(FinshDispatchTaskWithFailCount::getDispatch_fail_count_task_count).sum();
        String dispatch5TimesRadioStr = "0";
        String dispatch3TimesRadioStr = "0";
        String dispatch2TimesRadioStr = "0";
        String dispatch1TimesRadioStr = "0";
        if (totalCount > 0) {
            dispatch5TimesRadioStr = getRate(dispatch5TimesNum, totalCount);
            dispatch3TimesRadioStr = getRate(dispatch3TimesNum, totalCount);
            dispatch2TimesRadioStr = getRate(dispatch2TimesNum, totalCount);
            dispatch1TimesRadioStr = getRate(dispatch1TimesNum, totalCount);
        }
        resultMap.put("dispatch5TimesRadioStr", dispatch5TimesRadioStr);
        resultMap.put("dispatch3TimesRadioStr", dispatch3TimesRadioStr);
        resultMap.put("dispatch2TimesRadioStr", dispatch2TimesRadioStr);
        resultMap.put("dispatch1TimesRadioStr", dispatch1TimesRadioStr);
        resultMap.put("dispatch5TimesRadioWithPercentStr", dispatch5TimesRadioStr + "%");
        resultMap.put("dispatch3TimesRadioWithPercentStr", dispatch3TimesRadioStr + "%");
        resultMap.put("dispatch2TimesRadioWithPercentStr", dispatch2TimesRadioStr + "%");
        resultMap.put("dispatch1TimesRadioWithPercentStr", dispatch1TimesRadioStr + "%");
        return resultMap;
    }

    @Override
    public Map<String, Object> queryADS9004(String start_time, String end_time) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        if (DateUtil.isDayAfter(start_time, end_time)) {
            return resultMap;
        }
        boolean flag = true;
        while (flag) {
            resultMap = dealOneDayOperatorOnlineTime(start_time);
            start_time = DateUtil.getNextDate(start_time);
            if (DateUtil.isDayAfter(start_time, end_time)) {
                flag = false;
            }
        }
        return resultMap;
    }

    @Override
    public Map<String, Object> queryADS9005() {
        Map<String, Object> resultMap = adsReportMapper.getHfDispatchTaskOverview();
        return resultMap;
    }

    @Override
    public OperatorOnlineOverviewResp queryADS9006() {
        //所有坐席数
        Integer allOperatorNum = adsReportMapper.getAllOperatorNum();
        if (allOperatorNum == Constant.ZERO) {
            OperatorOnlineOverviewResp operatorOnlineOverviewResp = defaultRespInitService.getEmptryOperatorOnlineOverviewResp();
            return operatorOnlineOverviewResp;
        }
        //上线过的坐席数量
        Integer onlineOperatorNum = adsReportMapper.getTodayOnlineOperatorNum();
        if (onlineOperatorNum == Constant.ZERO) {
            OperatorOnlineOverviewResp operatorOnlineOverviewResp = defaultRespInitService.getEmptryOperatorOnlineOverviewResp();
            return operatorOnlineOverviewResp;
        }
        OperatorOnlineOverviewResp result = new OperatorOnlineOverviewResp();
        //今日上线人数
        result.setOnlineOperatorNum(onlineOperatorNum);
        //在线率=上线人数/所有的坐席数
        result.setOnlineRate(NumberCalculateUtil.calculateRate(result.getOnlineOperatorNum(), allOperatorNum).toString());
        //在线派单人数
        Map<Object, Object> allWorkingOperatorMap = redisService.hashGetAll(QueryStatisticsDataConstant.ALL_CHANNEL_WORKING_OPERATOR_QUEUE);
        int onlineDispatchOperatorNum = CollUtil.isEmpty(allWorkingOperatorMap) ? 0 : allWorkingOperatorMap.size();
        result.setOnlineDispatchOperatorNum(onlineDispatchOperatorNum);
        //派单率=在线派单人数/所有的坐席数
        result.setOnlineDispatchRate(NumberCalculateUtil.calculateRate(result.getOnlineDispatchOperatorNum(), onlineOperatorNum).toString());
        //离线人数
        result.setOfflineOperatorNum(result.getOnlineOperatorNum() - result.getOnlineDispatchOperatorNum());
        //离线率=离线人数/所有的坐席数
        result.setOfflineRate(NumberCalculateUtil.calculateRate(result.getOfflineOperatorNum(), onlineOperatorNum).toString());
        return result;
    }

    /**
     * 统计今天上过线的坐席的产能分布
     * 1. 查询今天上过线的坐席
     * 2. 查询坐席的产能
     * 3. 统计坐席的产能分布
     *
     * @return
     */
    @Override
    public TodayOperatorCapacityDistributionResp queryADS9007() {
        List<String> onlineOperatorList = adsReportMapper.getTodayOnlineOperatorNoList();
        if (CollUtil.isEmpty(onlineOperatorList)) {
            return defaultRespInitService.getEmptryTodayOperatorCapacityDistributionResp();
        }
        List<OperatorTaskHandleNum> todayOnlineOperatorTaskHandleNum = adsReportMapper.getTodayOnlineOperatorTaskHandleNum();
        if (CollUtil.isEmpty(todayOnlineOperatorTaskHandleNum)) {
            return defaultRespInitService.getEmptryTodayOperatorCapacityDistributionResp();
        }
        TodayOperatorCapacityDistributionResp result = defaultRespInitService.getEmptryTodayOperatorCapacityDistributionResp();
        List<String> haveTaskOperatorNos = todayOnlineOperatorTaskHandleNum.stream().map(ele -> ele.getOperator_no()).collect(Collectors.toList());

        //完成任务=0的坐席
        List<String> noTaskOperatorNos = onlineOperatorList.stream().filter(ele -> !haveTaskOperatorNos.contains(ele)).collect(Collectors.toList());
        result.setLevel_1_number(noTaskOperatorNos.size());

        //统计任务处理的分布情况
        for (OperatorTaskHandleNum operatorTaskHandleNum : todayOnlineOperatorTaskHandleNum) {
            Integer taskHandleNum = operatorTaskHandleNum.getTask_handle_num();
            if (taskHandleNum >= 0 && taskHandleNum < 60) {
                result.setLevel_1_number(result.getLevel_1_number() + 1);
            } else if (taskHandleNum >= 60 && taskHandleNum < 100) {
                result.setLevel_2_number(result.getLevel_2_number() + 1);
            } else if (taskHandleNum >= 100 && taskHandleNum < 160) {
                result.setLevel_3_number(result.getLevel_3_number() + 1);
            } else if (taskHandleNum >= 160 && taskHandleNum < 200) {
                result.setLevel_4_number(result.getLevel_4_number() + 1);
            } else if (taskHandleNum >= 200 && taskHandleNum < 260) {
                result.setLevel_5_number(result.getLevel_5_number() + 1);
            } else if (taskHandleNum >= 260 && taskHandleNum < 300) {
                result.setLevel_6_number(result.getLevel_6_number() + 1);
            } else {
                result.setLevel_7_number(result.getLevel_7_number() + 1);  // 300笔以上
            }
        }

        int allOnlineOperatorNum = onlineOperatorList.size();
        result.setLevel_1_rate(NumberCalculateUtil.calculateRate(result.getLevel_1_number(), allOnlineOperatorNum).toString());
        result.setLevel_2_rate(NumberCalculateUtil.calculateRate(result.getLevel_2_number(), allOnlineOperatorNum).toString());
        result.setLevel_3_rate(NumberCalculateUtil.calculateRate(result.getLevel_3_number(), allOnlineOperatorNum).toString());
        result.setLevel_4_rate(NumberCalculateUtil.calculateRate(result.getLevel_4_number(), allOnlineOperatorNum).toString());
        result.setLevel_5_rate(NumberCalculateUtil.calculateRate(result.getLevel_5_number(), allOnlineOperatorNum).toString());
        result.setLevel_6_rate(NumberCalculateUtil.calculateRate(result.getLevel_6_number(), allOnlineOperatorNum).toString());
        result.setLevel_7_rate(NumberCalculateUtil.calculateRate(result.getLevel_7_number(), allOnlineOperatorNum).toString());

        return result;
    }

    @Override
    public TodayFollowUpTaskExecutionDataResp queryADS9008() {
        TodayFollowUpTaskExecutionDataResp result = new TodayFollowUpTaskExecutionDataResp();

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfDay = now.with(LocalTime.MIN);
        LocalDateTime endOfDay = now.with(LocalTime.MAX);

        //待处理 (未首响)
        LambdaQueryWrapper<FollowupApply> applyWrapper = new LambdaQueryWrapper<>();
        applyWrapper.in(FollowupApply::getStatus, Lists.newArrayList(ApplyFlowStatus.APPLY_ACCEPT.getCode(), ApplyFlowStatus.APPLY_PLAN_PROCESSING.getCode(),
                        ApplyFlowStatus.APPLY_PENDING_PROCESSING.getCode(), ApplyFlowStatus.APPLY_IN_PROCESSING.getCode(), ApplyFlowStatus.APPLY_PUSH_OMNICHANNEL.getCode(),
                        ApplyFlowStatus.APPLY_PENDING_VERIFY.getCode(), ApplyFlowStatus.APPLY_AGAIN_VERIFY.getCode()))
                .ge(FollowupApply::getCreate_datetime, startOfDay)
                .le(FollowupApply::getCreate_datetime, endOfDay)
                .eq(FollowupApply::getCall_total_num, 0);
        result.setUnrespondedToBeProcessedTask(followupApplyService.count(applyWrapper));

        //待处理 (已首响)
        LambdaQueryWrapper<FollowupApply> nonzeroApplyWrapper = new LambdaQueryWrapper<>();
        nonzeroApplyWrapper.in(FollowupApply::getStatus, Lists.newArrayList(ApplyFlowStatus.APPLY_ACCEPT.getCode(), ApplyFlowStatus.APPLY_PLAN_PROCESSING.getCode(),
                        ApplyFlowStatus.APPLY_PENDING_PROCESSING.getCode(), ApplyFlowStatus.APPLY_IN_PROCESSING.getCode(), ApplyFlowStatus.APPLY_PUSH_OMNICHANNEL.getCode(),
                        ApplyFlowStatus.APPLY_PENDING_VERIFY.getCode(), ApplyFlowStatus.APPLY_AGAIN_VERIFY.getCode()))
                .ge(FollowupApply::getCreate_datetime, startOfDay)
                .le(FollowupApply::getCreate_datetime, endOfDay)
                .gt(FollowupApply::getCall_total_num, 0);
        result.setRespondedToBeProcessedTask(followupApplyService.count(nonzeroApplyWrapper));

        //待处理任务总量
        result.setToBeProcessedTaskTotal(result.getUnrespondedToBeProcessedTask() + result.getRespondedToBeProcessedTask());

        //任务生成总量
        LambdaQueryWrapper<FollowupTask> createQueryWrapper = new LambdaQueryWrapper<>();
        createQueryWrapper.ge(FollowupTask::getCreate_datetime, startOfDay)
                .le(FollowupTask::getCreate_datetime, endOfDay);
        result.setTaskGeneratedTotal(followupTaskMapper.selectCount(createQueryWrapper));


        //任务下发总量
        LambdaQueryWrapper<FollowupTask> pushQueryWrapper = new LambdaQueryWrapper<>();
        pushQueryWrapper.ge(FollowupTask::getPush_datetime, startOfDay)
                .le(FollowupTask::getPush_datetime, endOfDay);
        result.setTaskIssuedTotal(followupTaskMapper.selectCount(pushQueryWrapper));

        //已处理任务量
        LambdaQueryWrapper<FollowupTask> isHandleTaskQueryWrapper = new LambdaQueryWrapper<>();
        isHandleTaskQueryWrapper.ge(FollowupTask::getPush_datetime, startOfDay)
                .le(FollowupTask::getPush_datetime, endOfDay)
                .in(FollowupTask::getTask_status, Lists.newArrayList(
                        TaskFlowStatus.TASK_PENDING_VERIFIED.getCode(),
                        TaskFlowStatus.TASK_CONFIRM_VERIFIED.getCode(),
                        TaskFlowStatus.TASK_FINISHED.getCode(),
                        TaskFlowStatus.TASK_CANCEL.getCode()
                ));
        result.setProcessedTaskAmount(followupTaskMapper.selectCount(isHandleTaskQueryWrapper));

        //任务处理率
        String taskProcessingRate = result.getTaskGeneratedTotal() == 0 ? "0" : NumberCalculateUtil.calculateRateStr(result.getProcessedTaskAmount(), result.getTaskIssuedTotal());
        result.setTaskProcessingRate(taskProcessingRate);

        //未完成的任务
        LambdaQueryWrapper<FollowupTask> noHandleTaskQueryWrapper = new LambdaQueryWrapper<>();
        noHandleTaskQueryWrapper.ge(FollowupTask::getCreate_datetime, startOfDay)
                .le(FollowupTask::getCreate_datetime, endOfDay)
                .in(FollowupTask::getTask_status,
                        Lists.newArrayList(
                                TaskFlowStatus.TASK_PENDING_PROCESSING.getCode(),
                                TaskFlowStatus.TASK_DELIVERED.getCode(),
                                TaskFlowStatus.TASK_IN_PROCESSING.getCode())
                );
        Long noHandleTaskNum = followupTaskMapper.selectCount(noHandleTaskQueryWrapper);

        Map<Object, Object> allWorkingOperatorMap = redisService.hashGetAll(QueryStatisticsDataConstant.ALL_CHANNEL_WORKING_OPERATOR_QUEUE);
        int onlineDispatchOperatorNum = CollUtil.isEmpty(allWorkingOperatorMap) ? 0 : allWorkingOperatorMap.size();
        //在线坐席数
        result.setOnlineDispatchOperatorNum((long) onlineDispatchOperatorNum);

        //承压指数=未完成任务/在线坐席
        result.setPressureIndex(result.getOnlineDispatchOperatorNum() == null || result.getOnlineDispatchOperatorNum() == 0 ? "-" :
                NumberCalculateUtil.divideLong(noHandleTaskNum, result.getOnlineDispatchOperatorNum(), 1).toString()
        );

        //分钟产能=平均每分钟完成量（FINISH_DATETIME） 往前推60秒  状态=50 也就是完成的数量
        LambdaQueryWrapper<FollowupTask> finishQueryWrapper = new LambdaQueryWrapper<>();
        finishQueryWrapper.ge(FollowupTask::getFinish_datetime, now.minusSeconds(60));
        finishQueryWrapper.le(FollowupTask::getFinish_datetime, endOfDay);
        result.setMinuteCapacity(followupTaskMapper.selectCount(finishQueryWrapper));

        return result;
    }

    @Override
    public List<TodayOperatorExecuteStatusResp> queryADS9009() {

        List<TodayOperatorExecuteStatusResp> result = adsReportMapper.getTodayOnlineOperatorList();
        if (CollUtil.isEmpty(result)) {
            return Lists.newArrayList();
        }

        // 创建不可变副本用于异步处理
        final List<TodayOperatorExecuteStatusResp> finalResult = Collections.unmodifiableList(result);

        //在线状态
        CompletableFuture<Void> olineOperatorNoAsyncFuture = handleOlineOperatorNoAsync(finalResult);

        //任务执行量
        CompletableFuture<Void> operatorExecGroupAndCountsAsyncFuture = operatorExecGroupAndCountsAsync(finalResult);

        //任务未执行量
        CompletableFuture<Void> operatorNoExecGroupAndCountsAsyncFuture = operatorNoExecGroupAndCountsAsync(finalResult);

        //处理超时次数
        CompletableFuture<Void> groupAndCountTodayOverTimeAsyncFuture = groupAndCountTodayOverTimeAsync(finalResult);

        //未处理的预警超时次数 有就打提醒标记
        CompletableFuture<Void> noHandlegroupAndCountTodayOverTimeAsyncFuture = noHandlegroupAndCountTodayOverTimeAsync(finalResult);

        //等待所有任务完成
        CompletableFuture.allOf(olineOperatorNoAsyncFuture, operatorExecGroupAndCountsAsyncFuture, operatorNoExecGroupAndCountsAsyncFuture, groupAndCountTodayOverTimeAsyncFuture, noHandlegroupAndCountTodayOverTimeAsyncFuture).join();

        //数据处理之后到前端
        return ads9009PostHandle(result);
    }

    public static void main(String[] args) {
        List<TodayOperatorExecuteStatusResp> result = new ArrayList<>();
        for (int i = 10; i > 0; i--) {
            TodayOperatorExecuteStatusResp resp = new TodayOperatorExecuteStatusResp();
            resp.setColor(i+"");
            result.add(resp);

        }
        List<TodayOperatorExecuteStatusResp> collect = result.stream().sorted((a, b) -> {
            return 1;
        }).collect(Collectors.toList());
        for (TodayOperatorExecuteStatusResp resp : collect) {
            System.out.println(resp.getColor());
        }
    }
    
    /**
     *
     * 当坐席存在超时未处理时，未进行叹号预警图标和将该坐席排在首位
     *
     * 有超时处理次数（有过超时但已处理）的数据 采用 黄色展示；
     * 有超时未处里（有超时且还未处理）的数据 采用 红色展示；
     * 签出坐席自动排在末位且用灰色展示；
     * 签出坐席未执行≠0或存在超时未处理，再用红色展示并排在首位。
     * @param result
     * @return
     */

    private List<TodayOperatorExecuteStatusResp> sort(List<TodayOperatorExecuteStatusResp> result) {
        return result.stream()
                .sorted((a, b) -> {
                    // 签出状态判断
                    boolean aLogout = OnlineStatusEnum.ONLINE_RECORD_LOGOUT.getValue().equals(a.getStatus());
                    boolean bLogout = OnlineStatusEnum.ONLINE_RECORD_LOGOUT.getValue().equals(b.getStatus());

                    // 紧急状态判断（需要置顶的红色签出）
                    boolean aUrgent = aLogout && (a.getTaskNoExecuteNum() > 0 || Boolean.TRUE.equals(a.getOverIcon()));
                    boolean bUrgent = bLogout && (b.getTaskNoExecuteNum() > 0 || Boolean.TRUE.equals(b.getOverIcon()));

                    // 未签出但存在未处理超时
                    boolean aUnhandled = !aLogout && Boolean.TRUE.equals(a.getOverIcon());
                    boolean bUnhandled = !bLogout && Boolean.TRUE.equals(b.getOverIcon());

                    // 排序优先级
                    if (aUrgent != bUrgent) return aUrgent ? -1 : 1;
                    if (aUnhandled != bUnhandled) return aUnhandled ? -1 : 1;
                    if (aLogout != bLogout) return aLogout ? 1 : -1;

                    // 按任务总数倒序
                    int totalA = (a.getTaskExecuteNum() != null ? a.getTaskExecuteNum() : 0) +
                            (a.getTaskNoExecuteNum() != null ? a.getTaskNoExecuteNum() : 0);
                    int totalB = (b.getTaskExecuteNum() != null ? b.getTaskExecuteNum() : 0) +
                            (b.getTaskNoExecuteNum() != null ? b.getTaskNoExecuteNum() : 0);
                    return Integer.compare(totalB, totalA);
                })
                .peek(resp -> {
                    // 设置颜色标识
                    if (OnlineStatusEnum.ONLINE_RECORD_LOGOUT.getValue().equals(resp.getStatus())) {
                        if (resp.getTaskNoExecuteNum() > 0 || Boolean.TRUE.equals(resp.getOverIcon())) {
                            resp.setColor(ColorEnum.RED.getCode()); // 红色
                        } else {
                            resp.setColor(ColorEnum.GREY.getCode()); // 灰色
                        }
                    } else {
                        if (Boolean.TRUE.equals(resp.getOverIcon())) {
                            resp.setColor(ColorEnum.RED.getCode()); // 红色
                        } else if (resp.getOverTimeNum() > 0) {
                            resp.setColor(ColorEnum.YELLOW.getCode()); // 黄色
                        }
                    }
                })
                .collect(Collectors.toList());
    }


    private CompletableFuture<Void> noHandlegroupAndCountTodayOverTimeAsync(List<TodayOperatorExecuteStatusResp> result) {
        return CompletableFuture.runAsync(() -> {
            List<OperatorGroupAndCount> noHandlegroupAndCountTodayOverTime = followupTaskMapper.groupAndCountTodayNoHandleAndOverTime();
            if (CollUtil.isNotEmpty(noHandlegroupAndCountTodayOverTime)) {
                Map<String, OperatorGroupAndCount> resultMap = noHandlegroupAndCountTodayOverTime.stream()
                        .collect(Collectors.toMap(
                                OperatorGroupAndCount::getOperatorNo,
                                item -> item,
                                (existing, replacement) -> existing,
                                HashMap::new // 自定义 Map 实现
                        ));

                result.forEach(ele -> {
                    OperatorGroupAndCount operatorGroupAndCount = resultMap.get(ele.getOperatorNo());
                    if (operatorGroupAndCount != null) {
                        ele.setOverIcon(operatorGroupAndCount.getCountNum() >= Constant.ONE);
                    } else {
                        ele.setOverIcon(Boolean.FALSE);
                    }
                });
            } else {
                result.forEach(ele -> ele.setOverIcon(Boolean.FALSE));
            }
        }, monitorExecutor);

    }

    private CompletableFuture<Void> groupAndCountTodayOverTimeAsync(List<TodayOperatorExecuteStatusResp> result) {
        return CompletableFuture.runAsync(() -> {
            List<OperatorGroupAndCount> groupAndCountTodayOverTime = followupTaskMapper.groupAndCountTodayOverTime();
            if (CollUtil.isNotEmpty(groupAndCountTodayOverTime)) {
                Map<String, OperatorGroupAndCount> resultMap = groupAndCountTodayOverTime.stream()
                        .collect(Collectors.toMap(
                                OperatorGroupAndCount::getOperatorNo,
                                item -> item,
                                (existing, replacement) -> existing,
                                HashMap::new // 自定义 Map 实现
                        ));

                result.forEach(ele -> {
                    OperatorGroupAndCount operatorGroupAndCount = resultMap.get(ele.getOperatorNo());
                    if (operatorGroupAndCount != null) {
                        ele.setOverTimeNum(operatorGroupAndCount.getCountNum());
                    } else {
                        ele.setOverTimeNum(Constant.ZERO);
                    }
                });
            } else {
                result.forEach(ele -> ele.setOverTimeNum(Constant.ZERO));
            }
        }, monitorExecutor);

    }

    private CompletableFuture<Void> operatorNoExecGroupAndCountsAsync(List<TodayOperatorExecuteStatusResp> result) {
        return CompletableFuture.runAsync(() -> {
            List<OperatorGroupAndCount> operatorNoExecGroupAndCounts = followupTaskMapper.groupAndCountTodayByStatusOperatorNo(Lists.newArrayList(TaskFlowStatus.TASK_DELIVERED.getCode(), TaskFlowStatus.TASK_IN_PROCESSING.getCode()));
            if (CollUtil.isNotEmpty(operatorNoExecGroupAndCounts)) {
                Map<String, OperatorGroupAndCount> resultMap = operatorNoExecGroupAndCounts.stream()
                        .collect(Collectors.toMap(
                                OperatorGroupAndCount::getOperatorNo,
                                item -> item,
                                (existing, replacement) -> existing,
                                HashMap::new // 自定义 Map 实现
                        ));

                result.forEach(ele -> {
                    OperatorGroupAndCount operatorGroupAndCount = resultMap.get(ele.getOperatorNo());
                    if (operatorGroupAndCount != null) {
                        ele.setTaskNoExecuteNum(operatorGroupAndCount.getCountNum());
                    } else {
                        ele.setTaskNoExecuteNum(Constant.ZERO);
                    }
                });
            } else {
                result.forEach(ele -> ele.setTaskNoExecuteNum(Constant.ZERO));
            }
        }, monitorExecutor);
    }

    private CompletableFuture<Void> operatorExecGroupAndCountsAsync(List<TodayOperatorExecuteStatusResp> result) {
        return CompletableFuture.runAsync(() -> {
            List<OperatorGroupAndCount> operatorExecGroupAndCounts = followupTaskMapper.groupAndCountTodayByStatus(Lists.newArrayList(TaskFlowStatus.TASK_PENDING_VERIFIED.getCode(), TaskFlowStatus.TASK_CONFIRM_VERIFIED.getCode(), TaskFlowStatus.TASK_FINISHED.getCode()));
            if (CollUtil.isNotEmpty(operatorExecGroupAndCounts)) {
                Map<String, OperatorGroupAndCount> resultMap = operatorExecGroupAndCounts.stream()
                        .collect(Collectors.toMap(
                                OperatorGroupAndCount::getOperatorNo,
                                item -> item,
                                (existing, replacement) -> existing,
                                HashMap::new // 自定义 Map 实现
                        ));

                result.forEach(ele -> {
                    OperatorGroupAndCount operatorGroupAndCount = resultMap.get(ele.getOperatorNo());
                    if (operatorGroupAndCount != null) {
                        ele.setTaskExecuteNum(operatorGroupAndCount.getCountNum());
                    } else {
                        ele.setTaskExecuteNum(Constant.ZERO);
                    }
                });
            } else {
                result.forEach(ele -> ele.setTaskExecuteNum(Constant.ZERO));
            }
        }, monitorExecutor);
    }

    private CompletableFuture<Void> handleOlineOperatorNoAsync(List<TodayOperatorExecuteStatusResp> result) {
        return CompletableFuture.runAsync(() -> {
            Map<Object, Object> olineOperatorNoNameMap = redisService.hashGetAll(QueryStatisticsDataConstant.ALL_CHANNEL_WORKING_OPERATOR_QUEUE);
            if (CollUtil.isEmpty(olineOperatorNoNameMap)) {
                result.forEach(ele -> {
                    ele.setStatus(OnlineStatusEnum.ONLINE_RECORD_LOGOUT.getValue());
                    ele.setStatusName(OnlineStatusEnum.getNameByValue(ele.getStatus()));
                });
            } else {
                result.forEach(ele -> {
                    if (olineOperatorNoNameMap.containsKey(ele.getOperatorNo())) {
                        ele.setStatus(OnlineStatusEnum.ONLINE_RECORD_LOGIN.getValue());
                    } else {
                        ele.setStatus(OnlineStatusEnum.ONLINE_RECORD_LOGOUT.getValue());
                    }
                    ele.setStatusName(OnlineStatusEnum.getNameByValue(ele.getStatus()));
                });
            }
        }, monitorExecutor);

    }

    private List<TodayOperatorExecuteStatusResp>  ads9009PostHandle(List<TodayOperatorExecuteStatusResp> convertParam) {
        //修复坐席监控大屏展示问题：当坐席存在超时未处理时，未进行叹号预警图标和将该坐席排在首位
//坐席监控大屏展示颜色：
//有超时处理次数（有过超时但已处理）的数据 采用 黄色展示；
//有超时未处里（有超时且还未处理）的数据 采用 红色展示；
//签出坐席自动排在末位且用灰色展示；
//签出坐席未执行≠0或存在超时未处理，再用红色展示并排在首位。
        convertParam.forEach(resp -> {
            if (Boolean.TRUE.equals(resp.getOverIcon())) {
                resp.setColor(ColorEnum.RED.getCode()); // 红色
                return;
            }
            // 设置颜色标识
            if (OnlineStatusEnum.ONLINE_RECORD_LOGOUT.getValue().equals(resp.getStatus())) {
                if (resp.getTaskNoExecuteNum() > Constant.ZERO) {
                    resp.setColor(ColorEnum.RED.getCode()); // 红色
                } else {
                    resp.setColor(ColorEnum.GREY.getCode()); // 灰色
                }
            } else {
                if (resp.getOverTimeNum() > Constant.ZERO) {
                    resp.setColor(ColorEnum.YELLOW.getCode()); // 黄色
                }
            }
            if (StrUtil.isBlank(resp.getColor())) {
                resp.setColor(Constant.BLANK_SPACE_STRING);
            }
        });
        Map<String, List<TodayOperatorExecuteStatusResp>> colorMap = convertParam.stream().collect(Collectors.groupingBy(TodayOperatorExecuteStatusResp::getColor));

        //红色置顶
        List<TodayOperatorExecuteStatusResp> redList = createSortedList(colorMap,ColorEnum.RED.getCode());
        //黄色
        List<TodayOperatorExecuteStatusResp> yellowList = createSortedList(colorMap,ColorEnum.YELLOW.getCode());
        //灰色最后
        List<TodayOperatorExecuteStatusResp> greyList = createSortedList(colorMap,ColorEnum.GREY.getCode());
        // 提取所有需要排除的坐席编号
        Set<String> excludedNos = Stream.of(redList, yellowList, greyList)
                .flatMap(list -> list.stream().map(TodayOperatorExecuteStatusResp::getOperatorNo))
                .collect(Collectors.toSet());


        // 优化其他颜色过滤逻辑
        List<TodayOperatorExecuteStatusResp> otherColor = convertParam.stream()
                .filter(ele -> !excludedNos.contains(ele.getOperatorNo()))
                .sorted(Comparator.comparingInt(
                        item -> {
                            TodayOperatorExecuteStatusResp resp = (TodayOperatorExecuteStatusResp) item;
                            return (resp.getTaskExecuteNum() == null ? 0 : resp.getTaskExecuteNum()) +
                                    (resp.getTaskNoExecuteNum() == null ? 0 : resp.getTaskNoExecuteNum());
                        }
                ).reversed()).collect(Collectors.toList());

        List<TodayOperatorExecuteStatusResp> result = Stream.of(redList, yellowList, otherColor, greyList)
                .filter(CollUtil::isNotEmpty)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        return result;
    }
    // 提取公共排序方法
    private List<TodayOperatorExecuteStatusResp> createSortedList(Map<String, List<TodayOperatorExecuteStatusResp>> colorMap, String colorCode) {
        return colorMap.getOrDefault(colorCode, new ArrayList<>()).stream()
                .sorted(Comparator.comparingInt(
                        item -> {
                            TodayOperatorExecuteStatusResp resp = (TodayOperatorExecuteStatusResp) item;
                            return (resp.getTaskExecuteNum() == null ? 0 : resp.getTaskExecuteNum()) +
                                    (resp.getTaskNoExecuteNum() == null ? 0 : resp.getTaskNoExecuteNum());
                        }
                ).reversed())
                .collect(Collectors.toList());
    }
    public Map<String, Object> dealOneDayOperatorOnlineTime(String date_time) {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        Object result2;
        String deleteSql = "delete crh_monitor.operatoronlinetime where report_date = to_date('" + date_time + "', 'yyyymmdd')";
        try {
            log.info("执行sql：" + deleteSql);
            result2 = MapUtil.builder().put(Fields.SIZE, generalXmlMapper.delete(deleteSql)).map();
        } catch (Exception e) {
            throw new BizException(MonitorErrorCode.SQL_EXECUTE_IS_ERROR, "sql执行错误", e);
        }
        log.info("result：" + result2);

        //1. 查询见证操作员的登录流水
        List result = (List) getOperatorLoginRecords(date_time);
        String operator_no = null;
        Date begin_online_time = null;
        Date end_online_time = null;
        Date caculate_begin_online_time = null;
        Date caculate_end_online_time = null;
        Date repoprt_date = null;
        Float caculate_seconds = 0.0f;

        if (CollectionUtils.isNotEmpty(result)) {
            for (Object obj : result) {
                HashMap<String, Object> map = (HashMap<String, Object>) obj;
                String staff_no = (String) map.get("staff_no");
                String login_type = (String) map.get("login_type");
                Date create_datetime = (Date) map.get("create_datetime");
                repoprt_date = (Date) map.get("repoprt_date");

                if (operator_no == null) {
                    operator_no = staff_no;
                    if (caculate_begin_online_time == null && StringUtils.equals(login_type, "S")) {
                        caculate_begin_online_time = create_datetime;
                    }
                } else {
                    if (StringUtils.equals(operator_no, staff_no)) {
                        if (caculate_begin_online_time == null && StringUtils.equals(login_type, "S")) {
                            caculate_begin_online_time = create_datetime;
                        }
                        // 没有出现过登录的流水标志，不计算登出的时间
                        if (caculate_begin_online_time != null && StringUtils.equals(login_type, "O")) {
                            caculate_end_online_time = create_datetime;
                        }
                        if (caculate_begin_online_time != null && caculate_end_online_time != null && StringUtils.equals(login_type, "S")) {
                            caculate_seconds = caculate_seconds + DateUtil.getIntervalSeconds(caculate_begin_online_time, caculate_end_online_time);
                            caculate_begin_online_time = create_datetime;
                            caculate_end_online_time = null;
                        }
                    } else {
                        if (caculate_begin_online_time != null && caculate_end_online_time != null) {
                            caculate_seconds = caculate_seconds + DateUtil.getIntervalSeconds(caculate_begin_online_time, caculate_end_online_time);
                        }
                        OperatorOnlineTime operatorOnlineTime = new OperatorOnlineTime();
                        operatorOnlineTime.setReport_date(repoprt_date);
                        operatorOnlineTime.setOperator_no(operator_no);
                        operatorOnlineTime.setBegin_online_time(begin_online_time);
                        operatorOnlineTime.setEnd_online_time(end_online_time);
                        operatorOnlineTime.setOnline_time_sum(caculate_seconds);
                        operatorOnlineTimeMapper.insert(operatorOnlineTime);
                        operator_no = staff_no;
                        begin_online_time = null;
                        end_online_time = null;
                        caculate_seconds = 0.0f;
                        if (StringUtils.equals(login_type, "S")) {
                            caculate_begin_online_time = create_datetime;
                        } else {
                            caculate_begin_online_time = null;
                        }
                        caculate_end_online_time = null;
                    }
                }

                if (begin_online_time == null && StringUtils.equals(login_type, "S")) {
                    begin_online_time = create_datetime;
                }
                if (StringUtils.equals(login_type, "O")) {
                    end_online_time = create_datetime;
                }
            }
            if (caculate_begin_online_time != null && caculate_end_online_time != null) {
                caculate_seconds = caculate_seconds + DateUtil.getIntervalSeconds(caculate_begin_online_time, caculate_end_online_time);
            }
            OperatorOnlineTime operatorOnlineTime = new OperatorOnlineTime();
            operatorOnlineTime.setReport_date(repoprt_date);
            operatorOnlineTime.setOperator_no(operator_no);
            operatorOnlineTime.setBegin_online_time(begin_online_time);
            operatorOnlineTime.setEnd_online_time(end_online_time);
            operatorOnlineTime.setOnline_time_sum(caculate_seconds);
            operatorOnlineTimeMapper.insert(operatorOnlineTime);
        }
        return resultMap;
    }

    private Object getOperatorLoginRecords(String date_time) {
        String sql = "select \n" +
                "    trunc(b.create_datetime) as repoprt_date,\n" +
                "    b.staff_no, \n" +
                "    b.create_datetime,\n" +
                "    b.login_type\n" +
                "from crh_user.userloginjour b\n" +
                "where trunc(b.create_datetime) = to_date('" + date_time + "', 'yyyymmdd')\n" +
                "order by b.staff_no, b.create_datetime";
        Object result;
        try {
            log.info("执行sql：" + sql);
            result = generalXmlMapper.query(sql);
        } catch (Exception e) {
            throw new BizException(MonitorErrorCode.SQL_EXECUTE_IS_ERROR, "sql执行错误", e);
        }
        //4.返回结果
        return result;
    }
}
