package com.cairh.cpe.monitor.backend.service.impl;


import cn.hutool.extra.spring.SpringUtil;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.context.constant.ErrorCode;
import com.cairh.cpe.monitor.backend.basedata.service.IBaseDataService;
import com.cairh.cpe.monitor.backend.constant.BaseDataServiceConstant;
import com.cairh.cpe.monitor.backend.service.IBaseDataRouteService;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class BaseDataRouteServiceImpl implements IBaseDataRouteService {

    @Override
    public Object queryData(String methodCode) {
        // 通过功能号找到对应的service获取数据
        IBaseDataService baseDataService = SpringUtil
                .getBean(BaseDataServiceConstant.BASE_DATA_SERVER_PREFIX + methodCode, IBaseDataService.class);

        if (Objects.isNull(baseDataService)) {
            throw new BizException(ErrorCode.ERR_PARAM_IN_ERROR, "[methodCode]功能号未实现");
        }

        return baseDataService.queryData();
    }
}
