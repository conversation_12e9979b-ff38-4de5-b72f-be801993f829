package com.cairh.cpe.monitor.backend.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.common.constant.CacheKeyConfig;
import com.cairh.cpe.common.constant.Constant;
import com.cairh.cpe.common.constant.MonitorErrorCode;
import com.cairh.cpe.common.dto.OperatorGroupVo;
import com.cairh.cpe.context.BaseUser;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import com.cairh.cpe.monitor.backend.constant.*;
import com.cairh.cpe.monitor.backend.entity.FlowTaskRecordDetails;
import com.cairh.cpe.monitor.backend.entity.OperatorInfo;
import com.cairh.cpe.monitor.backend.form.element.MetaElement;
import com.cairh.cpe.monitor.backend.form.req.BasicStatsRequest;
import com.cairh.cpe.monitor.backend.form.req.MonitorDataReq;
import com.cairh.cpe.monitor.backend.form.resp.MonthlyTeamReviewRankingResp;
import com.cairh.cpe.monitor.backend.mapper.GeneralXmlMapper;
import com.cairh.cpe.monitor.backend.service.ICommonStatsService;
import com.cairh.cpe.monitor.backend.service.IFieldTranslationService;
import com.cairh.cpe.monitor.backend.service.IFlowTaskRecordDetailsService;
import com.cairh.cpe.monitor.backend.service.IOperatorInfoService;
import com.cairh.cpe.monitor.backend.util.CompletableFutureUtils;
import com.cairh.cpe.monitor.backend.util.XmlConvertSqlUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */

@Slf4j
@Service
public class CommonStatsServiceImpl implements ICommonStatsService {
    @Autowired
    protected CompositePropertySources compositePropertySources;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private GeneralXmlMapper generalXmlMapper;
    @Autowired
    private IFlowTaskRecordDetailsService flowTaskRecordDetailsService;
    @Autowired
    private IOperatorInfoService operatorInfoService;
    @Autowired
    private IFieldTranslationService fieldTranslationService;

    @Autowired
    @Qualifier("monitorExecutor")
    private ThreadPoolTaskExecutor monitorExecutor;

    @Override
    public Object commonStatsEntrance(BaseUser baseUser, BasicStatsRequest basicStatsRequest) {

        //1。获取funcId对应的配置
        String XmlCriterionJson = (String) redisTemplate.opsForHash().get(CacheKeyConfig.XML_CRITERION_CACHE_KEY, basicStatsRequest.getFunction_id());
        if (StringUtils.isBlank(XmlCriterionJson)) {
            throw new BizException(MonitorErrorCode.CACHE_CONFIG_NO_EXIST, "功能号[" + basicStatsRequest.getFunction_id() + "]不存在");
        }

        // 获取用户可操作营业部
        if (Objects.nonNull(baseUser)) {
            setOperatorEnBranchNos(baseUser.getStaff_no(), basicStatsRequest);
        }

        //2。转换配置为sql
        MetaElement metaElement;
        String sql;
        try {
            metaElement = JSON.parseObject(XmlCriterionJson, MetaElement.class);
            Map<String, Object> paramMap = BeanUtil.beanToMap(basicStatsRequest);
            if (Objects.isNull(paramMap)) {
                paramMap = new HashMap<>();
            }
            sql = XmlConvertSqlUtil.deal(metaElement, paramMap);
        } catch (Exception e) {
            throw new BizException(MonitorErrorCode.JSON_CONVERT_IS_ERROR, "XmlCriterionJson转换异常", e);
        }

        //3.执行sql
        String type = Optional
                .ofNullable(metaElement.getFunc().getType())
                .orElse(StringUtils.EMPTY).toUpperCase();
        SqlExecuteType executeType = SqlExecuteType.valueOf(type);

        Object result;
        try {

            log.info("执行sql的:" + basicStatsRequest.getFunction_id() + "，执行sql：" + sql);
            switch (executeType) {
                case QUERY:
                    result = generalXmlMapper.query(sql);
                    break;
                case INSERT:
                    result = MapUtil.builder().put(Fields.SIZE, generalXmlMapper.insert(sql)).map();
                    break;
                case DELETE:
                    result = MapUtil.builder().put(Fields.SIZE, generalXmlMapper.delete(sql)).map();
                    break;
                case UPDATE:
                    result = MapUtil.builder().put(Fields.SIZE, generalXmlMapper.update(sql)).map();
                    break;
                case COUNT:
                    result = MapUtil.builder().put(Fields.SIZE, generalXmlMapper.count(sql)).map();
                    break;
                default:
                    throw new BizException(MonitorErrorCode.ATTR_TYPE_NO_FOUND, "xml的Func标签TYPE属性值没有对应类型");
            }

        } catch (Exception e) {
            throw new BizException(MonitorErrorCode.SQL_EXECUTE_IS_ERROR, "sql执行错误", e);
        }

        //4.返回结果
        return result;
    }

    @Override
    public List<MonthlyTeamReviewRankingResp> getMonthlyTeamReviewRanking() {
        // 获取系统参数配置
        String groupConfig = compositePropertySources.getProperty(Constant.MONITOR_AC_OPERATOR_GROUP);
        if (StringUtils.isBlank(groupConfig)) {
            log.error("获取系统参数配置={}为空", Constant.MONITOR_AC_OPERATOR_GROUP);
            return Collections.emptyList();
        }
        try {
            List<OperatorGroupVo> groupList = JSONArray.parseArray(groupConfig).toJavaList(OperatorGroupVo.class);
            List<String> operatorNos = new ArrayList<>();
            groupList.forEach(group -> {
                List<String> nos = Arrays.asList(group.getGroup_operators().split(Constant.COMMA));
                group.setOperator_nos(nos);
                operatorNos.addAll(nos);
            });
            List<String> distinctOperatorNos = operatorNos.stream().distinct().collect(Collectors.toList());
            // 查询月度团队所有坐席审核数据
            List<FlowTaskRecordDetails> flowTaskRecordDetails = flowTaskRecordDetailsService.selectFlowTaskRecordDetailsByOperator(distinctOperatorNos);
            if (CollectionUtil.isNotEmpty(flowTaskRecordDetails)) {
                // 计算每个小组的人均笔数
                return groupList.stream().map(group -> {
                    MonthlyTeamReviewRankingResp resp = new MonthlyTeamReviewRankingResp();
                    resp.setGroup_name(group.getGroup_name());
                    List<String> operatorNoList = group.getOperator_nos();
                    if (CollectionUtil.isEmpty(operatorNoList)) {
                        resp.setGroup_div_num(0);
                        return resp;
                    }
                    int sum = flowTaskRecordDetails
                            .stream()
                            .filter(task -> operatorNoList.contains(task.getOperator_no()))
                            .mapToInt(FlowTaskRecordDetails::getTask_deal_count)
                            .sum();
                    BigDecimal result = new BigDecimal(sum).divide(new BigDecimal(operatorNoList.size()), 0, RoundingMode.HALF_UP);
                    resp.setGroup_div_num(result.intValue());
                    return resp;
                }).sorted(Comparator.comparing(MonthlyTeamReviewRankingResp::getGroup_div_num, Comparator.reverseOrder())).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("获取数据异常", e);
            throw new BizException("获取数据异常");
        }
        return Collections.emptyList();
    }


    private void setOperatorEnBranchNos(String staff_no, BasicStatsRequest basicStatsRequest) {
        try {
            // 构建Redis缓存key
            String cacheKey = CacheKeyConfig.REDIS_AUTH_KEY_PREFIX + staff_no;

            String operatorInfoJson = redisTemplate.opsForValue().get(cacheKey);
            if (operatorInfoJson == null) {
                // 缓存中没有，从数据库查询
                OperatorInfo info = operatorInfoService.selectOperatorInfoByStaffNo(staff_no);
                if (Objects.isNull(info)) {
                    log.warn("操作员：{} 不存在！", staff_no);
                    return;
                }
                operatorInfoJson = JSON.toJSONString(info);

                if (!StringUtils.equals(Constant.SPECIAL_BRANCH, info.getBranch_no())) {
                    basicStatsRequest.setEn_branch_nos(info.getEn_branch_nos() + "," + info.getBranch_no());
                }
                // 存入Redis，设置20分钟过期
                redisTemplate.opsForValue().set(cacheKey, operatorInfoJson, Constant.CACHE_EXPIRE_TIME, TimeUnit.MINUTES);
            } else {
                // 缓存中有，直接使用
                OperatorInfo info = JSON.parseObject(operatorInfoJson, OperatorInfo.class);
                if (!StringUtils.equals(Constant.SPECIAL_BRANCH, info.getBranch_no())) {
                    basicStatsRequest.setEn_branch_nos(info.getEn_branch_nos() + "," + info.getBranch_no());
                }
            }
        } catch (Exception e) {
            log.error("获取操作员信息时发生异常，员工编号: {}", staff_no, e);
        }
    }

    @Override
    public List<?> getMonitorData(MonitorDataReq req) {

        List<MonitorDataEnum> monitorDataEnums = Arrays.stream(MonitorDataEnum.values()).filter(x -> req.getFunctionId().equals(x.getFuncId())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(monitorDataEnums)) {
            log.error("当前funcId:{}未被定义，请检查参数", req.getFunctionId());
            return Collections.emptyList();
        }
        String redisKey = monitorDataEnums.get(0).getFuncId();
        String name = monitorDataEnums.get(0).getName();
        Class<?> clazz = monitorDataEnums.get(0).getClazz();

        if (Boolean.TRUE.equals(redisTemplate.hasKey(redisKey))) {
            String result = redisTemplate.opsForValue().get(redisKey);
            if (ObjectUtil.isEmpty(result)) {
                log.info("当前funcId:{}，获取{}数据时，redis的key中数据为空", req.getFunctionId(), name);
                return Collections.emptyList();
            }
            return JSONUtil.toList(JSONUtil.parseArray(result), clazz);
        } else {
            log.info("当前funcId:{}，获取{}数据时，redis中该key不存在", req.getFunctionId(), name);
            return Collections.emptyList();
        }
    }

    @Override
    public Object newCommonStatsEntrance(BaseUser baseUser, BasicStatsRequest basicStatsRequest) {
        String sourceFunctionId = basicStatsRequest.getFunction_id();
        return CompletableFutureUtils.mergeSumResult(
                () -> {

                    BasicStatsRequest tempRequest = new BasicStatsRequest();
                    BeanUtil.copyProperties(basicStatsRequest, tempRequest);

                    tempRequest.setFunction_id(sourceFunctionId + NewCommonStatsQuerySuffixEnum.PAGE_SUFFIX.getKey());
                    return (List<Object>) commonStatsEntrance(baseUser, tempRequest);
                },
                () -> {

                    BasicStatsRequest tempRequest = new BasicStatsRequest();
                    BeanUtil.copyProperties(basicStatsRequest, tempRequest);

                    tempRequest.setFunction_id(sourceFunctionId + NewCommonStatsQuerySuffixEnum.SUM_SUFFIX.getKey());
                    return (List<Object>) commonStatsEntrance(baseUser, tempRequest);

                },
                () -> {

                    BasicStatsRequest tempRequest = new BasicStatsRequest();
                    BeanUtil.copyProperties(basicStatsRequest, tempRequest);

                    tempRequest.setFunction_id(sourceFunctionId + NewCommonStatsQuerySuffixEnum.TOTAL_SUFFIX.getKey());
                    return (List<Object>) commonStatsEntrance(baseUser, tempRequest);

                }, basicStatsRequest.getHaveSum(),
                new Page<>(basicStatsRequest.getPage_num(), basicStatsRequest.getPage_size()),
                NewApiResultDataConvertEnum.getWithDefaultByFuncationId(sourceFunctionId),
                monitorExecutor
        );
    }

    @Override
    public Object commonStatsEntranceWithFieldTranslation(BaseUser baseUser, BasicStatsRequest basicStatsRequest) {
        //获取数据
        Object result = commonStatsEntrance(baseUser, basicStatsRequest);
        //字段翻译
        fieldTranslationService.translation((List<Map<String, Object>>) result, basicStatsRequest.getExcludeTransField());
        return result;
    }
}
