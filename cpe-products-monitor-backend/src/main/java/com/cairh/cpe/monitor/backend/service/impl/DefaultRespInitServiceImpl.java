package com.cairh.cpe.monitor.backend.service.impl;

import com.cairh.cpe.common.constant.Constant;
import com.cairh.cpe.monitor.backend.form.resp.OperatorOnlineOverviewResp;
import com.cairh.cpe.monitor.backend.form.resp.TodayOperatorCapacityDistributionResp;
import com.cairh.cpe.monitor.backend.service.DefaultRespInitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class DefaultRespInitServiceImpl implements DefaultRespInitService {

    @Override
    public OperatorOnlineOverviewResp getEmptryOperatorOnlineOverviewResp() {
        OperatorOnlineOverviewResp resp = new OperatorOnlineOverviewResp();
        resp.setOnlineOperatorNum(Constant.ZERO);
        resp.setOnlineRate(Constant.ZERO_STR);
        resp.setOfflineOperatorNum(Constant.ZERO);
        resp.setOfflineRate(Constant.ZERO_STR);
        resp.setOnlineDispatchOperatorNum(Constant.ZERO);
        resp.setOnlineDispatchRate(Constant.ZERO_STR);
        return resp;
    }

    @Override
    public TodayOperatorCapacityDistributionResp getEmptryTodayOperatorCapacityDistributionResp() {
        TodayOperatorCapacityDistributionResp resp = new TodayOperatorCapacityDistributionResp();
        resp.setLevel_1_number(Constant.ZERO);
        resp.setLevel_1_rate(Constant.ZERO_STR);
        resp.setLevel_2_number(Constant.ZERO);
        resp.setLevel_2_rate(Constant.ZERO_STR);
        resp.setLevel_3_number(Constant.ZERO);
        resp.setLevel_3_rate(Constant.ZERO_STR);
        resp.setLevel_4_number(Constant.ZERO);
        resp.setLevel_4_rate(Constant.ZERO_STR);
        resp.setLevel_5_number(Constant.ZERO);
        resp.setLevel_5_rate(Constant.ZERO_STR);
        resp.setLevel_6_number(Constant.ZERO);
        resp.setLevel_6_rate(Constant.ZERO_STR);
        resp.setLevel_7_number(Constant.ZERO);
        resp.setLevel_7_rate(Constant.ZERO_STR);
        return resp;
    }
}
