package com.cairh.cpe.monitor.backend.service.impl;


import cn.hutool.core.collection.CollUtil;
import com.cairh.cpe.monitor.backend.constant.NewApiResultDataConvertEnum;
import com.cairh.cpe.monitor.backend.form.resp.PageResp;
import com.cairh.cpe.monitor.backend.service.IFieldTranslationService;
import com.cairh.cpe.monitor.backend.service.NewApiResultDataConvertService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class DefaultResultDataConvertServiceImpl implements NewApiResultDataConvertService {
    @Autowired
    private IFieldTranslationService fieldTranslationService;

    @Override
    public String getFuncId() {
        return NewApiResultDataConvertEnum.DEFAULT.getFuncId();
    }

    @Override
    public void handle(Boolean haveSum, PageResp<?> pageResp) {
        List<?> records = pageResp.getRecords();
        //没返回值或者只有一个合计不需要转换
        if (haveSum && records.size() == 1) {
            records.clear();
        }
        if (CollUtil.isEmpty(records)) {
            return;
        }
        Object firstObj = haveSum && records.size() > 1 ? records.get(1) : records.get(0);
        Map<String, Object> map = (Map<String, Object>) firstObj;
        boolean endsWithRate = map.keySet().stream().filter(ele -> ele.toLowerCase().endsWith("rate")).findFirst().isPresent();
        if (ObjectUtils.isEmpty(map.get("branch_no")) && ObjectUtils.isEmpty(map.get("up_branch_no")) && !endsWithRate) {
            return;
        }

        fieldTranslationService.translation((List<Map<String, Object>>) records, null);

    }
}
