package com.cairh.cpe.monitor.backend.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.cairh.cpe.esb.base.rpc.IVBaseAllBranchDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseAllBranchQryRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseAllBranchQryResponse;
import com.cairh.cpe.monitor.backend.constant.TranslationEnum;
import com.cairh.cpe.monitor.backend.service.IFieldTranslationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FieldTranslationServiceImpl implements IFieldTranslationService {

    @DubboReference
    private IVBaseAllBranchDubboService baseAllBranchDubboService;

    @Override
    public void translation(List<Map<String, Object>> results,List<String> excludeTransField) {
        if (CollUtil.isEmpty(results)) {
            return;
        }
        // 获取所有branchNo
        VBaseAllBranchQryRequest request = new VBaseAllBranchQryRequest();
        List<VBaseAllBranchQryResponse> vBaseAllBranchQryResponses = baseAllBranchDubboService.baseDataQryAllBranch(request);
        Map<String, VBaseAllBranchQryResponse> responseMap = vBaseAllBranchQryResponses.stream().collect(Collectors.toMap(VBaseAllBranchQryResponse::getBranch_no, x -> x));

        results.forEach(result -> {
            try {
                TranslationEnum.handle(result, excludeTransField, responseMap);
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        });
    }

}
