package com.cairh.cpe.monitor.backend.service.impl;

import com.cairh.cpe.monitor.backend.entity.FlowTaskRecordDetails;
import com.cairh.cpe.monitor.backend.mapper.FlowTaskRecordDetailsMapper;
import com.cairh.cpe.monitor.backend.service.IFlowTaskRecordDetailsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Description：
 * Author： slx
 * Date： 2024/9/11 13:28
 */
@Slf4j
@Service
public class FlowTaskRecordDetailsServiceImpl implements IFlowTaskRecordDetailsService {

    @Autowired
    private FlowTaskRecordDetailsMapper flowTaskRecordDetailsMapper;
    @Override
    public List<FlowTaskRecordDetails> selectFlowTaskRecordDetailsByOperator(List<String> operatorNoList) {
        return flowTaskRecordDetailsMapper.selectFlowTaskRecordDetailsByOperator(operatorNoList);
    }
}
