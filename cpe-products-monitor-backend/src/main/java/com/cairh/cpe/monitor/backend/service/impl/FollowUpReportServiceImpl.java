package com.cairh.cpe.monitor.backend.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.monitor.backend.entity.FollowupTask;
import com.cairh.cpe.monitor.backend.form.req.BasicStatsRequest;
import com.cairh.cpe.monitor.backend.form.resp.*;
import com.cairh.cpe.monitor.backend.mapper.FollowupApplyMapper;
import com.cairh.cpe.monitor.backend.mapper.FollowupOperatorStatisticsMapper;
import com.cairh.cpe.monitor.backend.mapper.FollowupTaskMapper;
import com.cairh.cpe.monitor.backend.service.IFollowUpReportService;
import com.cairh.cpe.monitor.backend.util.CompletableFutureUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class FollowUpReportServiceImpl implements IFollowUpReportService {

    @Autowired
    private FollowupApplyMapper followupApplyMapper;

    @Autowired
    private FollowupOperatorStatisticsMapper followupOperatorStatisticsMapper;

    @Autowired
    private FollowupTaskMapper followupTaskMapper;

    @Autowired
    @Qualifier("monitorExecutor")
    private ThreadPoolTaskExecutor monitorExecutor;

    @Override
    public PageResp<FollowUpOperatorBusinessResp> operatorBusiness(BasicStatsRequest basicStatsRequest) {
        Page<FollowupTask> pageTask = new Page<>(basicStatsRequest.getPage_num(), basicStatsRequest.getPage_size());
        return CompletableFutureUtils.mergeSumResult(() -> {
                    List<FollowUpOperatorBusinessResp> result = followupTaskMapper.operatorBusiness(pageTask, basicStatsRequest);
                    return PageResp.ofPage(pageTask, result);
                }, () -> this.operatorBusinessTotal(basicStatsRequest)
                , pageTask, monitorExecutor);
    }

    @Override
    public FollowUpOperatorBusinessResp operatorBusinessTotal(BasicStatsRequest basicStatsRequest) {
        return followupTaskMapper.operatorBusinessTotal(basicStatsRequest);
    }

    @Override
    public PageResp<FollowUpOperatorWorkResp> operatorWork(BasicStatsRequest basicStatsRequest) {
        Page<FollowupTask> pageTask = new Page<>(basicStatsRequest.getPage_num(), basicStatsRequest.getPage_size());
        return CompletableFutureUtils.mergeSumResult(() -> {
                    List<FollowUpOperatorWorkResp> result = followupOperatorStatisticsMapper.operatorWork(pageTask, basicStatsRequest);
                    return PageResp.ofPage(pageTask, result);
                }, () -> this.operatorWorkTotal(basicStatsRequest)
                , pageTask, monitorExecutor);
    }

    @Override
    public FollowUpOperatorWorkResp operatorWorkTotal(BasicStatsRequest basicStatsRequest) {
        return followupOperatorStatisticsMapper.operatorWorkTotal(basicStatsRequest);
    }

    @Override
    public FollowUpDailyApplyResp dailyApply(BasicStatsRequest basicStatsRequest) {
        return followupApplyMapper.dailyApply(basicStatsRequest);
    }

    @Override
    public FollowUpDailyTaskResp dailyTask(BasicStatsRequest basicStatsRequest) {
        FollowUpDailyTaskResp result = followupTaskMapper.dailyTask(basicStatsRequest);
        Integer onlineOperatorNum = followupOperatorStatisticsMapper.getOnlineOperatorNum(basicStatsRequest);
        result.setOnline_user_count(onlineOperatorNum);
        result.setDaily_capacity(onlineOperatorNum == 0 ? 0.0 : result.getCall_total_count() * 1.0 / onlineOperatorNum);
        return result;
    }

}
