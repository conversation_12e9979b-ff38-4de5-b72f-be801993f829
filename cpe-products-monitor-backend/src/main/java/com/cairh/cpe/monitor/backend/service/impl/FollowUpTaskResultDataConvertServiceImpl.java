package com.cairh.cpe.monitor.backend.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.cairh.cpe.monitor.backend.constant.NewApiResultDataConvertEnum;
import com.cairh.cpe.monitor.backend.entity.FollowupSurvey;
import com.cairh.cpe.monitor.backend.form.resp.PageResp;
import com.cairh.cpe.monitor.backend.service.NewApiResultDataConvertService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 回访流水报表结果转换
 */
@Service
@Slf4j
public class FollowUpTaskResultDataConvertServiceImpl implements NewApiResultDataConvertService {
    @Override
    public String getFuncId() {
        return NewApiResultDataConvertEnum.MONITOR10004.getFuncId();
    }

    /**
     * 问卷答案转成1=是，2=是这种格式
     */
    @Override
    public void handle(Boolean haveSum, PageResp<?> pageResp) {
        //组装问卷答案
        List<?> records = pageResp.getRecords();
        if (CollUtil.isEmpty(records)) {
            return;
        }
        log.debug("funcId = [{}] result before convert = [{}]", this.getFuncId(), JSONObject.toJSONString(records));
        for (Object record : records) {
            try {
                Map<String, Object> resultMap = (Map<String, Object>) record;
                resultMap.put("qnaire_answer", null);
                Object qnaireContent = resultMap.remove("qnaire_content");
                if (qnaireContent == null || StrUtil.isBlank(String.valueOf(qnaireContent).trim()) || !String.valueOf(qnaireContent).trim().startsWith("[")) {
                    continue;
                }
                String qnaireAnswer = getQnaireAnswer(qnaireContent);
                if (StrUtil.isNotBlank(qnaireAnswer)) {
                    resultMap.put("qnaire_answer", qnaireAnswer);
                }
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        log.debug("funcId = [{}] result after convert = [{}]", this.getFuncId(), JSONObject.toJSONString(records));
    }

    private static String getQnaireAnswer(Object qnaireContent) {
        String firstSurveyResultDesc = null;
        String secondSurveyResultDesc = null;
        List<FollowupSurvey> followupSurveys = com.alibaba.fastjson.JSONObject.parseArray(qnaireContent.toString(), FollowupSurvey.class);
        for (FollowupSurvey followupSurvey : followupSurveys) {
            if (StrUtil.isNotBlank(followupSurvey.getSurvey_cate()) && Objects.equals(followupSurvey.getSurvey_cate(), "question_first")) {
                String firstSurveyResult = followupSurvey.getSurvey_result();
                if (StrUtil.isNotBlank(firstSurveyResult)) {
                    firstSurveyResultDesc = "1=" + ("1".equals(firstSurveyResult) ? "是" : "否");
                }
            } else if (StrUtil.isNotBlank(followupSurvey.getSurvey_cate()) && Objects.equals(followupSurvey.getSurvey_cate(), "question_second")) {
                String secondSurveyResult = followupSurvey.getSurvey_result();
                if (StrUtil.isNotBlank(secondSurveyResult)) {
                    secondSurveyResultDesc = "2=" + ("1".equals(secondSurveyResult) ? "是" : "否");
                }
            }
        }

        return Stream.of(firstSurveyResultDesc, secondSurveyResultDesc).filter(StrUtil::isNotBlank).collect(Collectors.joining(","));
    }
}
