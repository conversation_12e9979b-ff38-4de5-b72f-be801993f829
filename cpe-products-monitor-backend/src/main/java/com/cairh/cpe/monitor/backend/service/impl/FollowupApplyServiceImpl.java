package com.cairh.cpe.monitor.backend.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.monitor.backend.entity.FollowupApply;
import com.cairh.cpe.monitor.backend.mapper.FollowupApplyMapper;
import com.cairh.cpe.monitor.backend.service.IFollowupApplyService;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Service
public class FollowupApplyServiceImpl extends ServiceImpl<FollowupApplyMapper, FollowupApply> implements IFollowupApplyService {

    @Override
    public void updateApplyStatus(String serial_id, String status) {
        LambdaUpdateWrapper<FollowupApply> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(FollowupApply::getUpdate_datetime, new Date());
        wrapper.set(FollowupApply::getStatus, status);
        wrapper.eq(FollowupApply::getSerial_id, serial_id);
        this.update(wrapper);
    }
}
