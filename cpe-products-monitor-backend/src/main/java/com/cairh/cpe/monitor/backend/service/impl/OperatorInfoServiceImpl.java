package com.cairh.cpe.monitor.backend.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.monitor.backend.entity.OperatorInfo;
import com.cairh.cpe.monitor.backend.mapper.OperatorInfoMapper;
import com.cairh.cpe.monitor.backend.service.IOperatorInfoService;
import org.springframework.stereotype.Service;

/**
 * 查询用户信息服务实现
 *
 * <AUTHOR>
 * @since 2025/2/25 10:59
 */
@Service
public class OperatorInfoServiceImpl extends ServiceImpl<OperatorInfoMapper, OperatorInfo> implements IOperatorInfoService {

    @Override
    public OperatorInfo selectOperatorInfoByStaffNo(String staff_no) {
        return this.baseMapper.selectOperatorInfoByStaffNo(staff_no);
    }
}
