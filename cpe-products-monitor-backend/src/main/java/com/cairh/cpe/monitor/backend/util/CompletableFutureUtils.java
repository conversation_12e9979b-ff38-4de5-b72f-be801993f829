package com.cairh.cpe.monitor.backend.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cairh.cpe.monitor.backend.constant.NewApiResultDataConvertEnum;
import com.cairh.cpe.monitor.backend.form.resp.PageResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Supplier;

@Slf4j
public class CompletableFutureUtils {

    /**
     * 合并查询结果和合计结果
     *
     * @param executor         线程池
     * @param pageRespSupplier 查询结果提供者
     * @param sumSupplier      合计结果提供者
     * @param pageTask         分页信息
     * @return 合并后的结果
     */
    public static <T> PageResp<T> mergeSumResult(
            Supplier<PageResp<T>> pageRespSupplier,
            Supplier<T> sumSupplier,
            Page<?> pageTask, ThreadPoolTaskExecutor executor) {
        CompletableFuture<PageResp<T>> pageResultFuture = CompletableFuture.supplyAsync(() -> pageRespSupplier.get(), executor);

        CompletableFuture<T> sumResultFuture = CompletableFuture.supplyAsync(() -> sumSupplier.get(), executor);

        CompletableFuture<Void> allFutures = CompletableFuture.allOf(pageResultFuture, sumResultFuture);

        CompletableFuture<PageResp<T>> combinedFuture = allFutures.thenApply(v -> {
            try {
                PageResp<T> pageResult = pageResultFuture.get();
                T sumResult = sumResultFuture.get();
                pageResult.getRecords().add(0, sumResult);
                return pageResult;
            } catch (Exception e) {
                log.error(e.getMessage());
                return PageResp.ofPage(pageTask, Lists.newArrayList());
            }
        });

        try {
            return combinedFuture.get();
        } catch (Exception e) {
            log.error("mergeSumResult merger error", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 合并查询结果和合计结果
     *
     * @param pageRespSupplier   查询结果提供者
     * @param sumSupplier        合计结果提供者
     * @param haveSum            是否有合计行
     * @param pageTask           分页信息
     * @param resultConvertEnums 结果转换枚举
     * @param executor           线程池
     * @return 合并后的结果
     */
    public static PageResp<?> mergeSumResult(
            Supplier<List<Object>> pageRespSupplier,
            Supplier<List<Object>> sumSupplier,
            Supplier<List<Object>> countSupplier,
            Boolean haveSum, Page<?> pageTask,
            List<NewApiResultDataConvertEnum> resultConvertEnums,
            ThreadPoolTaskExecutor executor) {
        haveSum = BooleanUtil.isTrue(haveSum);
        CompletableFuture<List<Object>> pageResultFuture = CompletableFuture.supplyAsync(() -> pageRespSupplier.get(), executor);
        CompletableFuture<List<Object>> countResultFuture = CompletableFuture.supplyAsync(() -> countSupplier.get(), executor);
        CompletableFuture<List<Object>> sumResultFuture;
        CompletableFuture<Void> allFutures;
        List<CompletableFuture> allOfList = new ArrayList<>();
        //兼容没合计的场景
        if (haveSum) {
            sumResultFuture = CompletableFuture.supplyAsync(() -> sumSupplier.get(), executor);
            allOfList.add(pageResultFuture);
            allOfList.add(sumResultFuture);
            allOfList.add(countResultFuture);
        } else {
            sumResultFuture = null;
            allOfList.add(pageResultFuture);
            allOfList.add(countResultFuture);
        }
        CompletableFuture<?>[] futuresArray = allOfList.toArray(new CompletableFuture[0]);
        allFutures = CompletableFuture.allOf(futuresArray);
        Boolean finalHaveSum = haveSum;
        CompletableFuture<PageResp<T>> combinedFuture = allFutures.thenApply(v -> {
            try {
                //查分页数据
                List<Object> pageResult = pageResultFuture.get();
                //查总数
                List<Object> count = countResultFuture.get();
                List<Object> record = new ArrayList<>();
                record.addAll(pageResult);
                //添加合计数据
                addSumResult(finalHaveSum, sumResultFuture, record);
                PageResp pageResp = PageResp.ofPage(Long.parseLong(String.valueOf(((Map) count.get(0)).get("total"))), pageTask.getPages(), pageTask.getSize(), record);
                //结果处理
                Optional.ofNullable(resultConvertEnums).ifPresent(
                        eles -> eles.forEach(
                                ele -> ele.handleData(finalHaveSum,pageResp)));
                return pageResp;
            } catch (Exception e) {
                log.error("mergeSumResult error = {}", JSONObject.toJSONString(e));
                return PageResp.ofPage(pageTask, Lists.newArrayList());
            }
        });
        try {
            return combinedFuture.get();
        } catch (Exception e) {
            log.error("mergeSumResult get() error = {}", JSONObject.toJSONString(e));
            throw new RuntimeException(e);
        }
    }

    private static void addSumResult(Boolean haveSum, CompletableFuture<List<Object>> sumResultFuture, List<Object> record) throws InterruptedException, ExecutionException {
        if (haveSum) {
            //查合计数据
            List<Object> sum = sumResultFuture.get();
            //合计列一定超过两个字段,否则不合并合计 SELECT 1 total FROM DUAL 单挑数据提升性能
            if (CollUtil.isNotEmpty(sum) && ((HashMap) sum.get(0)).entrySet().size() > 1) {
                //合计放到第一个
                record.add(0, sum.get(0));
            }
        }
    }
}
