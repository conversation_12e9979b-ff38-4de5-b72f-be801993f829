package com.cairh.cpe.monitor.backend.util;

import cn.hutool.core.util.StrUtil;


/**
 * 日期转换工具类
 */
public class DateConvertUtil {


    public DateConvertUtil() {
    }

    public static String appendHhMmSs(String time, String appendStr) {
        if (StrUtil.isBlank(time) || StrUtil.isBlank(appendStr) || appendStr.contains(":")) {
            return time;
        }
        return time + " " + appendStr;
    }

    public static String appendStartTime(String time) {
        if (StrUtil.isBlank(time) || time.contains(":")) {
            return time;
        }
        return time + " 00:00:00";
    }

    public static String appendEndTime(String time) {
        if (StrUtil.isBlank(time) || time.contains(":")) {
            return time;
        }
        return time + " 23:59:59";
    }
}
