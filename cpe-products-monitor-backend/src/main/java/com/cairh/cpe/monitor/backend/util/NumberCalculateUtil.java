package com.cairh.cpe.monitor.backend.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class NumberCalculateUtil {
    /**
     * 两个 Integer 相除并保留两位小数
     *
     * @param dividend 被除数
     * @param divisor  除数
     * @return 保留两位小数的结果
     * @throws 当除数为 0 时抛出异常
     */
    public static BigDecimal divideIntegers(Integer dividend, Integer divisor) {
        if (dividend == null || divisor == null) {
            throw new IllegalArgumentException("被除数和除数不能为 null");
        }
        if (divisor == 0) {
            throw new ArithmeticException("除数不能为 0");
        }
        return divideIntegers(dividend,divisor,2);
    }

    /**
     * 两个 Integer 相除并保留两位小数
     *
     * @param dividend 被除数
     * @param divisor  除数
     * @return 保留两位小数的结果
     * @throws 当除数为 0 时抛出异常
     */
    public static BigDecimal divideIntegers(Integer dividend, Integer divisor, int scale) {
        if (dividend == null || divisor == null) {
            throw new IllegalArgumentException("被除数和除数不能为 null");
        }
        if (divisor == 0) {
            throw new ArithmeticException("除数不能为 0");
        }
        return new BigDecimal(dividend)
                .divide(new BigDecimal(divisor), scale, RoundingMode.HALF_UP);
    }
    /**
     * 两个 divideLong 相除并保留两位小数
     *
     * @param dividend 被除数
     * @param divisor  除数
     * @return 保留两位小数的结果
     * @throws 当除数为 0 时抛出异常
     */
    public static BigDecimal divideLong(Long dividend, Long divisor, int scale) {
        return divideIntegers(Integer.parseInt(String.valueOf(dividend)), Integer.valueOf(String.valueOf(divisor)), scale);
    }

    /**
     * 两个 Integer 相除并保留两位小数
     *
     * @param dividend 被除数
     * @param divisor  除数
     * @return 保留两位小数的结果
     */
    public static BigDecimal calculateRate(Integer dividend, Integer divisor) {
        return divideIntegers(dividend * 100, divisor);
    }

    /**
     * 两个 Integer 相除并保留两位小数
     *
     * @param dividend 被除数
     * @param divisor  除数
     * @return 百分比字符串
     */
    public static String calculateRateStr(Integer dividend, Integer divisor) {
        return divideIntegers(dividend * 100, divisor).toString();
    }


    /**
     * 两个 BigDecimal 做乘法运算
     *
     * @param multiplicand 被乘数
     * @param multiplier   乘数
     * @return 乘法运算的结果
     */
    public static BigDecimal multiply(BigDecimal multiplicand, BigDecimal multiplier) {
        if (multiplicand == null || multiplier == null) {
            throw new IllegalArgumentException("被乘数和乘数不能为 null");
        }
        return multiplicand.multiply(multiplier);
    }

    /**
     * 两个 Long 相除并保留两位小数
     *
     * @param dividend 被除数
     * @param divisor  除数
     * @return 保留两位小数的结果
     */
    public static String calculateRateStr(Long dividend, Long divisor) {
        return calculateRateStr(Integer.valueOf(dividend.toString()), Integer.valueOf(divisor.toString()));
    }
}
