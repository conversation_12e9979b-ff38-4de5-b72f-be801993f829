package com.cairh.cpe.monitor.backend.util;

import com.cairh.cpe.common.constant.MonitorErrorCode;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.monitor.backend.constant.Fields;
import com.cairh.cpe.monitor.backend.constant.ParseTypeEnum;
import com.cairh.cpe.monitor.backend.form.element.*;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Attribute;
import org.dom4j.Element;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;

/**
 * xml示例
 *
 * <Package desc="码上赢推送查询" Verify="码上赢推送查询">
 * <Func ID="CRH.DATA.QUERY.ICBCPUSHRECORDLIST" MODULE_NAME="码上赢推送查询" TYPE="QUERY" DESC="查询数据库对象">
 * <InParam>
 * <Param Code="start_time" Name="开始时间"/>
 * <Param Code="end_time" Name="结束时间"/>
 * <Param Code="status" Name="状态"/>
 * </InParam>
 * <Content><![CDATA[
 * select imr.curr_date,
 * imr.quantity,
 * decode(imr.status,'0','成功','失败') status,
 * imr.remark
 * from crh_wskh.icbcmsyrecord imr
 * where 1=1
 * <if condition="@status != null">
 * and imr.status = '@status'
 * </if>
 *
 * <if condition="@start_time != null">
 * and imr.curr_date >= to_number(replace('@start_time','-',''))
 * </if>
 * <if condition="@end_time != null">
 * and imr.curr_date <= to_number(replace('@end_time','-',''))
 * </if>
 * order by imr.curr_date desc
 * ]]></Content>
 * <OutParam>
 * <Param Code="start_time" Name="开始时间" Default=""/>
 * </OutParam>
 * </Func>
 * </Package>
 */

public class XmlConvertEntityUtil {

    public static void deal(MetaElement metaElement, Element element, ParseTypeEnum parseTypeEnum) {

        switch (parseTypeEnum) {
            case Func:
                dealFunc(metaElement, element);
                break;
            case InParam:
                dealInParam(metaElement, element);
                break;
            case Content:
                dealContent(metaElement, element);
                break;
            case OutParam:
                dealOutParam(metaElement, element);
                break;
            default:
                throw new BizException(MonitorErrorCode.PARSE_TYPE_NO_FOUND, "没有对应转换格式类型");
        }
    }

    public static void dealFunc(MetaElement metaElement, Element element) {
        FuncElement func = Optional.ofNullable(metaElement.getFunc()).orElse(new FuncElement());
        metaElement.setFunc(func);

        for (Iterator<Attribute> iterator = element.attributeIterator(); iterator.hasNext(); ) {
            Attribute attribute = iterator.next();
            String attrName = attribute.getName();
            String attrValue = attribute.getValue().toLowerCase();
            switch (attrName) {
                case Fields.MODULE_NAME:
                    func.setModuleName(attrValue);
                    break;
                case Fields.ID:
                    func.setId(attrValue);
                    break;
                case Fields.TYPE:
                    func.setType(attrValue);
                    break;
                case Fields.DESC:
                    func.setDesc(attrValue);
                    break;
                case Fields.SOURCE_TYPE:
                    func.setSourceType(attrValue);
                    break;
                default:
                    throw new BizException(MonitorErrorCode.ATTR_TYPE_NO_FOUND, "xml的func属性没有对应字段类型");
            }
        }
    }

    public static void dealInParam(MetaElement metaElement, Element element) {
        List<ParamElement> inParamList = Optional.ofNullable(metaElement.getParamElements()).orElse(new ArrayList<>());
        metaElement.setParamElements(inParamList);

        List paramList = element.content();
        for (Object object : paramList) {
            if (object instanceof Element) {
                ParamElement xmlInParam = new ParamElement();
                Element paramElement = (Element) object;
                xmlInParam.setCode(Optional
                        .ofNullable(paramElement.attributeValue(Fields.CODE)).orElse(StringUtils.EMPTY));
                xmlInParam.setName(Optional
                        .ofNullable(paramElement.attributeValue(Fields.NAME)).orElse(StringUtils.EMPTY));
                inParamList.add(xmlInParam);
            }
        }
    }

    public static void dealContent(MetaElement metaElement, Element element) {
        List<ContentElement> contentList = Optional.ofNullable(metaElement.getContentElements()).orElse(new ArrayList<>());
        metaElement.setContentElements(contentList);

        String elementText = element.getText();
        String startJudge = "<if ";
        String endJudge = "</if>";
        int startIndex = elementText.indexOf(startJudge);
        int endIndex = elementText.indexOf(endJudge);

        while (startIndex != -1 && endIndex != -1) {
            ContentElement noJudgeContent = new ContentElement();
            noJudgeContent.setValue(elementText.substring(0, startIndex));
            contentList.add(noJudgeContent);

            String condition_str = elementText.substring(startIndex, endIndex + endJudge.length());
            String condition = condition_str.substring(condition_str.indexOf("condition=\"") + "condition=\"".length(), condition_str.indexOf("\">"));
            String condition_value = condition_str.substring(condition_str.indexOf("\">") + "\">".length(), condition_str.indexOf(endJudge));

            ContentElement judgeContent = new ContentElement();
            judgeContent.setCondition(condition);
            judgeContent.setValue(condition_value);
            contentList.add(judgeContent);

            elementText = elementText.substring(endIndex + endJudge.length());
            startIndex = elementText.indexOf(startJudge);
            endIndex = elementText.indexOf(endJudge);
        }

        ContentElement endContent = new ContentElement();
        endContent.setValue(elementText);
        contentList.add(endContent);
    }

    public static void dealOutParam(MetaElement metaElement, Element element) {
        List<ResultElement> outParamList = Optional.ofNullable(metaElement.getResultElements()).orElse(new ArrayList<>());
        metaElement.setResultElements(outParamList);

        List paramList = element.content();
        for (Object object : paramList) {
            if (object instanceof Element) {
                ResultElement resultElement = new ResultElement();
                Element paramElement = (Element) object;
                resultElement.setCode(Optional
                        .ofNullable(paramElement.attributeValue(Fields.CODE)).orElse(StringUtils.EMPTY));
                resultElement.setName(Optional
                        .ofNullable(paramElement.attributeValue(Fields.NAME)).orElse(StringUtils.EMPTY));
                resultElement.setDefaultValue(Optional
                        .ofNullable(paramElement.attributeValue(Fields.DEFAULT)).orElse(StringUtils.EMPTY));
                outParamList.add(resultElement);
            }
        }
    }

}
