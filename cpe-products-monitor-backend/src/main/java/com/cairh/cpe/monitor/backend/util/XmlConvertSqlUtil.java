package com.cairh.cpe.monitor.backend.util;

import com.cairh.cpe.common.constant.Constant;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.monitor.backend.form.element.ContentElement;
import com.cairh.cpe.monitor.backend.form.element.MetaElement;
import com.cairh.cpe.monitor.backend.form.element.ParamElement;
import org.apache.commons.lang3.StringUtils;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static com.cairh.cpe.common.constant.MonitorErrorCode.XML_PARSE_ERROR;


public class XmlConvertSqlUtil {

    public static String deal(MetaElement metaElement, Map<String, Object> param) {

        StringBuilder stringBuilder = new StringBuilder();
        boolean hasCode = false;
        String keyStr = StringUtils.EMPTY;
        List<ParamElement> inParamList = metaElement.getParamElements();
        List<ContentElement> contentList = metaElement.getContentElements();

        for (ContentElement content : contentList) {
            String condition = content.getCondition();
            String value = content.getValue();

            if (StringUtils.isBlank(condition)) {
                stringBuilder.append(value);
            } else {
                for (ParamElement paramElement : inParamList) {
                    String code = paramElement.getCode();
                    if (!condition.contains(Constant.CHAR_SET[0] + code)) {
                        continue;
                    }

                    Set<String> keys = param.keySet();
                    for (String key : keys) {
                        if (key.equalsIgnoreCase(code)) {
                            hasCode = true;
                            keyStr = key;
                            break;
                        } else {
                            hasCode = false;
                            keyStr = StringUtils.EMPTY;
                        }
                    }

                    Object paramKey = param.get(keyStr);
                    if (hasCode && !Objects.isNull(paramKey)) {
                        condition = condition
                                .replaceAll(Constant.CHAR_SET[0] + code, "'" + paramKey + "'")
                                .replaceAll(Constant.CHAR_SET[1] + code, "'" + paramKey + "'");
                    } else {
                        condition = condition
                                .replaceAll(Constant.CHAR_SET[0] + code, "null")
                                .replaceAll("'" + Constant.CHAR_SET[0] + code + "'", "null")
                                .replaceAll(Constant.CHAR_SET[1] + code, "null")
                                .replaceAll("'" + Constant.CHAR_SET[1] + code + "'", "null");
                    }
                }

                if (dealCondition(condition)) {
                    stringBuilder.append(content.getValue());
                }
            }
        }

        String sql = stringBuilder.toString();
        for (ParamElement paramElement : inParamList) {
            String code = paramElement.getCode();
            hasCode = false;
            keyStr = StringUtils.EMPTY;
            Set<String> keys = param.keySet();
            for (String key : keys) {
                if (key.equalsIgnoreCase(code)) {
                    hasCode = true;
                    keyStr = key;
                }
            }

            Object paramKey = param.get(keyStr);
            if (hasCode && !Objects.isNull(paramKey)) {
                sql = sql.replaceAll(Constant.CHAR_SET[0] + code, paramKey.toString())
                        .replaceAll(Constant.CHAR_SET[1] + code, paramKey.toString());
            }
        }
        return sql;
    }

    /**
     * 处理Condition 返回该条件是否为true
     */
    public static boolean dealCondition(String condition) {
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = manager.getEngineByName("js");
        try {
            return (Boolean) engine.eval(condition);
        } catch (Exception e) {
            throw new BizException(XML_PARSE_ERROR, e);
        }
    }
}
