#开发环境本地使用
#spring:
#  profiles:
#    include: nacos-discovery, nacos-config
#  cloud:
#    nacos:
#      discovery:
#        namespace: cpe-base
#        group: cpe-group
#      config:
#        file-extension: yml
#        namespace: cpe-base
#      server-addr: 111.229.161.129:8848
#      username: nacos
#      password: nacos
#  application:
#    name: cpe-products-monitor-backend
#  messages:
#    basename: i18n/messages
#    encoding: UTF-8


#线上环境使用
management:
  endpoints:
    web:
      exposure:
        include: '*'
    jmx:
      exposure:
        include: '*'

spring:
  application:
    name: cpe-products-monitor-backend
  messages:
    basename: i18n/messages
    encoding: UTF-8

mybatis-plus:
  configuration:
    mapUnderscoreToCamelCase: false
  mapperLocations: classpath*:/mapper/**/*.xml