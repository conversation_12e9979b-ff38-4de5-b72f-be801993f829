spring:
  cloud:
    nacos:
      config:
        enabled: false
      discovery:
        enabled: false

---
spring:
  config:
    activate:
      on-profile: nacos-discovery
  cloud:
    nacos:
      discovery:
        enabled: true
dubbo:
  registry:
    address: nacos://${spring.cloud.nacos.discovery.server-addr:${spring.cloud.nacos.server-addr}}
    group: DUBBO_SERVICE_GROUP
    parameters:
      namespace: ${spring.cloud.nacos.discovery.namespace}

---
spring:
  config:
    activate:
      on-profile: nacos-config
    import:
      - nacos:application.yml?group=COMMON_GROUP
      - nacos:${spring.application.name}.yml
  cloud:
    nacos:
      config:
        enabled: true
---
spring:
  config:
    activate:
      on-profile: nacos-config & dev
    import:
      - nacos:application-dev.yml?group=COMMON_GROUP
      - nacos:${spring.application.name}-dev.yml
---
spring:
  config:
    activate:
      on-profile: nacos-config & run
    import:
      - nacos:application-run.yml?group=COMMON_GROUP
      - nacos:${spring.application.name}-run.yml
---
spring:
  config:
    activate:
      on-profile: nacos-config & test
    import:
      - nacos:application-test.yml?group=COMMON_GROUP
      - nacos:${spring.application.name}-test.yml

---
spring:
  cloud:
    consul:
      enabled: false
      config:
        enabled: false
