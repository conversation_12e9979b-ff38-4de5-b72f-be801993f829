<Package desc="开户运营监控API">
    <Func ID="MONITOR2000" MODULE_NAME="个人在岗统计表总数查询" TYPE="QUERY" DESC="个人在岗统计表总数查询">
        <InParam>
            <Param Code="staff_no" Name="员工号"/>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
            <Param Code="time_property" Name="时间属性"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="operator_no" Name="工号"/>
            <Param Code="operator_name" Name="见证岗姓名"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			select
				count(1) as total_num
			from
				crh_monitor.personalondutystatistics
			where 1=1
			<if condition="@start_time != null && @start_time.length() > 0">
				and report_date >= to_date('@start_time' , 'yyyy-mm-dd hh24:mi:ss')
			</if>
			<if condition="@end_time != null && @end_time.length() > 0">
				and report_date <= to_date('@end_time' , 'yyyy-mm-dd hh24:mi:ss')
			</if>
			<if condition="@operator_no != null && @operator_no.length() > 0">
				and operator_no = '@operator_no'
			</if>
			<if condition="@branch_no != null && @branch_no.length() > 0">
				and instr(','||'@branch_no'||',',','||branch_no||',')>0
			</if>
			<if condition="@operator_name != null && @operator_name.length() > 0">
				and operator_name = '@operator_name'
			</if>
			<if condition="@up_branch_no != null && @up_branch_no.length() > 0">
				and instr(','||'@up_branch_no'||',',','||up_branch_no||',')>0
			</if>
			<if condition="@staff_no != null && @staff_no.length() > 0">
				and ((instr(','||(select en_branch_nos from crh_user.operatorinfo where staff_no = '@staff_no')||',',','||branch_no||',')>0 and branch_no != '3') or ( select branch_No from crh_user.operatorinfo where staff_no = '@staff_no') = '3')
			</if>
		]]></Content>
    </Func>

    <Func ID="MONITOR2001" MODULE_NAME="个人在岗统计表分页查询" TYPE="QUERY" DESC="个人在岗统计表分页查询">
        <InParam>
            <Param Code="staff_no" Name="员工号"/>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
            <Param Code="time_property" Name="时间属性"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="operator_no" Name="工号"/>
            <Param Code="operator_name" Name="见证岗姓名"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			select
				*
			from
			(
				select
					report_date,
					operator_name,
					operator_no,
					branch_no,
					branch_name,
					up_branch_name,
					begin_online_time,
					end_online_time,
					online_time_sum,
					begin_deal_time,
					end_deal_time,
					sum_deal_time,
					pause_sum,
					recycle_sum,
					deliver_sum,
					rownum rn
				from
				(
					select
						report_date,
						operator_name,
						operator_no,
						branch_no,
						branch_name,
						up_branch_name,
						begin_online_time,
						end_online_time,
						online_time_sum,
						begin_deal_time,
						end_deal_time,
						sum_deal_time,
						pause_sum,
						recycle_sum,
						deliver_sum
					from
						crh_monitor.personalondutystatistics
					where 1=1
					<if condition="@start_time != null && @start_time.length() > 0">
						and report_date >= to_date('@start_time' , 'yyyy-mm-dd hh24:mi:ss')
					</if>
					<if condition="@end_time != null && @end_time.length() > 0">
						and report_date <= to_date('@end_time' , 'yyyy-mm-dd hh24:mi:ss')
					</if>
					<if condition="@operator_no != null && @operator_no.length() > 0">
						and operator_no = '@operator_no'
					</if>
					<if condition="@branch_no != null && @branch_no.length() > 0">
						and instr(','||'@branch_no'||',',','||branch_no||',')>0
					</if>
					<if condition="@operator_name != null && @operator_name.length() > 0">
						and operator_name = '@operator_name'
					</if>
					<if condition="@up_branch_no != null && @up_branch_no.length() > 0">
						and instr(','||'@up_branch_no'||',',','||up_branch_no||',')>0
					</if>
					<if condition="@staff_no != null && @staff_no.length() > 0">
						and ((instr(','||(select en_branch_nos from crh_user.operatorinfo where staff_no = '@staff_no')||',',','||branch_no||',')>0 and branch_no != '3') or ( select branch_No from crh_user.operatorinfo where staff_no = '@staff_no') = '3')
					</if>
					order by report_date desc, operator_no
				)
			)
			<if condition="@page_num != null && @page_size != null">
				WHERE rn BETWEEN ( @page_num-1 ) * @page_size + 1 AND @page_num * @page_size
			</if>
		]]></Content>
    </Func>

    <Func ID="MONITOR2002" MODULE_NAME="驳回明细表总数查询" TYPE="QUERY" DESC="驳回明细表总数查询">
        <InParam>
            <Param Code="staff_no" Name="员工号"/>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
            <Param Code="time_property" Name="时间属性"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="id_no" Name="证件号码"/>
            <Param Code="user_name" Name="客户姓名"/>
            <Param Code="task_type" Name="任务类型"/>
            <Param Code="audit_operator_no" Name="见证人工号"/>
            <Param Code="review_operator_no" Name="复核人工号"/>
            <Param Code="secondary_review_operator_no" Name="二次复核人工号"/>
            <Param Code="call_flag" Name="外呼标志"/>
            <Param Code="match_labels_flag" Name="增加标签标志"/>
            <Param Code="busin_type" Name="业务类型"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="activity_no" Name="活动码"/>
            <Param Code="video_type" Name="视频见证方式"/>
            <Param Code="marketing_team" Name="营销团队"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			with a as (
				select
					request_no,
					task_id,
					time_property,
					audit_operator_no,
					audit_operator_name,
					review_operator_no,
					review_operator_name,
					sec_rv_operator_no,
					sec_rv_operator_name,
					id_no,
					user_name,
					user_branch_no,
					user_branch_name,
					user_up_branch_no,
					user_up_branch_name,
					request_status,
					call_flag,
                    match_labels,
					busin_type,
					channel_code,
                    activity_no,
					replace(replace(request_status, '-4', ''), '-8', '')  as task_type,
					decode(request_status, 'audit-4', audit_finish_datetime, 'audit-8', audit_finish_datetime, 'review-4', review_finish_datetime,  sec_rv_finish_datetime) as finish_datetime,
					decode(request_status, 'audit-4', audit_op_content,'audit-8', audit_op_content, 'review-4', review_op_content,  sec_rv_op_content) as op_content
				from crh_ac.businprocessrequestaudittrail
				where request_status in ('audit-4', 'audit-8', 'review-4', 'secondary_review-4')
				<if condition="@id_no != null && @id_no.length() > 0">
					and id_no = '@id_no'
				</if>
				<if condition="@user_name != null && @user_name.length() > 0">
					and user_name = '@user_name'
				</if>
				<if condition="@branch_no != null && @branch_no.length() > 0">
					and instr(','||'@branch_no'||',',','||user_branch_no||',')>0
				</if>
				<if condition="@staff_no != null && @staff_no.length() > 0">
					and instr(','||(select en_branch_nos from crh_user.operatorinfo where staff_no = '@staff_no')||',',','||user_branch_no||',')>0
				</if>
				<if condition="@time_property != null && @time_property.length() > 0">
					and time_property = '@time_property'
				</if>
				<if condition="@up_branch_no != null && @up_branch_no.length() > 0">
					and instr(','||'@up_branch_no'||',',','||user_up_branch_no||',')>0
				</if>
				<if condition="@audit_operator_no != null && @audit_operator_no.length() > 0">
					and audit_operator_no = '@audit_operator_no'
				</if>
				<if condition="@review_operator_no != null && @review_operator_no.length() > 0">
					and review_operator_no = '@review_operator_no'
				</if>
				<if condition="@secondary_review_operator_no != null && @secondary_review_operator_no.length() > 0">
					and sec_rv_operator_no = '@secondary_review_operator_no'
				</if>
				<if condition="@call_flag != null && @call_flag.length() > 0">
					and call_flag = '@call_flag'
				</if>
				<if condition="@match_labels_flag != null && @match_labels_flag.length() > 0">
					and instr(','||'@match_labels_flag'||',', ','||match_labels||',')>0
				</if>
				<if condition="@busin_type != null && @busin_type.length() > 0">
					and instr(','||'@busin_type'||',',','||busin_type||',')>0
				</if>
				<if condition="@channel_code != null && @channel_code.length() > 0">
					and channel_code = '@channel_code'
				</if>
				<if condition="@activity_no != null && @activity_no.length() > 0">
					and activity_no = '@activity_no'
				</if>
				<if condition="@video_type != null && @video_type.length() > 0">
					and video_type = '@video_type'
				</if>
				<if condition="@marketing_team != null && @marketing_team.length() > 0">
					and marketing_team = '@marketing_team'
				</if>
			)
			select
				count(1) as total_num
			from
				a
			where 1=1
			<if condition="@start_time != null && @start_time.length() > 0">
				and a.finish_datetime >= to_date('@start_time' , 'yyyy-mm-dd hh24:mi:ss')
			</if>
			<if condition="@end_time != null && @end_time.length() > 0">
				and a.finish_datetime <= to_date('@end_time' , 'yyyy-mm-dd hh24:mi:ss')
			</if>
			<if condition="@task_type != null && @task_type.length() > 0">
				and instr(','||'@task_type'||',',','||a.task_type||',')>0
			</if>
		]]></Content>
    </Func>

    <Func ID="MONITOR2003" MODULE_NAME="驳回明细表分页查询" TYPE="QUERY" DESC="驳回明细表分页查询">
        <InParam>
            <Param Code="staff_no" Name="员工号"/>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
            <Param Code="time_property" Name="时间属性"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="id_no" Name="证件号码"/>
            <Param Code="user_name" Name="客户姓名"/>
            <Param Code="task_type" Name="任务类型"/>
            <Param Code="audit_operator_no" Name="见证人工号"/>
            <Param Code="review_operator_no" Name="复核人工号"/>
            <Param Code="secondary_review_operator_no" Name="二次复核人工号"/>
            <Param Code="call_flag" Name="外呼标志"/>
            <Param Code="match_labels_flag" Name="增加标签标志"/>
            <Param Code="busin_type" Name="业务类型"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="activity_no" Name="活动码"/>
            <Param Code="video_type" Name="视频见证方式"/>
            <Param Code="marketing_team" Name="营销团队"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			with a as (
				select
					request_no,
					task_id,
					time_property,
					audit_operator_no,
					audit_operator_name,
					review_operator_no,
					review_operator_name,
					sec_rv_operator_no as secondary_review_operator_no,
					sec_rv_operator_name as secondary_review_operator_name,
					id_kind,
					id_no,
					user_name,
					user_branch_no,
					user_branch_name,
					user_up_branch_no,
					user_up_branch_name,
					request_status,
					call_flag,
                    match_labels,
					busin_type,
					channel_code,
                    activity_no,
                    video_type,
					marketing_team,
					replace(replace(request_status, '-4', ''), '-8', '')  as task_type,
					decode(request_status, 'audit-4', audit_finish_datetime, 'audit-8', audit_finish_datetime, 'review-4', review_finish_datetime,  sec_rv_finish_datetime) as finish_datetime,
					decode(request_status, 'audit-4', audit_op_content,'audit-8', audit_op_content, 'review-4', review_op_content,  sec_rv_op_content) as op_content
				from crh_ac.businprocessrequestaudittrail
				where request_status in ('audit-4', 'audit-8', 'review-4', 'secondary_review-4')
				<if condition="@id_no != null && @id_no.length() > 0">
					and id_no = '@id_no'
				</if>
				<if condition="@user_name != null && @user_name.length() > 0">
					and user_name = '@user_name'
				</if>
				<if condition="@branch_no != null && @branch_no.length() > 0">
					and instr(','||'@branch_no'||',',','||user_branch_no||',')>0
				</if>
				<if condition="@staff_no != null && @staff_no.length() > 0">
					and instr(','||(select en_branch_nos from crh_user.operatorinfo where staff_no = '@staff_no')||',',','||user_branch_no||',')>0
				</if>
				<if condition="@time_property != null && @time_property.length() > 0">
					and time_property = '@time_property'
				</if>
				<if condition="@up_branch_no != null && @up_branch_no.length() > 0">
					and instr(','||'@up_branch_no'||',',','||user_up_branch_no||',')>0
				</if>
				<if condition="@audit_operator_no != null && @audit_operator_no.length() > 0">
					and audit_operator_no = '@audit_operator_no'
				</if>
				<if condition="@review_operator_no != null && @review_operator_no.length() > 0">
					and review_operator_no = '@review_operator_no'
				</if>
				<if condition="@secondary_review_operator_no != null && @secondary_review_operator_no.length() > 0">
					and sec_rv_operator_no = '@secondary_review_operator_no'
				</if>
				<if condition="@call_flag != null && @call_flag.length() > 0">
					and call_flag = '@call_flag'
				</if>
				<if condition="@match_labels_flag != null && @match_labels_flag.length() > 0">
					and instr(','||'@match_labels_flag'||',', ','||match_labels||',')>0
				</if>
				<if condition="@busin_type != null && @busin_type.length() > 0">
					and instr(','||'@busin_type'||',',','||busin_type||',')>0
				</if>
				<if condition="@channel_code != null && @channel_code.length() > 0">
					and channel_code = '@channel_code'
				</if>
				<if condition="@activity_no != null && @activity_no.length() > 0">
					and activity_no = '@activity_no'
				</if>
				<if condition="@video_type != null && @video_type.length() > 0">
					and video_type = '@video_type'
				</if>
				<if condition="@marketing_team != null && @marketing_team.length() > 0">
					and marketing_team = '@marketing_team'
				</if>
			)
			select
				*
			from
			(
				select
					request_no,
					task_id,
					time_property,
					audit_operator_no,
					audit_operator_name,
					review_operator_no,
					review_operator_name,
					secondary_review_operator_no,
					secondary_review_operator_name,
					id_kind,
					id_no,
					user_name as client_name,
					user_branch_no,
					user_branch_name as branch_name,
					user_up_branch_no,
					user_up_branch_name as up_branch_name,
					request_status,
					task_type,
					finish_datetime,
					op_content,
					call_flag,
					match_labels,
					busin_type,
					channel_code,
                    activity_no,
                    video_type,
					marketing_team,
					rownum rn
				from
				(
					select
						request_no,
						task_id,
						time_property,
						audit_operator_no,
						audit_operator_name,
						review_operator_no,
						review_operator_name,
						secondary_review_operator_no,
						secondary_review_operator_name,
						id_kind,
						id_no,
						user_name,
						user_branch_no,
						user_branch_name,
						user_up_branch_no,
						user_up_branch_name,
						request_status,
						task_type,
						finish_datetime,
						op_content,
						call_flag,
						match_labels,
						channel_code,
						activity_no,
						busin_type,
						video_type,
						marketing_team
					from
						a
					where 1=1
					<if condition="@start_time != null && @start_time.length() > 0">
						and a.finish_datetime >= to_date('@start_time' , 'yyyy-mm-dd hh24:mi:ss')
					</if>
					<if condition="@end_time != null && @end_time.length() > 0">
						and a.finish_datetime <= to_date('@end_time' , 'yyyy-mm-dd hh24:mi:ss')
					</if>
					<if condition="@task_type != null && @task_type.length() > 0">
						and instr(','||'@task_type'||',',','||a.task_type||',')>0
					</if>
					order by a.finish_datetime desc
				)
			)
			<if condition="@page_num != null && @page_size != null">
				WHERE rn BETWEEN ( @page_num-1 ) * @page_size + 1 AND @page_num * @page_size
			</if>
		]]></Content>
    </Func>

    <Func ID="MONITOR2006" MODULE_NAME="个人业务量报表总数查询" TYPE="QUERY" DESC="个人业务量报表总数查询">
        <InParam>
            <Param Code="staff_no" Name="员工号"/>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
            <Param Code="time_property" Name="时间属性"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="en_branch_nos" Name="可操作营业部"/>
            <Param Code="operator_no" Name="工号"/>
            <Param Code="operator_name" Name="姓名"/>
            <Param Code="busin_type" Name="业务类型"/>
            <Param Code="video_type" Name="视频见证方式"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			with e as (
				select
					a.operator_no,
					a.operator_name,
					a.operator_branch_no,
					a.operator_up_branch_no,
					a.branch_name,
					a.up_branch_name,
					a.request_no,
					a.task_type,
					a.task_status,
					a.task_datetime,
					a.deal_datetime,
					a.finish_datetime,
					a.pause_flag,
					a.first_pause_datetime,
					a.first_deal_datetime,
					a.first_deal_operator_no,
					a.end_deal_operator_no,
					a.end_deal_datetime,
					a.request_datetime,
					a.audit_finish_datetime,
					a.first_pause_operator_no
				from crh_ac.flowtaskrecorddetails a
				where invalid_flag = '0'
				and   a.operator_no != ' '
				<if condition="@start_time != null && @start_time.length() > 0">
					and a.finish_datetime >= to_date('@start_time' , 'yyyy-mm-dd hh24:mi:ss')
				</if>
				<if condition="@end_time != null && @end_time.length() > 0">
					and a.finish_datetime <= to_date('@end_time' , 'yyyy-mm-dd hh24:mi:ss')
				</if>
				<if condition="@time_property != null && @time_property.length() > 0">
					and time_property = '@time_property'
				</if>
				<if condition="@branch_no != null && @branch_no.length() > 0">
					and instr(','||'@branch_no'||',',','||a.operator_branch_no||',')>0
				</if>
				<if condition="@en_branch_nos != null && @en_branch_nos.length() > 0">
					AND instr(',' || '@en_branch_nos' || ',', ',' || a.branch_no || ',') > 0
				</if>
				<if condition="@up_branch_no != null && @up_branch_no.length() > 0">
					and instr(','||'@up_branch_no'||',',','||a.operator_up_branch_no||',')>0
				</if>
				<if condition="@operator_no != null && @operator_no.length() > 0">
					and a.operator_no = '@operator_no'
				</if>
				<if condition="@operator_name != null && @operator_name.length() > 0">
					and a.operator_name = '@operator_name'
				</if>
				<if condition="@busin_type != null && @busin_type.length() > 0">
					and instr(','||'@busin_type'||',',','||busin_type||',')>0
				</if>
				<if condition="@video_type != null && @video_type.length() > 0">
					and a.video_type = '@video_type'
				</if>
			),t1 as (
				select
					e.operator_no,
					e.operator_name,
					e.operator_branch_no,
					e.operator_up_branch_no,
					sum(decode(e.task_type, 'audit', decode(e.task_status, '3', 1, 0), 0)) as audit_success_sum,
					sum(decode(e.task_type, 'audit', decode(e.task_status, '4', 1, '8', 1, 0), 0)) as audit_fail_sum,
					sum(decode(e.task_type, 'review', decode(e.task_status, '3', 1, 0), 0)) as review_success_sum,
					sum(decode(e.task_type, 'review', decode(e.task_status, '4', 1, 0), 0)) as review_fail_sum,
					sum(decode(e.task_type, 'secondary_review', decode(e.task_status, '3', 1, 0), 0)) as secondary_review_success_sum,
					sum(decode(e.task_type, 'secondary_review', decode(e.task_status, '4', 1, 0), 0)) as secondary_review_fail_sum,
					round(sum(decode(e.task_type, 'audit', decode(e.deal_datetime, null, 0, e.deal_datetime - e.request_datetime), 0)) * 24 * 60 *60) as audit_wait_time,
					round(sum(decode(e.task_type, 'audit', decode(e.finish_datetime, null, 0, e.finish_datetime - e.deal_datetime), 0)) * 24 * 60 *60) as audit_deal_time,
					round(sum(decode(e.task_type, 'review', decode(e.deal_datetime, null, 0, e.deal_datetime - e.audit_finish_datetime), 0)) * 24 * 60 * 60) as review_wait_time,
					round(sum(decode(e.task_type, 'review', decode(e.finish_datetime, null, 0, e.finish_datetime - e.deal_datetime), 0)) * 24 * 60 * 60) as review_deal_time,
					round(sum(decode(e.task_type, 'secondary_review', decode(e.deal_datetime, null, 0, e.deal_datetime - e.task_datetime), 0)) * 24 * 60 * 60) as secondary_review_wait_time,
					round(sum(decode(e.task_type, 'secondary_review', decode(e.finish_datetime, null, 0, e.finish_datetime - e.deal_datetime), 0)) * 24 * 60 * 60) as secondary_review_deal_time
				from e
				group by e.operator_no, e.operator_name, e.operator_branch_no, e.operator_up_branch_no
			)
			select
				count(1) as total_num
			from
				t1
			where 1=1
		]]></Content>
    </Func>

    <Func ID="MONITOR2007" MODULE_NAME="个人业务量报表分页查询" TYPE="QUERY" DESC="个人业务量报表分页查询">
        <InParam>
            <Param Code="staff_no" Name="员工号"/>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
            <Param Code="time_property" Name="时间属性"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="en_branch_nos" Name="可操作营业部"/>
            <Param Code="operator_no" Name="工号"/>
            <Param Code="operator_name" Name="姓名"/>
            <Param Code="busin_type" Name="业务类型"/>
            <Param Code="video_type" Name="视频见证方式"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
		    with e as (
				select
					a.operator_no,
					a.operator_name,
					a.operator_branch_no,
					a.operator_up_branch_no,
					a.branch_name,
					a.up_branch_name,
					a.request_no,
					a.task_type,
					a.task_status,
					a.task_datetime,
					a.deal_datetime,
					a.finish_datetime,
					a.pause_flag,
					a.first_pause_datetime,
					a.first_deal_datetime,
					a.first_deal_operator_no,
					a.end_deal_operator_no,
					a.end_deal_datetime,
					a.request_datetime,
					a.audit_finish_datetime,
					decode(a.first_pause_operator_no, ' ', operator_no, a.first_pause_operator_no) as first_pause_operator_no
				from crh_ac.flowtaskrecorddetails a
				where invalid_flag = '0'
				and   a.operator_no != ' '
				<if condition="@start_time != null && @start_time.length() > 0">
					and a.finish_datetime >= to_date('@start_time' , 'yyyy-mm-dd hh24:mi:ss')
				</if>
				<if condition="@end_time != null && @end_time.length() > 0">
					and a.finish_datetime <= to_date('@end_time' , 'yyyy-mm-dd hh24:mi:ss')
				</if>
				<if condition="@time_property != null && @time_property.length() > 0">
					and time_property = '@time_property'
				</if>
				<if condition="@branch_no != null && @branch_no.length() > 0">
					and instr(','||'@branch_no'||',',','||a.operator_branch_no||',')>0
				</if>
				<if condition="@en_branch_nos != null && @en_branch_nos.length() > 0">
					AND instr(',' || '@en_branch_nos' || ',', ',' || a.branch_no || ',') > 0
				</if>
				<if condition="@up_branch_no != null && @up_branch_no.length() > 0">
					and instr(','||'@up_branch_no'||',',','||a.operator_up_branch_no||',')>0
				</if>
				<if condition="@operator_no != null && @operator_no.length() > 0">
					and a.operator_no = '@operator_no'
				</if>
				<if condition="@operator_name != null && @operator_name.length() > 0">
					and a.operator_name = '@operator_name'
				</if>
				<if condition="@busin_type != null && @busin_type.length() > 0">
					and instr(','||'@busin_type'||',',','||busin_type||',')>0
				</if>
				<if condition="@video_type != null && @video_type.length() > 0">
					and a.video_type = '@video_type'
				</if>
			),t1 as (
				select
					e.operator_no,
					e.operator_name,
					e.operator_branch_no,
					e.operator_up_branch_no,
					sum(decode(e.task_type, 'audit', decode(e.task_status, '3', 1, 0), 0)) as audit_success_sum,
					sum(decode(e.task_type, 'audit', decode(e.task_status, '4', 1, '8', 1, 0), 0)) as audit_fail_sum,
					sum(decode(e.task_type, 'review', decode(e.task_status, '3', 1, 0), 0)) as review_success_sum,
					sum(decode(e.task_type, 'review', decode(e.task_status, '4', 1, 0), 0)) as review_fail_sum,
					sum(decode(e.task_type, 'secondary_review', decode(e.task_status, '3', 1, 0), 0)) as secondary_review_success_sum,
					sum(decode(e.task_type, 'secondary_review', decode(e.task_status, '4', 1, 0), 0)) as secondary_review_fail_sum,
					round(sum(decode(e.task_type, 'audit', decode(e.deal_datetime, null, 0, e.deal_datetime - e.request_datetime), 0)) * 24 * 60 *60) as audit_wait_time,
					round(sum(decode(e.task_type, 'audit', decode(e.finish_datetime, null, 0, e.finish_datetime - e.deal_datetime), 0)) * 24 * 60 *60) as audit_deal_time,
					round(sum(decode(e.task_type, 'review', decode(e.deal_datetime, null, 0, e.deal_datetime - e.audit_finish_datetime), 0)) * 24 * 60 * 60) as review_wait_time,
					round(sum(decode(e.task_type, 'review', decode(e.finish_datetime, null, 0, e.finish_datetime - e.deal_datetime), 0)) * 24 * 60 * 60) as review_deal_time,
					round(sum(decode(e.task_type, 'secondary_review', decode(e.deal_datetime, null, 0, e.deal_datetime - e.task_datetime), 0)) * 24 * 60 * 60) as secondary_review_wait_time,
					round(sum(decode(e.task_type, 'secondary_review', decode(e.finish_datetime, null, 0, e.finish_datetime - e.deal_datetime), 0)) * 24 * 60 * 60) as secondary_review_deal_time
				from e
				group by e.operator_no, e.operator_name, e.operator_branch_no, e.operator_up_branch_no
			), t2 as(
				select
					e.end_deal_operator_no,
					sum(decode(e.pause_flag, '0', 1, 0)) as no_handup_sum,
					sum(decode(e.pause_flag, '1', 1, 0)) as handup_sum,
					round(sum(decode(e.pause_flag, '0', e.first_deal_datetime - e.request_datetime, 0)) * 24 * 60 *60) as no_handup_time,
					round(sum(decode(e.pause_flag, '1', e.first_deal_datetime - e.request_datetime, 0)) * 24 * 60 *60) as handup_time,
					round(sum(decode(e.pause_flag, '1', e.end_deal_datetime - e.first_pause_datetime, 0)) * 24 * 60 *60) as handup_deal_time
				from e
				where e.task_type = 'audit'
				group by e.end_deal_operator_no
			),h as (
				select
					t1.operator_no,
					t1.operator_name,
					t1.operator_branch_no,
					t1.operator_up_branch_no,
					t1.audit_success_sum,
					t1.audit_fail_sum,
					t1.audit_success_sum + t1.audit_fail_sum as audit_total_sum,
					t1.review_success_sum,
					t1.review_fail_sum,
					t1.secondary_review_success_sum,
					t1.secondary_review_fail_sum,
					t1.review_success_sum + t1.review_fail_sum as review_total_sum,
					t1.audit_wait_time,
					t1.audit_deal_time,
					t1.review_wait_time,
					t1.review_deal_time,
					t2.handup_sum,
					t2.no_handup_sum,
					t2.handup_time,
					t2.handup_deal_time,
					t2.no_handup_time
				from t1, t2
				where t1.operator_no = t2.end_deal_operator_no(+)
			), m as (
				select
					'合计' as operator_name,
					'--' as operator_no,
					'--' as up_branch_name,
					'--' as branch_name,
					nvl(sum(h.audit_success_sum), 0) as audit_success_sum,
					nvl(sum(h.audit_fail_sum), 0) as audit_fail_sum,
					nvl(sum(h.review_success_sum), 0) as review_success_sum,
					nvl(sum(h.review_fail_sum), 0) as review_fail_sum,
					nvl(sum(h.secondary_review_success_sum), 0) as secondary_review_success_sum,
					nvl(sum(h.secondary_review_fail_sum), 0) as secondary_review_fail_sum,
					decode(sum(h.audit_total_sum) , 0 , 0 , round(sum(h.audit_wait_time)/sum(h.audit_total_sum))) as avg_audit_wait_time,
					decode(sum(h.audit_total_sum) , 0 , 0 , round(sum(h.audit_deal_time)/sum(h.audit_total_sum))) as avg_audit_deal_time,
					decode(sum(h.review_total_sum) , 0 , 0 , round(sum(h.review_wait_time)/sum(h.review_total_sum))) as avg_review_wait_time,
					decode(sum(h.review_total_sum) , 0 , 0 , round(sum(h.review_deal_time)/sum(h.review_total_sum))) as avg_review_deal_time,
					decode(sum(h.handup_sum) , 0 , 0 , round(sum(h.handup_time)/sum(h.handup_sum))) as avg_handup_time,
					decode(sum(h.handup_sum) , 0 , 0 , round(sum(h.handup_deal_time)/sum(h.handup_sum))) as avg_handup_deal_time,
					decode(sum(h.no_handup_sum) , 0 , 0 , round(sum(h.no_handup_time)/sum(h.no_handup_sum))) as avg_no_handup_time
				from
					h, crh_user.allbranch t1, crh_user.allbranch t2
				where h.operator_branch_no = t1.branch_no (+)
				and h.operator_up_branch_no = t2.branch_no (+)
			)
			select
				m.operator_name,
				m.operator_no,
				m.up_branch_name,
				m.branch_name,
				m.audit_success_sum,
				m.audit_fail_sum,
				m.review_success_sum,
				m.review_fail_sum,
				m.secondary_review_success_sum,
				m.secondary_review_fail_sum,
				m.avg_audit_wait_time,
				m.avg_audit_deal_time,
				m.avg_review_wait_time,
				m.avg_review_deal_time,
				m.avg_handup_time,
				m.avg_handup_deal_time,
				m.avg_no_handup_time,
				rownum rn
			from
				m
			union all
			select
				*
			from
			(
				select
                    operator_name,
                    operator_no,
                    up_branch_name,
                    branch_name,
                    audit_success_sum,
                    audit_fail_sum,
                    review_success_sum,
                    review_fail_sum,
                    secondary_review_success_sum,
                    secondary_review_fail_sum,
                    avg_audit_wait_time,
                    avg_audit_deal_time,
                    avg_review_wait_time,
                    avg_review_deal_time,
                    avg_handup_time,
					avg_handup_deal_time,
                    avg_no_handup_time,
                    rownum rn
                from (
					select
						h.operator_name,
						h.operator_no,
						t2.branch_name as up_branch_name,
						t1.branch_name as branch_name,
						h.audit_success_sum,
						h.audit_fail_sum,
						h.review_success_sum,
						h.review_fail_sum,
						h.secondary_review_success_sum,
						h.secondary_review_fail_sum,
						decode(h.audit_total_sum , 0 , 0 , round(h.audit_wait_time/h.audit_total_sum)) as avg_audit_wait_time,
						decode(h.audit_total_sum , 0 , 0 , round(h.audit_deal_time/h.audit_total_sum)) as avg_audit_deal_time,
						decode(h.review_total_sum , 0 , 0 , round(h.review_wait_time/h.review_total_sum)) as avg_review_wait_time,
						decode(h.review_total_sum , 0 , 0 , round(h.review_deal_time/h.review_total_sum)) as avg_review_deal_time,
						decode(h.handup_sum , 0 , 0 , round(h.handup_time/h.handup_sum)) as avg_handup_time,
						decode(h.handup_sum , 0 , 0 , round(h.handup_deal_time/h.handup_sum)) as avg_handup_deal_time,
						decode(h.no_handup_sum , 0 , 0 , round(h.no_handup_time/h.no_handup_sum)) as avg_no_handup_time
					from
						h, crh_user.allbranch t1, crh_user.allbranch t2
					where h.operator_branch_no = t1.branch_no (+)
					and h.operator_up_branch_no = t2.branch_no (+)
					order by h.operator_no
				)
			)
			<if condition="@page_num != null && @page_size != null">
				WHERE rn BETWEEN ( @page_num-1 ) * @page_size + 1 AND @page_num * @page_size
			</if>
		]]></Content>
    </Func>

    <Func ID="MONITOR2008" MODULE_NAME="营业部业务量报表总数查询" TYPE="QUERY" DESC="营业部业务量报表总数查询">
        <InParam>
            <Param Code="staff_no" Name="员工号"/>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
            <Param Code="time_property" Name="时间属性"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="busin_type" Name="业务类型"/>
            <Param Code="video_type" Name="视频见证方式"/>
            <Param Code="marketing_team" Name="营销团队"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			with e as (
				select
					a.request_datetime,
					a.user_branch_no as branch_no,
					a.user_branch_name as branch_name,
					a.user_up_branch_no as up_branch_no,
					a.user_up_branch_name as up_branch_name,
					a.request_no,
					--0 待见证未挂起过 1 待见证挂起过 2. 见证完成未挂起 3. 见证完成挂起过 4. 复核完成未挂起 5. 核完成挂起过 6. 其他
					decode(audit_finish_datetime, null, a.pause_flag, decode(a.review_finish_datetime, null, decode(a.pause_flag, '0', '2', '1', '3', '6'), decode(a.pause_flag, '0', '4', '1', '5', '6'))) as flag,
					a.request_status,
					a.audit_create_datetime,
					a.audit_deal_datetime,
					a.audit_finish_datetime,
					a.review_create_datetime,
					a.review_deal_datetime,
					a.review_finish_datetime,
					a.first_pause_datetime,
					a.first_deal_datetime,
					a.first_deal_operator_no,
					a.end_deal_datetime
				from crh_ac.businprocessrequestaudittrail a
				where 1 =1
				and   a.user_branch_no != ' '
				<if condition="@start_time != null && @start_time.length() > 0">
					and a.request_datetime >= to_date('@start_time' , 'yyyy-mm-dd hh24:mi:ss')
				</if>
				<if condition="@end_time != null && @end_time.length() > 0">
					and a.request_datetime <= to_date('@end_time' , 'yyyy-mm-dd hh24:mi:ss')
				</if>
				<if condition="@time_property != null && @time_property.length() > 0">
					and a.time_property = '@time_property'
				</if>
				<if condition="@branch_no != null && @branch_no.length() > 0">
					and instr(','||'@branch_no'||',',','||a.user_branch_no||',')>0
				</if>
				<if condition="@staff_no != null && @staff_no.length() > 0">
					and instr(','||(select en_branch_nos from crh_user.operatorinfo where staff_no = '@staff_no')||',',','||a.user_branch_no||',')>0
				</if>
				<if condition="@up_branch_no != null && @up_branch_no.length() > 0">
					and instr(','||'@up_branch_no'||',',','||a.user_up_branch_no||',')>0
				</if>
				<if condition="@busin_type != null && @busin_type.length() > 0">
					and instr(','||'@busin_type'||',',','||busin_type||',')>0
				</if>
				<if condition="@video_type != null && @video_type.length() > 0">
					and a.video_type = '@video_type'
				</if>
				<if condition="@marketing_team != null && @marketing_team.length() > 0">
					and marketing_team = '@marketing_team'
				</if>
			)
			select
				count(distinct e.branch_no) as total_num
			from
				e
		]]></Content>
    </Func>

    <Func ID="MONITOR2009" MODULE_NAME="营业部业务量报表分页查询" TYPE="QUERY" DESC="营业部业务量报表分页查询">
        <InParam>
            <Param Code="staff_no" Name="员工号"/>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
            <Param Code="time_property" Name="时间属性"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="busin_type" Name="业务类型"/>
            <Param Code="video_type" Name="视频见证方式"/>
            <Param Code="marketing_team" Name="营销团队"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			with e as (
				select
					a.request_datetime,
					a.user_branch_no as branch_no,
					a.user_branch_name as branch_name,
					a.user_up_branch_no as up_branch_no,
					a.user_up_branch_name as up_branch_name,
					a.request_no,
					--0 待见证未挂起过 1 待见证挂起过 2. 见证完成未挂起 3. 见证完成挂起过 4. 复核完成未挂起 5. 核完成挂起过 6. 其他
					decode(audit_finish_datetime, null, a.pause_flag, decode(a.review_finish_datetime, null, decode(a.pause_flag, '0', '2', '1', '3', '6'), decode(a.pause_flag, '0', '4', '1', '5', '6'))) as flag,
					a.request_status,
					a.audit_create_datetime,
					a.audit_deal_datetime,
					a.audit_finish_datetime,
					a.review_create_datetime,
					a.review_deal_datetime,
					a.review_finish_datetime,
					a.first_pause_datetime,
					a.first_deal_datetime,
					a.first_deal_operator_no,
					a.end_deal_datetime
				from crh_ac.businprocessrequestaudittrail a
				where 1 =1
				and   a.user_branch_no != ' '
				<if condition="@start_time != null && @start_time.length() > 0">
					and a.request_datetime >= to_date('@start_time' , 'yyyy-mm-dd hh24:mi:ss')
				</if>
				<if condition="@end_time != null && @end_time.length() > 0">
					and a.request_datetime <= to_date('@end_time' , 'yyyy-mm-dd hh24:mi:ss')
				</if>
				<if condition="@time_property != null && @time_property.length() > 0">
					and a.time_property = '@time_property'
				</if>
				<if condition="@branch_no != null && @branch_no.length() > 0">
					and instr(','||'@branch_no'||',',','||a.user_branch_no||',')>0
				</if>
				<if condition="@staff_no != null && @staff_no.length() > 0">
					and instr(','||(select en_branch_nos from crh_user.operatorinfo where staff_no = '@staff_no')||',',','||a.user_branch_no||',')>0
				</if>
				<if condition="@up_branch_no != null && @up_branch_no.length() > 0">
					and instr(','||'@up_branch_no'||',',','||a.user_up_branch_no||',')>0
				</if>
				<if condition="@busin_type != null && @busin_type.length() > 0">
					and instr(','||'@busin_type'||',',','||busin_type||',')>0
				</if>
				<if condition="@video_type != null && @video_type.length() > 0">
					and a.video_type = '@video_type'
				</if>
				<if condition="@marketing_team != null && @marketing_team.length() > 0">
					and marketing_team = '@marketing_team'
				</if>
			),l as (
				select
					e.branch_no,
					e.branch_name,
					e.up_branch_name,
					count(1) as request_total_sum_repeat,
					count(distinct e.request_no) as request_total_sum,
					count(distinct decode(e.audit_finish_datetime, null, null, e.request_no)) as request_audit_finish_sum,
					count(distinct decode(e.review_finish_datetime, null, null, decode(e.request_status, 'review-4', null, e.request_no))) as request_review_success_sum,
					sum(decode(e.audit_finish_datetime, null, 0, 1)) as audit_total_sum,
					sum(decode(e.audit_finish_datetime, null, 0, decode(e.request_status, 'audit-4', 0, 'audit-8', 0, 1))) as audit_success_sum,
					sum(decode(e.audit_finish_datetime, null, 0, decode(e.request_status, 'audit-4', 1, 'audit-8', 1, 0))) as audit_fail_sum,
					sum(decode(e.review_finish_datetime, null, 0, 1)) as review_total_sum,
					sum(decode(e.review_finish_datetime, null, 0, decode(e.request_status, 'review-4', 0, 1))) as review_success_sum,
					sum(decode(e.review_finish_datetime, null, 0, decode(e.request_status, 'review-4', 1, 0))) as review_fail_sum,
					round(sum(decode(e.audit_deal_datetime, null, 0, e.audit_deal_datetime - e.request_datetime)) * 24 * 60 *60) as audit_wait_time,
					round(sum(decode(e.audit_finish_datetime, null, 0, e.audit_finish_datetime - e.audit_deal_datetime)) * 24 * 60 *60) as audit_deal_time,
					round(sum(decode(e.review_deal_datetime, null, 0, e.review_deal_datetime - e.audit_finish_datetime)) * 24 * 60 * 60) as review_wait_time,
					round(sum(decode(e.review_finish_datetime, null, 0, e.review_finish_datetime - e.review_deal_datetime)) * 24 * 60 * 60) as review_deal_time,
					sum(decode(e.flag, '0', 1, 0)) as no_handup_sum,
					sum(decode(e.flag, '1', 1, 0)) as handup_sum,
					sum(decode(e.flag, '2', 1, '4', 1, 0)) as finish_no_handup_sum,
					sum(decode(e.flag, '3', 1, '5', 1, 0)) as finish_handup_sum,
					round(sum(decode(e.flag, '2', e.first_deal_datetime - e.request_datetime, '4', e.first_deal_datetime - e.request_datetime, 0)) * 24 * 60 *60) as no_handup_deal_time,
					round(sum(decode(e.flag, '3', e.first_deal_datetime - e.request_datetime, '5', e.first_deal_datetime - e.request_datetime, 0)) * 24 * 60 *60) as handup_wait_time,
					round(sum(decode(e.flag, '3', decode(sign(e.audit_deal_datetime - e.first_pause_datetime), -1, 0, e.audit_deal_datetime - e.first_pause_datetime), '5', decode(sign(e.audit_deal_datetime - e.first_pause_datetime), -1, 0, e.audit_deal_datetime - e.first_pause_datetime), 0)) * 24 * 60 *60) as handup_deal_time
				from e
				group by e.branch_no, e.branch_name, e.up_branch_name
				order by e.branch_no
			)
			select
				*
			from
			(
				select
					branch_no,
					up_branch_name,
					branch_name,
					request_total_sum,
					request_total_sum_repeat,
					audit_total_sum,
					audit_success_sum,
					audit_fail_sum,
					review_success_sum,
					review_fail_sum,
					no_handup_sum,
					handup_sum,
					avg_audit_wait_time,
					avg_audit_deal_time,
					avg_review_wait_time,
					avg_review_deal_time,
					avg_handup_wait_time,
					avg_handup_deal_time,
					avg_no_handup_time,
					handup_rate,
					review_success_rate,
					audit_success_rate,
					rownum rn
				from
				(
					select
						l.branch_no,
						l.branch_name,
						l.up_branch_name,
						l.request_total_sum,
						l.request_total_sum_repeat,
						l.audit_total_sum,
						l.audit_success_sum,
						l.audit_fail_sum,
						l.review_success_sum,
						l.review_fail_sum,
						l.no_handup_sum,
						l.handup_sum,
						decode(l.audit_total_sum , 0 , 0 , round(l.audit_wait_time/l.audit_total_sum)) as avg_audit_wait_time,
						decode(l.audit_total_sum , 0 , 0 , round(l.audit_deal_time/l.audit_total_sum)) as avg_audit_deal_time,
						decode(l.review_total_sum , 0 , 0 , round(l.review_wait_time/l.review_total_sum)) as avg_review_wait_time,
						decode(l.review_total_sum , 0 , 0 , round(l.review_deal_time/l.review_total_sum)) as avg_review_deal_time,
						decode(l.finish_handup_sum , 0 , 0 , round(l.handup_wait_time/l.finish_handup_sum)) as avg_handup_wait_time,
						decode(l.finish_handup_sum , 0 , 0 , round(l.handup_deal_time/l.finish_handup_sum)) as avg_handup_deal_time,
						decode(l.finish_no_handup_sum , 0 , 0 , round(l.no_handup_deal_time/l.finish_no_handup_sum)) as avg_no_handup_time,
						to_char(nvl(decode(l.audit_total_sum , 0 , 0 , 100 * round(l.finish_handup_sum/l.audit_total_sum, 4)), 0),'fm9999990.00')  || '%' as handup_rate,
						to_char(nvl(decode(l.audit_success_sum + l.audit_fail_sum , 0 , 0 , 100 * round(l.review_success_sum/(l.audit_success_sum + l.audit_fail_sum), 4)), 0),'fm9999990.00') || '%' as review_success_rate,
						to_char(nvl(decode(l.request_audit_finish_sum , 0 , 0 , 100 * round(l.request_review_success_sum/l.request_audit_finish_sum, 4)), 0),'fm9999990.00') || '%' as audit_success_rate
					from
						l
				)
			)
			<if condition="@page_num != null && @page_size != null">
				WHERE rn BETWEEN ( @page_num-1 ) * @page_size + 1 AND @page_num * @page_size
			</if>
		]]></Content>
    </Func>

    <Func ID="MONITOR2010" MODULE_NAME="分公司业务量报表总数查询" TYPE="QUERY" DESC="分公司业务量报表总数查询">
        <InParam>
            <Param Code="staff_no" Name="员工号"/>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
            <Param Code="time_property" Name="时间属性"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="en_branch_nos" Name="可操作营业部"/>
            <Param Code="busin_type" Name="业务类型"/>
            <Param Code="video_type" Name="视频见证方式"/>
            <Param Code="marketing_team" Name="营销团队"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			with e as (
				select
					a.request_datetime,
					a.user_branch_no as branch_no,
					a.user_branch_name as branch_name,
					a.user_up_branch_no as up_branch_no,
					a.user_up_branch_name as up_branch_name,
					a.request_no,
					--0 待见证未挂起过 1 待见证挂起过 2. 见证完成未挂起 3. 见证完成挂起过 4. 复核完成未挂起 5. 核完成挂起过 6. 其他
					decode(audit_finish_datetime, null, a.pause_flag, decode(a.review_finish_datetime, null, decode(a.pause_flag, '0', '2', '1', '3', '6'), decode(a.pause_flag, '0', '4', '1', '5', '6'))) as flag,
					a.request_status,
					a.audit_create_datetime,
					a.audit_deal_datetime,
					a.audit_finish_datetime,
					a.review_create_datetime,
					a.review_deal_datetime,
					a.review_finish_datetime,
					a.first_pause_datetime,
					a.first_deal_datetime,
					a.first_deal_operator_no,
					a.end_deal_datetime
				from crh_ac.businprocessrequestaudittrail a
				where a.user_up_branch_no != '3'
				and   a.user_up_branch_no != ' '
				<if condition="@start_time != null && @start_time.length() > 0">
					and a.request_datetime >= to_date('@start_time' , 'yyyy-mm-dd hh24:mi:ss')
				</if>
				<if condition="@end_time != null && @end_time.length() > 0">
					and a.request_datetime <= to_date('@end_time' , 'yyyy-mm-dd hh24:mi:ss')
				</if>
				<if condition="@time_property != null && @time_property.length() > 0">
					and a.time_property = '@time_property'
				</if>
				<if condition="@branch_no != null && @branch_no.length() > 0">
					and instr(','||'@branch_no'||',',','||a.user_branch_no||',')>0
				</if>
				<if condition="@en_branch_nos != null && @en_branch_nos.length() > 0">
					AND instr(',' || '@en_branch_nos' || ',', ',' || a.user_branch_no || ',') > 0
				</if>
				<if condition="@up_branch_no != null && @up_branch_no.length() > 0">
					and instr(','||'@up_branch_no'||',',','||a.user_up_branch_no||',')>0
				</if>
				<if condition="@busin_type != null && @busin_type.length() > 0">
					and instr(','||'@busin_type'||',',','||busin_type||',')>0
				</if>
				<if condition="@video_type != null && @video_type.length() > 0">
					and a.video_type = '@video_type'
				</if>
				<if condition="@marketing_team != null && @marketing_team.length() > 0">
					and marketing_team = '@marketing_team'
				</if>
			)
			select
				count(distinct e.up_branch_no) as total_num
			from
				e
		]]></Content>
    </Func>

    <Func ID="MONITOR2011" MODULE_NAME="分公司业务量报表分页查询" TYPE="QUERY" DESC="分公司业务量报表分页查询">
        <InParam>
            <Param Code="staff_no" Name="员工号"/>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
            <Param Code="time_property" Name="时间属性"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="en_branch_nos" Name="可操作营业部"/>
            <Param Code="busin_type" Name="业务类型"/>
            <Param Code="video_type" Name="视频见证方式"/>
            <Param Code="marketing_team" Name="营销团队"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			with e as (
				select
					a.request_datetime,
					a.user_branch_no as branch_no,
					a.user_branch_name as branch_name,
					a.user_up_branch_no as up_branch_no,
					a.user_up_branch_name as up_branch_name,
					a.request_no,
					--0 待见证未挂起过 1 待见证挂起过 2. 见证完成未挂起 3. 见证完成挂起过 4. 复核完成未挂起 5. 核完成挂起过 6. 其他
					decode(audit_finish_datetime, null, a.pause_flag, decode(a.review_finish_datetime, null, decode(a.pause_flag, '0', '2', '1', '3', '6'), decode(a.pause_flag, '0', '4', '1', '5', '6'))) as flag,
					a.request_status,
					a.audit_create_datetime,
					a.audit_deal_datetime,
					a.audit_finish_datetime,
					a.review_create_datetime,
					a.review_deal_datetime,
					a.review_finish_datetime,
					a.first_pause_datetime,
					a.first_deal_datetime,
					a.first_deal_operator_no,
					a.end_deal_datetime
				from crh_ac.businprocessrequestaudittrail a
				where a.user_up_branch_no != '3'
				and   a.user_up_branch_no != ' '
				<if condition="@start_time != null && @start_time.length() > 0">
					and a.request_datetime >= to_date('@start_time' , 'yyyy-mm-dd hh24:mi:ss')
				</if>
				<if condition="@end_time != null && @end_time.length() > 0">
					and a.request_datetime <= to_date('@end_time' , 'yyyy-mm-dd hh24:mi:ss')
				</if>
				<if condition="@time_property != null && @time_property.length() > 0">
					and a.time_property = '@time_property'
				</if>
				<if condition="@branch_no != null && @branch_no.length() > 0">
					and instr(','||'@branch_no'||',',','||a.user_branch_no||',')>0
				</if>
				<if condition="@en_branch_nos != null && @en_branch_nos.length() > 0">
					AND instr(',' || '@en_branch_nos' || ',', ',' || a.user_branch_no || ',') > 0
				</if>
				<if condition="@up_branch_no != null && @up_branch_no.length() > 0">
					and instr(','||'@up_branch_no'||',',','||a.user_up_branch_no||',')>0
				</if>
				<if condition="@busin_type != null && @busin_type.length() > 0">
					and instr(','||'@busin_type'||',',','||busin_type||',')>0
				</if>
				<if condition="@video_type != null && @video_type.length() > 0">
					and a.video_type = '@video_type'
				</if>
				<if condition="@marketing_team != null && @marketing_team.length() > 0">
					and marketing_team = '@marketing_team'
				</if>
			),l as (
				select
					e.up_branch_no,
					e.up_branch_name,
					count(1) as request_total_sum_repeat,
					count(distinct e.request_no) as request_total_sum,
					count(distinct decode(e.audit_finish_datetime, null, null, e.request_no)) as request_audit_finish_sum,
					count(distinct decode(e.review_finish_datetime, null, null, decode(e.request_status, 'review-4', null, e.request_no))) as request_review_success_sum,
					sum(decode(e.audit_finish_datetime, null, 0, 1)) as audit_total_sum,
					sum(decode(e.audit_finish_datetime, null, 0, decode(e.request_status, 'audit-4', 0, 'audit-8', 0, 1))) as audit_success_sum,
					sum(decode(e.audit_finish_datetime, null, 0, decode(e.request_status, 'audit-4', 1, 'audit-8', 1, 0))) as audit_fail_sum,
					sum(decode(e.review_finish_datetime, null, 0, 1)) as review_total_sum,
					sum(decode(e.review_finish_datetime, null, 0, decode(e.request_status, 'review-4', 0, 1))) as review_success_sum,
					sum(decode(e.review_finish_datetime, null, 0, decode(e.request_status, 'review-4', 1, 0))) as review_fail_sum,
					round(sum(decode(e.audit_deal_datetime, null, 0, e.audit_deal_datetime - e.request_datetime)) * 24 * 60 *60) as audit_wait_time,
					round(sum(decode(e.audit_finish_datetime, null, 0, e.audit_finish_datetime - e.audit_deal_datetime)) * 24 * 60 *60) as audit_deal_time,
					round(sum(decode(e.review_deal_datetime, null, 0, e.review_deal_datetime - e.audit_finish_datetime)) * 24 * 60 * 60) as review_wait_time,
					round(sum(decode(e.review_finish_datetime, null, 0, e.review_finish_datetime - e.review_deal_datetime)) * 24 * 60 * 60) as review_deal_time,
					sum(decode(e.flag, '0', 1, 0)) as no_handup_sum,
					sum(decode(e.flag, '1', 1, 0)) as handup_sum,
					sum(decode(e.flag, '2', 1, '4', 1, 0)) as finish_no_handup_sum,
					sum(decode(e.flag, '3', 1, '5', 1, 0)) as finish_handup_sum,
					round(sum(decode(e.flag, '2', e.first_deal_datetime - e.request_datetime, '4', e.first_deal_datetime - e.request_datetime, 0)) * 24 * 60 *60) as no_handup_deal_time,
					round(sum(decode(e.flag, '3', e.first_deal_datetime - e.request_datetime, '5', e.first_deal_datetime - e.request_datetime, 0)) * 24 * 60 *60) as handup_wait_time,
					round(sum(decode(e.flag, '3', decode(sign(e.audit_deal_datetime - e.first_pause_datetime), -1, 0, e.audit_deal_datetime - e.first_pause_datetime), '5', decode(sign(e.audit_deal_datetime - e.first_pause_datetime), -1, 0, e.audit_deal_datetime - e.first_pause_datetime), 0)) * 24 * 60 *60) as handup_deal_time
				from e
				group by e.up_branch_no, e.up_branch_name
				order by e.up_branch_no
			),m as(
				select
					'0' as up_branch_no,
					'合计' as up_branch_name,
					sum(l.request_total_sum) as request_total_sum,
					sum(l.request_total_sum_repeat) as request_total_sum_repeat,
					sum(l.request_audit_finish_sum) as request_audit_finish_sum,
                    sum(l.request_review_success_sum) as request_review_success_sum,
					sum(l.audit_success_sum) as audit_success_sum,
					sum(l.audit_fail_sum) as audit_fail_sum,
					sum(l.audit_total_sum) as audit_total_sum,
					sum(l.review_success_sum) as review_success_sum,
					sum(l.review_fail_sum) as review_fail_sum,
					sum(l.review_total_sum) as review_total_sum,
					sum(l.audit_wait_time) as audit_wait_time,
					sum(l.audit_deal_time) as audit_deal_time,
					sum(l.review_wait_time) as review_wait_time,
					sum(l.review_deal_time) as review_deal_time,
					sum(l.handup_sum) as handup_sum,
					sum(l.no_handup_sum) as no_handup_sum,
					sum(l.finish_handup_sum) as finish_handup_sum,
					sum(l.finish_no_handup_sum) as finish_no_handup_sum,
					sum(l.handup_wait_time) as handup_wait_time,
					sum(l.handup_deal_time) as handup_deal_time,
					sum(l.no_handup_deal_time) as no_handup_deal_time
				from
					l
				where 1=1
			)
			select
				m.up_branch_no,
				m.up_branch_name,
				m.request_total_sum,
				m.request_total_sum_repeat,
				m.audit_total_sum,
				m.audit_success_sum,
				m.audit_fail_sum,
				m.review_success_sum,
				m.review_fail_sum,
				m.no_handup_sum,
				m.handup_sum,
				decode(m.audit_total_sum , 0 , 0 , round(m.audit_wait_time/m.audit_total_sum)) as avg_audit_wait_time,
				decode(m.audit_total_sum , 0 , 0 , round(m.audit_deal_time/m.audit_total_sum)) as avg_audit_deal_time,
				decode(m.review_total_sum , 0 , 0 , round(m.review_wait_time/m.review_total_sum)) as avg_review_wait_time,
				decode(m.review_total_sum , 0 , 0 , round(m.review_deal_time/m.review_total_sum)) as avg_review_deal_time,
				decode(m.finish_handup_sum , 0 , 0 , round(m.handup_wait_time/m.finish_handup_sum)) as avg_handup_wait_time,
				decode(m.finish_handup_sum , 0 , 0 , round(m.handup_deal_time/m.finish_handup_sum)) as avg_handup_deal_time,
				decode(m.finish_no_handup_sum , 0 , 0 , round(m.no_handup_deal_time/m.finish_no_handup_sum)) as avg_no_handup_time,
				to_char(nvl(decode(m.audit_total_sum , 0 , 0 , 100 * round(m.finish_handup_sum/m.audit_total_sum, 4)), 0),'fm9999990.00') || '%' as handup_rate,
				to_char(nvl(decode(m.audit_success_sum + m.audit_fail_sum , 0 , 0 , 100 * round(m.review_success_sum/(m.audit_success_sum + m.audit_fail_sum), 4)), 0),'fm9999990.00') || '%' as review_success_rate,
				to_char(nvl(decode(m.request_audit_finish_sum , 0 , 0 , 100 * round(m.request_review_success_sum/m.request_audit_finish_sum, 4)), 0),'fm9999990.00') || '%' as audit_success_rate,
				rownum rn
			from
				m
			union all
			select
				*
			from
			(
				select
					l.up_branch_no,
					l.up_branch_name,
					l.request_total_sum,
					l.request_total_sum_repeat,
					l.audit_total_sum,
					l.audit_success_sum,
					l.audit_fail_sum,
					l.review_success_sum,
					l.review_fail_sum,
					l.no_handup_sum,
					l.handup_sum,
					decode(l.audit_total_sum , 0 , 0 , round(l.audit_wait_time/l.audit_total_sum)) as avg_audit_wait_time,
					decode(l.audit_total_sum , 0 , 0 , round(l.audit_deal_time/l.audit_total_sum)) as avg_audit_deal_time,
					decode(l.review_total_sum , 0 , 0 , round(l.review_wait_time/l.review_total_sum)) as avg_review_wait_time,
					decode(l.review_total_sum , 0 , 0 , round(l.review_deal_time/l.review_total_sum)) as avg_review_deal_time,
					decode(l.finish_handup_sum , 0 , 0 , round(l.handup_wait_time/l.finish_handup_sum)) as avg_handup_wait_time,
					decode(l.finish_handup_sum , 0 , 0 , round(l.handup_deal_time/l.finish_handup_sum)) as avg_handup_deal_time,
					decode(l.finish_no_handup_sum , 0 , 0 , round(l.no_handup_deal_time/l.finish_no_handup_sum)) as avg_no_handup_time,
					to_char(nvl(decode(l.audit_total_sum , 0 , 0 , 100 * round(l.finish_handup_sum/l.audit_total_sum, 4)), 0),'fm9999990.00') || '%' as handup_rate,
					to_char(nvl(decode(l.audit_success_sum + l.audit_fail_sum , 0 , 0 , 100 * round(l.review_success_sum/(l.audit_success_sum + l.audit_fail_sum), 4)), 0),'fm9999990.00') || '%' as review_success_rate,
					to_char(nvl(decode(l.request_audit_finish_sum , 0 , 0 , 100 * round(l.request_review_success_sum/l.request_audit_finish_sum, 4)), 0),'fm9999990.00')  || '%' as audit_success_rate,
					rownum rn
				from
					l
			)
			<if condition="@page_num != null && @page_size != null">
				WHERE rn BETWEEN ( @page_num-1 ) * @page_size + 1 AND @page_num * @page_size
			</if>
		]]></Content>
    </Func>

    <Func ID="MONITOR2012" MODULE_NAME="见证申请流水表总数查询" TYPE="QUERY" DESC="见证申请流水表总数查询">
        <InParam>
            <Param Code="staff_no" Name="员工号"/>
            <Param Code="request_status" Name="任务状态"/>
            <Param Code="request_start_time" Name="申请起始日期"/>
            <Param Code="request_end_time" Name="申请结束日期"/>
            <Param Code="audit_start_time" Name="见证起始日期"/>
            <Param Code="audit_end_time" Name="见证结束日期"/>
            <Param Code="review_start_time" Name="复核起始日期"/>
            <Param Code="review_end_time" Name="复核结束日期"/>
            <Param Code="secondary_review_start_time" Name="二次复核起始日期"/>
            <Param Code="secondary_review_end_time" Name="二次复核结束日期"/>
            <Param Code="broker_code" Name="推荐人工号"/>
            <Param Code="broker_name" Name="推荐人"/>
            <Param Code="busin_type" Name="业务类型"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="activity_no" Name="活动码"/>
            <Param Code="video_type" Name="视频见证方式"/>
            <Param Code="id_no" Name="证件号码"/>
            <Param Code="user_name" Name="客户姓名"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="audit_operator_no" Name="见证人工号"/>
            <Param Code="audit_operator_name" Name="见证人姓名"/>
            <Param Code="review_operator_no" Name="复核人工号"/>
            <Param Code="review_operator_name" Name="复核人姓名"/>
            <Param Code="secondary_review_operator_no" Name="二次复核人工号"/>
            <Param Code="secondary_review_operator_name" Name="二次复核人姓名"/>
            <Param Code="marketing_team" Name="营销团队"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			select
				count(1) as total_num
			from
				crh_ac.businprocessrequestaudittrail
			where 1=1
			<if condition="@request_start_time != null && @request_start_time.length() > 0">
				and request_datetime >= to_date('@request_start_time' , 'yyyy-mm-dd hh24:mi:ss')
			</if>
			<if condition="@request_end_time != null && @request_end_time.length() > 0">
				and request_datetime <= to_date('@request_end_time' , 'yyyy-mm-dd hh24:mi:ss')
			</if>
			<if condition="@branch_no != null && @branch_no.length() > 0">
				and instr(','||'@branch_no'||',',','||user_branch_no||',')>0
			</if>
			<if condition="@staff_no != null && @staff_no.length() > 0">
				and instr(','||(select en_branch_nos from crh_user.operatorinfo where staff_no = '@staff_no')||',',','||user_branch_no||',')>0
			</if>
			<if condition="@channel_code != null && @channel_code.length() > 0">
				and channel_code = '@channel_code'
			</if>
			<if condition="@activity_no != null && @activity_no.length() > 0">
				and activity_no = '@activity_no'
			</if>
			<if condition="@id_no != null && @id_no.length() > 0">
				and id_no = '@id_no'
			</if>
			<if condition="@user_name != null && @user_name.length() > 0">
				and user_name = '@user_name'
			</if>
			<if condition="@audit_start_time != null && @audit_start_time.length() > 0">
				and audit_finish_datetime >= to_date('@audit_start_time' , 'yyyy-mm-dd hh24:mi:ss')
			</if>
			<if condition="@audit_end_time != null && @audit_end_time.length() > 0">
				and audit_finish_datetime <= to_date('@audit_end_time' , 'yyyy-mm-dd hh24:mi:ss')
			</if>
			<if condition="@review_start_time != null && @review_start_time.length() > 0">
				and review_finish_datetime >= to_date('@review_start_time' , 'yyyy-mm-dd hh24:mi:ss')
			</if>
			<if condition="@review_end_time != null && @review_end_time.length() > 0">
				and review_finish_datetime <= to_date('@review_end_time' , 'yyyy-mm-dd hh24:mi:ss')
			</if>
			<if condition="@secondary_review_start_time != null && @secondary_review_start_time.length() > 0">
				and sec_rv_finish_datetime >= to_date('@secondary_review_start_time' , 'yyyy-mm-dd hh24:mi:ss')
			</if>
			<if condition="@secondary_review_end_time != null && @secondary_review_end_time.length() > 0">
				and sec_rv_finish_datetime <= to_date('@secondary_review_end_time' , 'yyyy-mm-dd hh24:mi:ss')
			</if>
			<if condition="@audit_operator_no != null && @audit_operator_no.length() > 0">
				and audit_operator_no = '@audit_operator_no'
			</if>
			<if condition="@audit_operator_name != null && @audit_operator_name.length() > 0">
				and audit_operator_name = '@audit_operator_name'
			</if>
			<if condition="@review_operator_no != null && @review_operator_no.length() > 0">
				and review_operator_no = '@review_operator_no'
			</if>
			<if condition="@review_operator_name != null && @review_operator_name.length() > 0">
				and review_operator_name = '@review_operator_name'
			</if>
			<if condition="@secondary_review_operator_no != null && @secondary_review_operator_no.length() > 0">
				and sec_rv_operator_no = '@secondary_review_operator_no'
			</if>
			<if condition="@secondary_review_operator_name != null && @secondary_review_operator_name.length() > 0">
				and sec_rv_operator_name = '@secondary_review_operator_name'
			</if>
			<if condition="@up_branch_no != null && @up_branch_no.length() > 0">
				and instr(','||'@up_branch_no'||',',','||user_up_branch_no||',')>0
			</if>
			<if condition="@request_status != null && @request_status.length() > 0">
				and instr(','||'@request_status'||',',','||request_status||',')>0
			</if>
			<if condition="@busin_type != null && @busin_type.length() > 0">
				and instr(','||'@busin_type'||',',','||busin_type||',')>0
			</if>
			<if condition="@video_type != null && @video_type.length() > 0">
				and video_type = '@video_type'
			</if>
			<if condition="@marketing_team != null && @marketing_team.length() > 0">
				and marketing_team = '@marketing_team'
			</if>
		]]></Content>
    </Func>

    <Func ID="MONITOR2013" MODULE_NAME="见证申请流水表分页查询" TYPE="QUERY" DESC="见证申请流水表分页查询">
        <InParam>
            <Param Code="staff_no" Name="员工号"/>
            <Param Code="request_status" Name="任务状态"/>
            <Param Code="request_start_time" Name="申请起始日期"/>
            <Param Code="request_end_time" Name="申请结束日期"/>
            <Param Code="audit_start_time" Name="见证起始日期"/>
            <Param Code="audit_end_time" Name="见证结束日期"/>
            <Param Code="review_start_time" Name="复核起始日期"/>
            <Param Code="review_end_time" Name="复核结束日期"/>
            <Param Code="secondary_review_start_time" Name="二次复核起始日期"/>
            <Param Code="secondary_review_end_time" Name="二次复核结束日期"/>
            <Param Code="broker_code" Name="推荐人工号"/>
            <Param Code="broker_name" Name="推荐人"/>
            <Param Code="busin_type" Name="业务类型"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="activity_no" Name="活动码"/>
            <Param Code="video_type" Name="视频见证方式"/>
            <Param Code="id_no" Name="证件号码"/>
            <Param Code="user_name" Name="客户姓名"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="audit_operator_no" Name="见证人工号"/>
            <Param Code="audit_operator_name" Name="见证人姓名"/>
            <Param Code="review_operator_no" Name="复核人工号"/>
            <Param Code="review_operator_name" Name="复核人姓名"/>
            <Param Code="secondary_review_operator_no" Name="二次复核人工号"/>
            <Param Code="secondary_review_operator_name" Name="二次复核人姓名"/>
            <Param Code="marketing_team" Name="营销团队"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			select
				*
			from
			(
				select
					user_name,
					id_no,
					user_up_branch_name as up_branch_name,
					user_branch_name as branch_name,
					request_status,
					first_deal_datetime,
					request_datetime,
					audit_create_datetime,
					audit_deal_datetime,
					audit_finish_datetime,
					audit_wait_time,
					audit_time,
					audit_operator_no,
					audit_operator_name,
					review_operator_no,
					review_operator_name,
					review_create_datetime,
					review_deal_datetime,
					review_finish_datetime,
					review_wait_time,
					review_time,
					secondary_review_operator_no,
					secondary_review_operator_name,
					secondary_review_create_datetime,
					secondary_review_deal_datetime,
					secondary_review_finish_datetime,
					secondary_review_wait_time,
					secondary_review_time,
					channel_code,
					activity_no,
					busin_type,
					video_type,
					marketing_team,
					rownum rn
				from
				(
					select
						user_name,
						id_no,
						user_up_branch_name,
						user_branch_name,
						request_status,
						request_datetime,
						first_deal_datetime,
						audit_create_datetime,
						audit_deal_datetime,
						audit_finish_datetime,
						decode(audit_deal_datetime, null, 0, audit_deal_datetime - request_datetime) * 24 * 60 *60 as audit_wait_time,
						decode(audit_finish_datetime, null, 0, audit_finish_datetime - audit_deal_datetime) * 24 * 60 *60 as audit_time,
						audit_operator_no,
						audit_operator_name,
						review_operator_no,
						review_operator_name,
						review_create_datetime,
						review_deal_datetime,
						review_finish_datetime,
						decode(review_deal_datetime, null, 0, decode(review_create_datetime, null, 0, review_deal_datetime - audit_finish_datetime)) * 24 * 60 *60 as review_wait_time,
						decode(review_finish_datetime, null, 0, decode(review_create_datetime, null, 0, review_finish_datetime - review_deal_datetime)) * 24 * 60 *60 as review_time,
						sec_rv_operator_no as secondary_review_operator_no,
						sec_rv_operator_name as secondary_review_operator_name,
						sec_rv_create_datetime as secondary_review_create_datetime,
						sec_rv_deal_datetime as secondary_review_deal_datetime,
						sec_rv_finish_datetime as secondary_review_finish_datetime,
						decode(sec_rv_deal_datetime, null, 0, decode(sec_rv_create_datetime, null, 0, sec_rv_deal_datetime - sec_rv_create_datetime)) * 24 * 60 *60 as secondary_review_wait_time,
						decode(sec_rv_finish_datetime, null, 0, decode(sec_rv_create_datetime, null, 0, sec_rv_finish_datetime - sec_rv_deal_datetime)) * 24 * 60 *60 as secondary_review_time,
						channel_code,
						activity_no,
						busin_type,
						video_type,
						marketing_team
					from
						crh_ac.businprocessrequestaudittrail
					where 1=1
					<if condition="@request_start_time != null && @request_start_time.length() > 0">
						and request_datetime >= to_date('@request_start_time' , 'yyyy-mm-dd hh24:mi:ss')
					</if>
					<if condition="@request_end_time != null && @request_end_time.length() > 0">
						and request_datetime <= to_date('@request_end_time' , 'yyyy-mm-dd hh24:mi:ss')
					</if>
					<if condition="@branch_no != null && @branch_no.length() > 0">
						and instr(','||'@branch_no'||',',','||user_branch_no||',')>0
					</if>
					<if condition="@staff_no != null && @staff_no.length() > 0">
						and instr(','||(select en_branch_nos from crh_user.operatorinfo where staff_no = '@staff_no')||',',','||user_branch_no||',')>0
					</if>
					<if condition="@channel_code != null && @channel_code.length() > 0">
						and channel_code = '@channel_code'
					</if>
					<if condition="@activity_no != null && @activity_no.length() > 0">
						and activity_no = '@activity_no'
					</if>
					<if condition="@id_no != null && @id_no.length() > 0">
						and id_no = '@id_no'
					</if>
					<if condition="@user_name != null && @user_name.length() > 0">
						and user_name = '@user_name'
					</if>
					<if condition="@audit_start_time != null && @audit_start_time.length() > 0">
						and audit_finish_datetime >= to_date('@audit_start_time' , 'yyyy-mm-dd hh24:mi:ss')
					</if>
					<if condition="@audit_end_time != null && @audit_end_time.length() > 0">
						and audit_finish_datetime <= to_date('@audit_end_time' , 'yyyy-mm-dd hh24:mi:ss')
					</if>
					<if condition="@review_start_time != null && @review_start_time.length() > 0">
						and review_finish_datetime >= to_date('@review_start_time' , 'yyyy-mm-dd hh24:mi:ss')
					</if>
					<if condition="@review_end_time != null && @review_end_time.length() > 0">
						and review_finish_datetime <= to_date('@review_end_time' , 'yyyy-mm-dd hh24:mi:ss')
					</if>
					<if condition="@secondary_review_start_time != null && @secondary_review_start_time.length() > 0">
						and sec_rv_finish_datetime >= to_date('@secondary_review_start_time' , 'yyyy-mm-dd hh24:mi:ss')
					</if>
					<if condition="@secondary_review_end_time != null && @secondary_review_end_time.length() > 0">
						and sec_rv_finish_datetime <= to_date('@secondary_review_end_time' , 'yyyy-mm-dd hh24:mi:ss')
					</if>
					<if condition="@audit_operator_no != null && @audit_operator_no.length() > 0">
						and audit_operator_no = '@audit_operator_no'
					</if>
					<if condition="@audit_operator_name != null && @audit_operator_name.length() > 0">
						and audit_operator_name = '@audit_operator_name'
					</if>
					<if condition="@review_operator_no != null && @review_operator_no.length() > 0">
						and review_operator_no = '@review_operator_no'
					</if>
					<if condition="@review_operator_name != null && @review_operator_name.length() > 0">
						and review_operator_name = '@review_operator_name'
					</if>
					<if condition="@secondary_review_operator_no != null && @secondary_review_operator_no.length() > 0">
						and sec_rv_operator_no = '@secondary_review_operator_no'
					</if>
					<if condition="@secondary_review_operator_name != null && @secondary_review_operator_name.length() > 0">
						and sec_rv_operator_name = '@secondary_review_operator_name'
					</if>
					<if condition="@up_branch_no != null && @up_branch_no.length() > 0">
						and instr(','||'@up_branch_no'||',',','||user_up_branch_no||',')>0
					</if>
					<if condition="@request_status != null && @request_status.length() > 0">
						and instr(','||'@request_status'||',',','||request_status||',')>0
					</if>
					<if condition="@busin_type != null && @busin_type.length() > 0">
						and instr(','||'@busin_type'||',',','||busin_type||',')>0
					</if>
					<if condition="@video_type != null && @video_type.length() > 0">
						and video_type = '@video_type'
					</if>
					<if condition="@marketing_team != null && @marketing_team.length() > 0">
						and marketing_team = '@marketing_team'
					</if>
					order by request_datetime desc
				)
			)
			<if condition="@page_num != null && @page_size != null">
				WHERE rn BETWEEN ( @page_num-1 ) * @page_size + 1 AND @page_num * @page_size
			</if>
		]]></Content>
    </Func>

    <Func ID="MONITOR2014" MODULE_NAME="任务量查询" TYPE="QUERY" DESC="任务量查询">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
        </InParam>
        <Content><![CDATA[
			select
				t3.total_num,
				t3.exclude_pause_total_num,
				t3.finish_num,
				t3.exclude_pause_finish_num,
				t3.audit_total_num,
				t3.audit_finish_num,
				t3.audit_unfinish_num,
				t3.review_total_num,
				t3.review_finish_num,
				t3.review_total_num - t3.review_finish_num as review_unfinish_num,
				t3.one_way_video_num,
				t3.not_one_way_video_num,
				t3.two_way_video_num,
				t3.ydzy_video_num,
				t3.not_one_way_video_num - t3.two_way_video_num - t3.ydzy_video_num as other_video_num,
				100 * round(decode(t3.total_num, 0 , 0, t3.finish_num/t3.total_num), 4) as finish_radio,
				100 * round(decode(t3.exclude_pause_total_num, 0 , 0, t3.finish_num/t3.exclude_pause_total_num), 4) as exclude_pause_finish_radio,
				t3.zb_finish_num,
				100 * round(decode(t3.finish_num, 0 , 0, t3.zb_finish_num/t3.finish_num), 4) as zb_finish_radio,
				t3.tg_finish_num,
				100 * round(decode(t3.finish_num, 0 , 0, t3.tg_finish_num/t3.finish_num), 4) as tg_finish_radio,
				t3.zb_audit_finish_num,
				100 * round(decode(t3.audit_finish_num, 0 , 0, t3.zb_audit_finish_num/t3.audit_finish_num), 4) as zb_audit_finish_radio,
				t3.tg_audit_finish_num,
				100 * round(decode(t3.audit_finish_num, 0 , 0, t3.tg_audit_finish_num/t3.audit_finish_num), 4) as tg_audit_finish_radio,
				t3.zb_review_finish_num,
				100 * round(decode(t3.review_finish_num, 0 , 0, t3.zb_review_finish_num/t3.review_finish_num), 4) as zb_review_finish_radio,
				t3.tg_review_finish_num,
				100 * round(decode(t3.review_finish_num, 0 , 0, t3.tg_review_finish_num/t3.review_finish_num), 4) as tg_review_finish_radio
			from (
				select
					t2.total_num,
					t2.exclude_pause_total_num,
					t2.finish_num,
					t2.exclude_pause_finish_num,
					t2.audit_total_num,
					t2.zb_audit_finish_num + t2.tg_audit_finish_num as audit_finish_num,
					t2.audit_total_num - t2.zb_audit_finish_num - t2.tg_audit_finish_num as audit_unfinish_num,
					t2.one_way_video_num,
					t2.total_num - t2.one_way_video_num as not_one_way_video_num,
					t2.two_way_video_num,
					t2.ydzy_video_num,
					t2.zb_audit_finish_num + t2.zb_review_finish_num + t2.zb_secondary_review_finish_num as zb_finish_num,
					t2.tg_audit_finish_num + t2.tg_review_finish_num + t2.tg_secondary_review_finish_num as tg_finish_num,
					t2.zb_audit_finish_num,
					t2.tg_audit_finish_num,
					t2.review_total_num,
					t2.finish_num - t2.zb_audit_finish_num - t2.tg_audit_finish_num as review_finish_num,
					t2.zb_review_finish_num + t2.zb_secondary_review_finish_num as zb_review_finish_num,
					t2.tg_review_finish_num + t2.tg_secondary_review_finish_num as tg_review_finish_num
				from (
					select
						count(1) as total_num,
						sum(decode(t1.task_status, 'a', 0, 1)) as exclude_pause_total_num,
						sum(decode(t1.task_status, '3', 1, '4', 1, 0)) as finish_num,
						sum(decode(t1.pause_flag, '0', decode(t1.task_status, '3', 1, '4', 1, '8', 1, 0), 0)) as exclude_pause_finish_num,
						sum(decode(t1.busin_type, '100058', decode(t1.video_type, '1', 1, 0), 0)) as one_way_video_num,
						sum(decode(t1.busin_type, '100058', decode(t1.video_type, '2', 1, 0), 0)) as two_way_video_num,
						sum(decode(t1.busin_type, '100910', 1, '100920', 1, '100930', 1, 0)) as ydzy_video_num,
						sum(decode(t1.task_type, 'audit', 1, 0)) as audit_total_num,
						sum(decode(t1.task_type, 'review', 1, 'secondary_review', 1, 0)) as review_total_num,
						sum(decode(t1.operator_branch_no, '3', decode(t1.task_type, 'audit', decode(t1.task_status, '3', 1, '4', 1, '8', 1, 0) , 0), 0)) as zb_audit_finish_num,
						sum(decode(t1.operator_branch_no, '3', decode(t1.task_type, 'review', decode(t1.task_status, '3', 1, '4', 1, 0) , 0), 0)) as zb_review_finish_num,
						sum(decode(t1.operator_branch_no, '3', decode(t1.task_type, 'secondary_review', decode(t1.task_status, '3', 1, '4', 1, 0) , 0), 0)) as zb_secondary_review_finish_num,
						sum(decode(t1.operator_branch_no, '3', decode(t1.task_type, 'audit', decode(t1.task_status, '1', 1, '2', 1, 0) , 0), 0)) as zb_audit_unfinish_num,
						sum(decode(t1.operator_branch_no, '3', decode(t1.task_type, 'review', decode(t1.task_status, '1', 1, '2', 1, 0) , 0), 0)) as zb_review_unfinish_num,
						sum(decode(t1.operator_branch_no, '3', decode(t1.task_type, 'secondary_review', decode(t1.task_status, '1', 1, '2', 1, 0) , 0), 0)) as zb_secondary_review_unfinish_num,
						sum(decode(t1.operator_branch_no, '3', 0, decode(t1.task_type, 'audit', decode(t1.task_status, '3', 1, '4', 1, '8', 1, 0) , 0))) as tg_audit_finish_num,
						sum(decode(t1.operator_branch_no, '3', 0, decode(t1.task_type, 'review', decode(t1.task_status, '3', 1, '4', 1, 0) , 0))) as tg_review_finish_num,
						sum(decode(t1.operator_branch_no, '3', 0, decode(t1.task_type, 'secondary_review', decode(t1.task_status, '3', 1, '4', 1, 0) , 0))) as tg_secondary_review_finish_num,
						sum(decode(t1.operator_branch_no, '3', 0, decode(t1.task_type, 'audit', decode(t1.task_status, '1', 1, '2', 1, 0) , 0))) as tg_audit_unfinish_num,
						sum(decode(t1.operator_branch_no, '3', 0, decode(t1.task_type, 'review', decode(t1.task_status, '1', 1, '2', 1, 0) , 0))) as tg_review_unfinish_num,
						sum(decode(t1.operator_branch_no, '3', 0, decode(t1.task_type, 'secondary_review', decode(t1.task_status, '1', 1, '2', 1, 0) , 0))) as tg_secondary_review_unfinish_num
					from crh_ac.flowtaskrecorddetails t1
					where   t1.task_datetime >= trunc(sysdate)
					and     t1.task_datetime < trunc(sysdate) +1
					and   invalid_flag = '0'
					group by trunc(t1.task_datetime)
				) t2
			)t3
		]]></Content>
    </Func>

    <Func ID="MONITOR2015" MODULE_NAME="分公司任务量top8" TYPE="QUERY" DESC="分公司任务量top8">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
        </InParam>
        <Content><![CDATA[
			with t2 as (
				select
					t1.up_branch_no,
					t1.up_branch_name,
					count(1) as up_branch_no_num,
					sum(decode(t1.task_status, '3', 1, '4', 1, '8', 1, 0)) as finish_num,
					sum(decode(t1.task_status, '3', 0, '4', 0, '8', 1, 1)) as un_finish_num,
					row_number() over(order by count(1) desc) as rank_num
				from crh_ac.flowtaskrecorddetails t1
					where   t1.task_datetime >= trunc(sysdate)
					and     t1.task_datetime < trunc(sysdate) +1
					and invalid_flag = '0'
					and t1.up_branch_no != '3'
				group by t1.up_branch_no, t1.up_branch_name
			), t3 as (
				select
					sum(t2.up_branch_no_num) as total_num
				from t2
			)
			select
				t2.up_branch_no,
				t2.up_branch_name,
				t2.up_branch_no_num,
				t2.finish_num,
				t2.un_finish_num,
				t3.total_num,
				100 * round(decode(t3.total_num, 0 , 0, t2.up_branch_no_num/t3.total_num), 4) as up_branch_no_radio
			from t2, t3
			where t2.rank_num <= 8
			order by t2.up_branch_no_num desc
		]]></Content>
    </Func>

    <Func ID="MONITOR2016" MODULE_NAME="驳回原因top5" TYPE="QUERY" DESC="驳回原因top5">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
        </InParam>
        <Content><![CDATA[
			with t2 as (
				select
					t1.reason_desc,
					count(1) as reason_desc_num,
					sum(decode(t1.task_type, 'audit', 1, 0)) as audit_num,
					sum(decode(t1.task_type, 'audit', 0, 1)) as review_num
				from (
					select
						a.task_type,
						a.create_datetime,
						b.reason_desc
					from crh_ac.businflowtask a, crh_ac.taskreasonrecord b
					where   a.create_datetime >= trunc(sysdate)
					and     a.create_datetime < trunc(sysdate) +1
					and 	a.task_status = '4'
					and 	a.task_id = b.task_id
				) t1
				group by t1.reason_desc
				order by count(1) desc
			), t3 as (
				select
					sum(t2.reason_desc_num) as total_num
				from t2
			), t4 as(
				select
					t2.reason_desc,
					t2.reason_desc_num,
					t2.audit_num,
					t2.review_num
				from t2
				where rownum <= 5
			)
			select
				t4.reason_desc,
				t4.reason_desc_num,
				t4.audit_num,
				t4.review_num,
				t3.total_num,
				100 * round(decode(t3.total_num, 0 , 0, t4.reason_desc_num/t3.total_num), 4) as reason_desc_radio
			from t4, t3
		]]></Content>
    </Func>

    <Func ID="MONITOR2017" MODULE_NAME="业务类型概况（作废，MONITOR2014中返回）" TYPE="QUERY" DESC="业务类型概况">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
        </InParam>
        <Content><![CDATA[
			select
				sum(num) as total_num,
				sum(decode(t1.busin_type, '100058', decode(t1.video_type, '1', t1.num, 0), 0)) as one_way_video_num,
				sum(decode(t1.busin_type, '100058', decode(t1.video_type, '2', t1.num, 0), 0)) as two_way_video_num,
				sum(decode(t1.busin_type, 'aaa', t1.num, 0)) as aaa_num,
				sum(decode(t1.busin_type, 'bbb', t1.num, 0)) as bbb_num,
				sum(decode(t1.busin_type, 'ccc', t1.num, 0)) as ccc_num
			from (
				select
					a.video_type,
					a.busin_type,
					count(1) as num
				from crh_ac.userqueryextinfo a
				where   a.request_datetime >= trunc(sysdate)
				and     a.request_datetime < trunc(sysdate) +1
				and   a.is_snapshot = '0'
				group by a.video_type, a.busin_type
			)t1
		]]></Content>
    </Func>

    <Func ID="MONITOR2018" MODULE_NAME="审核结果" TYPE="QUERY" DESC="审核结果">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
        </InParam>
        <Content><![CDATA[
			select
				to_char(nvl(decode(m.request_audit_finish_sum , 0 , 0 , 100 * round(m.request_review_success_sum/m.request_audit_finish_sum, 4)), 0),'fm9999990.00') || '%' as audit_success_rate
			from (
				select
					count(distinct decode(e.audit_finish_datetime, null, null, e.request_no)) as request_audit_finish_sum,
					count(distinct decode(e.review_finish_datetime, null, null, decode(e.request_status, 'review-4', null, e.request_no))) as request_review_success_sum
				from crh_ac.businprocessrequestaudittrail e
				where   e.request_datetime >= trunc(sysdate)
				and   e.request_datetime < trunc(sysdate) +1
			) m
		]]></Content>
    </Func>

    <Func ID="MONITOR2019" MODULE_NAME="时长统计" TYPE="QUERY" DESC="时长统计">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
        </InParam>
        <Content><![CDATA[
			select
				decode(t3.zb_audit_num , 0 , 0 , round(t3.zb_audit_deal_time/t3.zb_audit_num)) as zb_audit_deal_time,
				decode(t3.zb_review_num , 0 , 0 , round(t3.zb_review_deal_time/t3.zb_review_num)) as zb_review_deal_time,
				decode(t3.tg_audit_num , 0 , 0 , round(t3.tg_audit_deal_time/t3.tg_audit_num)) as tg_audit_deal_time,
				decode(t3.tg_review_num , 0 , 0 , round(t3.tg_review_deal_time/t3.tg_review_num)) as tg_review_deal_time
			from (
				select
					nvl(sum(decode(t2.branch_no, '3', audit_sum, 0)), 0) as zb_audit_num,
					nvl(sum(decode(t2.branch_no, '3', review_sum, 0)), 0) as zb_review_num,
					nvl(sum(decode(t2.branch_no, '3', audit_deal_time, 0)), 0) as zb_audit_deal_time,
					nvl(sum(decode(t2.branch_no, '3', review_deal_time, 0)), 0) as zb_review_deal_time,
					nvl(sum(decode(t2.branch_no, '3', 0, audit_sum)), 0) as tg_audit_num,
					nvl(sum(decode(t2.branch_no, '3', 0, review_sum)), 0) as tg_review_num,
					nvl(sum(decode(t2.branch_no, '3', 0, audit_deal_time)), 0) as tg_audit_deal_time,
					nvl(sum(decode(t2.branch_no, '3', 0, review_deal_time)), 0) as tg_review_deal_time
				from (
					select
						e.operator_branch_no as branch_no,
						sum(decode(e.task_type, 'audit', 1, 0)) as audit_sum,
						sum(decode(e.task_type, 'review', 1, 0)) as review_sum,
						sum(decode(e.task_type, 'secondary_review', 1, 0)) as secondary_review_sum,
						round(sum(decode(e.task_type, 'audit', e.finish_datetime - e.deal_datetime, 0)) * 24 * 60 *60) as audit_deal_time,
						round(sum(decode(e.task_type, 'review', e.finish_datetime - e.deal_datetime, 0)) * 24 * 60 * 60) as review_deal_time,
						round(sum(decode(e.task_type, 'secondary_review', e.finish_datetime - e.deal_datetime, 0)) * 24 * 60 * 60) as secondary_review_deal_time
					from crh_ac.flowtaskrecorddetails e
					where e.task_status in ('3', '4', '8')
					and   e.task_datetime >= trunc(sysdate)
					and   e.task_datetime < trunc(sysdate) +1
					and   invalid_flag = '0'
					group by e.operator_branch_no
				) t2
			)t3
		]]></Content>
    </Func>

    <Func ID="MONITOR2020" MODULE_NAME="任务量小时趋势" TYPE="QUERY" DESC="任务量小时趋势">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
        </InParam>
        <Content><![CDATA[
			select
				query_time,
				total_num,
				excute_num
			from (
				select
					to_char(create_datetime, 'hh24') || ':00' as query_time,
                    create_datetime,
					total_num,
					excute_num
				from (
					select
						create_datetime,
						total_num,
						excute_num
					from crh_monitor.hourworkload
					order by create_datetime desc
				) where rownum < 8
				union all
				select
					to_char(sysdate, 'hh24') || ':' || to_char(sysdate, 'mi') as query_time,
                    sysdate,
					count(1) total_num,
					nvl(sum(decode(a.task_status, '3', 1, '4', 1, '8', 1, 0)), 0) as excute_num
				from crh_ac.businflowtask a
				where a.create_datetime >= sysdate - 1/24
			)
			order by create_datetime
		]]></Content>
    </Func>

    <Func ID="MONITOR2021" MODULE_NAME="任务量日趋势" TYPE="QUERY" DESC="任务量日趋势">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
        </InParam>
        <Content><![CDATA[
			select
				to_char(create_datetime, 'mmdd') as  query_day,
				tg_total_num
			from (
				select
					create_datetime,
					tg_total_num
				from (
					select
						create_datetime,
						notg_total_num + tg_total_num as tg_total_num
					from crh_monitor.dailyworkload
					union all
					select
						trunc(sysdate) as query_day,
						count(1) as tg_total_num
					from  crh_ac.flowtaskrecorddetails
					where   create_datetime >= trunc(sysdate)
					and     create_datetime < trunc(sysdate) +1
					and   branch_no != '3'
					and   invalid_flag = '0'
				)
				order by create_datetime desc
			) where rownum <= 31
			order by create_datetime
		]]></Content>
    </Func>

    <Func ID="MONITOR2022" MODULE_NAME="驳回率趋势" TYPE="QUERY" DESC="驳回率趋势">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
        </InParam>
        <Content><![CDATA[
			select
				to_number(TO_CHAR(query_day, 'mm')) ||'.'||to_number(TO_CHAR(query_day, 'dd')) as query_day,
				tg_audit_reject_radio,
				tg_review_reject_radio,
				zb_audit_reject_radio,
				zb_review_reject_radio
			from (
                    select
                        query_day,
                        query_day as order_query_day,
                        100 * round(decode(tg_audit_total_num, 0 , 0, tg_audit_reject_num/tg_audit_total_num), 4) as tg_audit_reject_radio,
                        100 * round(decode(tg_review_total_num, 0 , 0, tg_review_reject_num/tg_review_total_num), 4) as tg_review_reject_radio,
                        100 * round(decode(zb_audit_total_num, 0 , 0, zb_audit_reject_num/zb_audit_total_num), 4) as zb_audit_reject_radio,
                        100 * round(decode(zb_review_total_num, 0 , 0, zb_review_reject_num/zb_review_total_num), 4) as zb_review_reject_radio
                    from (
                        select
                            query_day,
                            total_num,
                            zb_audit_total_num,
                            zb_review_total_num,
                            zb_audit_reject_num,
                            zb_review_reject_num,
                            tg_audit_total_num,
                            tg_review_total_num,
                            tg_audit_reject_num,
                            tg_review_reject_num
                        from crh_monitor.rejectstatistics
                        union all
                        select
                            trunc(sysdate) as query_day,
                            count(1) as total_num,
                            nvl(sum(decode(t1.operator_branch_no, '3', decode(t1.task_type, 'audit', 1, 0), 0)), 0) as zb_audit_total_num,
                            nvl(sum(decode(t1.operator_branch_no, '3', decode(t1.task_type, 'review', 1, 0), 0)), 0) as zb_review_total_num,
                            nvl(sum(decode(t1.operator_branch_no, '3', decode(t1.task_type ||'-'||t1.task_status, 'audit-4', 1, 'audit-8', 1, 0), 0)), 0) as zb_audit_reject_num,
                            nvl(sum(decode(t1.operator_branch_no, '3', decode(t1.task_type ||'-'||t1.task_status, 'review-4', 1, 0), 0)), 0) as zb_review_reject_num,
                            nvl(sum(decode(t1.operator_branch_no, '3', 0, decode(t1.task_type, 'audit', 1, 0))), 0) as tg_audit_total_num,
                            nvl(sum(decode(t1.operator_branch_no, '3', 0, decode(t1.task_type, 'review', 1, 0))), 0) as tg_review_total_num,
                            nvl(sum(decode(t1.operator_branch_no, '3', 0, decode(t1.task_type ||'-'||t1.task_status, 'audit-4', 1, 'audit-8', 1, 0))), 0) as tg_audit_reject_num,
                            nvl(sum(decode(t1.operator_branch_no, '3', 0, decode(t1.task_type ||'-'||t1.task_status, 'review-4', 1, 0))), 0) as tg_review_reject_num
                        from crh_ac.flowtaskrecorddetails t1
                        where t1.invalid_flag = '0'
                        and   t1.operator_no != ' '
                        and   trunc(t1.finish_datetime) = trunc(sysdate)
                    )
				order by query_day desc
			) where rownum <= 30
			order by order_query_day
		]]></Content>
    </Func>

    <Func ID="MONITOR2023" MODULE_NAME="大屏第三屏总数查询" TYPE="QUERY" DESC="大屏第三屏总数查询">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
        </InParam>
        <Content><![CDATA[
			select
				count(1) as total_num
			from crh_ac.businprocessrequestaudittrail
			where   request_datetime >= trunc(sysdate)
            and     request_datetime < trunc(sysdate) +1
		]]></Content>
    </Func>

    <Func ID="MONITOR2024" MODULE_NAME="大屏第三屏分页查询" TYPE="QUERY" DESC="大屏第三屏分页查询">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
		    with h as (
				select
					request_no,
					match_labels,
					task_source,
					substr(user_name,1,1) || decode(length(user_name), 2, '*', 3, '**', '***')  as client_name,
					request_status as task_status,
					audit_operator_no,
					audit_operator_name,
					audit_finish_datetime,
					review_operator_no,
					review_operator_name,
					review_finish_datetime,
					sec_rv_operator_no as double_operator_no,
					sec_rv_operator_name as double_operator_name,
					sec_rv_finish_datetime as double_finish_datetime,
					app_id,
					request_datetime,
					user_branch_no as branch_no,
					user_up_branch_name as up_branch_name,
					rownum as rn
				from crh_ac.businprocessrequestaudittrail
				where   request_datetime >= trunc(sysdate)
                and     request_datetime < trunc(sysdate) +1
				order by request_datetime asc
			)
			select
				*
			from h
			<if condition="@page_num != null && @page_size != null">
				WHERE rn BETWEEN ( @page_num-1 ) * @page_size + 1 AND @page_num * @page_size
			</if>
		]]></Content>
    </Func>

    <Func ID="MONITOR2025" MODULE_NAME="坐席被置忙top5" TYPE="QUERY" DESC="坐席被置忙top5">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
        </InParam>
        <Content><![CDATA[
			with t1 as (
				select
					operator_no,
					count(1)  as num
				from crh_ads.operatorautobusydetail
				where   create_datetime >= trunc(sysdate)
                and     create_datetime < trunc(sysdate) +1
				and     deal_flag = '1'
				group by  operator_no
				order by count(1) desc
			)
			select
				t2.user_name as operator_no,
				t1.num
			from t1, crh_user.operatorinfo t2
			where t1.operator_no = t2.staff_no
			order by t1.num desc
		]]></Content>
    </Func>

    <Func ID="MONITOR2026" MODULE_NAME="超时回收总次数top5" TYPE="QUERY" DESC="超时回收总次数top5">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
        </InParam>
        <Content><![CDATA[
			select
				operator_name as operator_no,
				count(1)  as num
			from crh_ads.dispatchtask
			where   create_datetime >= trunc(sysdate)
			and     create_datetime < trunc(sysdate) +1
			and     dispatch_status = '4'
			and     subsys_id = '24'
			and     operator_name != ' '
			group by  operator_name
			order by count(1) desc
		]]></Content>
    </Func>

    <Func ID="MONITOR2027" MODULE_NAME="队列流速" TYPE="QUERY" DESC="队列流速">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
        </InParam>
        <Content><![CDATA[
			select
				query_time,
				queue_level0_num,
				queue_level1_num,
				queue_level2_num,
				queue_level3_num,
				queue_level4_num
			from (
				select
					to_char(create_datetime, 'hh24') || ':00' as query_time,
					create_datetime,
					queue_level0_num,
					queue_level1_num,
					queue_level2_num,
					queue_level3_num,
					queue_level4_num
				from (
					select
						create_datetime,
						queue_level0_num,
						queue_level1_num,
						queue_level2_num,
						queue_level3_num,
						queue_level4_num
					from crh_monitor.queuelevelspeed
					order by create_datetime desc
				) where rownum < 9
				union all
				select
					to_char(sysdate, 'hh24') || ':' || to_char(sysdate, 'mi') as query_time,
					sysdate,
					nvl(sum(decode(queue_level, 1, 1, 0)), 0) as queue_level0_num,
					nvl(sum(decode(queue_level, 2, 1, 0)), 0) as queue_level1_num,
					nvl(sum(decode(queue_level, 3, 1, 0)), 0) as queue_level2_num,
					nvl(sum(decode(queue_level, 4, 1, 0)), 0) as queue_level3_num,
					nvl(sum(decode(queue_level, 5, 1, 0)), 0) as queue_level4_num
				from crh_ads.dispatchtask
				where subsys_id = '24'
				and   dispatch_datetime > (sysdate -  (select property_value from crh_monitor.syspropertyconfig where property_key = 'monitor.ac.statistics.unit.time') /1440)
			)
			order by create_datetime
		]]></Content>
    </Func>

    <Func ID="MONITOR2028" MODULE_NAME="本月小组人均工作量排名" TYPE="QUERY" DESC="本月小组人均工作量排名">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
        </InParam>
        <Content><![CDATA[
			select
				group_name,
				group_div_num
			from (
				select
					b.group_name,
					nvl(t1.group_div_num, 0) as group_div_num
				from crh_user.operatorgroup b, (
					select
						group_name,
						group_div_num
					from (
						select
							group_name,
							count(operator_no) as operator_num,
							sum(num) as group_num,
							round(sum(num)/count(operator_no), 2) as group_div_num
						from (
							select
								group_name,
								operator_no,
								count(1) as num
							from (
								select
									a.operator_no,
									a.task_id,
									b.group_name
								from crh_ads.dispatchtask a, crh_user.operatorgroup b
								where dispatch_status = '3'
								and   finish_datetime >= to_date(to_char(sysdate, 'yyyymm') || '01', 'yyyymmdd')
								and   instr(','||b.oper_nos||',',','||a.operator_no||',')>0
							)
							group by group_name, operator_no
						)
						group by group_name
					)
				) t1 where b.group_name = t1.group_name(+)
			)
			order by group_div_num desc
		]]></Content>
    </Func>

    <Func ID="MONITOR2029" MODULE_NAME="服务绿通客户平均响应时间及服务绿通客户总人数" TYPE="QUERY"
          DESC="服务绿通客户平均响应时间及服务绿通客户总人数">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
        </InParam>
        <Content><![CDATA[
			select
				nvl(decode(task_num, 0, 0, round(response_time/task_num)), 0) as avg_response_time,
                total_num
			from (
				select
					nvl(sum(decode(a.task_status, '3', 1, '4', 1, '8', 1, 0)), 0) as task_num,
                    count(distinct request_no) as total_num,
					round(nvl(sum(decode(a.task_status, '3', (a.deal_datetime - a.white_datetime), '4', (a.deal_datetime - a.white_datetime), '8', (a.deal_datetime - a.white_datetime), 0)), 0) * 24 * 60 *60)  as response_time
				from crh_ac.flowtaskrecorddetails a
				where a.white_flag = '1'
				and   a.white_datetime >= trunc(sysdate)
				and   a.white_datetime < trunc(sysdate) +1
				and   invalid_flag = '0'
			)
		]]></Content>
    </Func>

    <Func ID="MONITOR2030" MODULE_NAME="服务绿通客户总人数（合并到MONITOR2029）" TYPE="QUERY"
          DESC="服务绿通客户总人数（合并到MONITOR2029）">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
        </InParam>
        <Content><![CDATA[
			select
				count(distinct request_no) as total_num
			from crh_ac.flowtaskrecorddetails
			where white_flag = '1'
			and   request_datetime >= trunc(sysdate)
			and   request_datetime < trunc(sysdate) +1
			and   invalid_flag = '0'
		]]></Content>
    </Func>

    <Func ID="MONITOR2031" MODULE_NAME="服务绿通客户分公司排名" TYPE="QUERY" DESC="服务绿通客户分公司排名">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
        </InParam>
        <Content><![CDATA[
			select
				up_branch_name as branch_name,
				count(distinct request_no) as total_num
			from crh_ac.flowtaskrecorddetails
			where white_flag = '1'
			and   finish_datetime >= trunc(sysdate)
			and   finish_datetime < trunc(sysdate) +1
			and   invalid_flag = '0'
			group by up_branch_name
			order by count(distinct request_no) desc
		]]></Content>
    </Func>

    <Func ID="MONITOR2032" MODULE_NAME="智能审核规则命中TOP5" TYPE="QUERY" DESC="智能审核规则命中TOP5">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
        </InParam>
        <Content><![CDATA[
			with a as(
				SELECT
					null as rule_type,
					null as rule_name
				FROM dual
			), b as (
				select
					a.rule_type,
					a.rule_name,
					count(1) as aiaudit_num,
					row_number() over(partition by a.rule_type order by count(1) desc) as rank_num
				from a
				group by a.rule_type, a.rule_name

			), c as (
				select
					a.rule_type,
					count(1) as aiaudit_type_total_num
				from a
				group by a.rule_type
			)
			select
				b.rule_type,
				b.rule_name,
				b.aiaudit_num,
				b.rank_num,
				c.aiaudit_type_total_num,
				100 * round(b.aiaudit_num/c.aiaudit_type_total_num, 4) as aiaudit_type_radio
			from b, c
			where b.rule_type = c.rule_type
			and  b.rank_num <=5
			order by b.rule_type desc, b.rank_num
		]]></Content>
    </Func>

    <Func ID="MONITOR2033" MODULE_NAME="一键外呼业务量表总数查询" TYPE="QUERY" DESC="一键外呼业务量表总数查询">
        <InParam>
            <Param Code="staff_no" Name="员工号"/>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="operator_no" Name="工号"/>
            <Param Code="operator_name" Name="见证岗姓名"/>
            <Param Code="busin_type" Name="业务类型"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			with a as (
				select
					staff_name,
					staffid,
					operator_branch_no,
					operator_branch_name,
					operator_up_branch_no,
					operator_up_branch_name,
					is_answer,
					call_status,
					call_duration,
					keynote_flag,
					create_datetime,
					busin_type
				from crh_ac.calldetails
				where call_status != '2'
				<if condition="@start_time != null && @start_time.length() > 0">
					and create_datetime >= to_date('@start_time' , 'yyyy-mm-dd hh24:mi:ss')
				</if>
				<if condition="@end_time != null && @end_time.length() > 0">
					and create_datetime <= to_date('@end_time' , 'yyyy-mm-dd hh24:mi:ss')
				</if>
				<if condition="@operator_no != null && @operator_no.length() > 0">
					and staffid = '@operator_no'
				</if>
				<if condition="@branch_no != null && @branch_no.length() > 0">
					and instr(','||'@branch_no'||',',','||operator_branch_no||',')>0
				</if>
				<if condition="@operator_name != null && @operator_name.length() > 0">
					and staff_name = '@operator_name'
				</if>
				<if condition="@up_branch_no != null && @up_branch_no.length() > 0">
					and instr(','||'@up_branch_no'||',',','||operator_up_branch_no||',')>0
				</if>
				<if condition="@busin_type != null && @busin_type.length() > 0">
					and instr(','||'@busin_type'||',',','||busin_type||',')>0
				</if>
				<if condition="@staff_no != null && @staff_no.length() > 0">
					and ((instr(','||(select en_branch_nos from crh_user.operatorinfo where staff_no = '@staff_no')||',',','||operator_branch_no||',')>0 and operator_branch_no != '3') or ( select branch_No from crh_user.operatorinfo where staff_no = '@staff_no') = '3')
				</if>
			), b  as (
				select
					staff_name,
					staffid,
					operator_branch_name,
					operator_up_branch_name,
					count(1)    as call_total_num,
					sum(decode(is_answer, 1, 1, 0)) as call_answer_num,
					sum(decode(is_answer, 1, call_duration, 0)) as call_duration_total,
					sum(decode(is_answer, 1, decode(keynote_flag, 1, 1, 0), 0)) as call_answer_keynote_num
				from a
				group by staff_name, staffid, operator_branch_name, operator_up_branch_name
			)
			select
				count(1) as total_num
			from b
		]]></Content>
    </Func>

    <Func ID="MONITOR2034" MODULE_NAME="一键外呼业务量表分页查询" TYPE="QUERY" DESC="一键外呼业务量表分页查询">
        <InParam>
            <Param Code="staff_no" Name="员工号"/>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="operator_no" Name="工号"/>
            <Param Code="operator_name" Name="见证岗姓名"/>
            <Param Code="busin_type" Name="业务类型"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			with a as (
				select
					staff_name,
					staffid,
					operator_branch_no,
					operator_branch_name,
					operator_up_branch_no,
					operator_up_branch_name,
					is_answer,
					call_status,
					call_duration,
					keynote_flag,
					create_datetime,
					busin_type,
					label_options,
					hang_up_side
				from crh_ac.calldetails
				where call_status != '2'
				<if condition="@start_time != null && @start_time.length() > 0">
					and create_datetime >= to_date('@start_time' , 'yyyy-mm-dd hh24:mi:ss')
				</if>
				<if condition="@end_time != null && @end_time.length() > 0">
					and create_datetime <= to_date('@end_time' , 'yyyy-mm-dd hh24:mi:ss')
				</if>
				<if condition="@operator_no != null && @operator_no.length() > 0">
					and staffid = '@operator_no'
				</if>
				<if condition="@branch_no != null && @branch_no.length() > 0">
					and instr(','||'@branch_no'||',',','||operator_branch_no||',')>0
				</if>
				<if condition="@operator_name != null && @operator_name.length() > 0">
					and staff_name = '@operator_name'
				</if>
				<if condition="@up_branch_no != null && @up_branch_no.length() > 0">
					and instr(','||'@up_branch_no'||',',','||operator_up_branch_no||',')>0
				</if>
				<if condition="@busin_type != null && @busin_type.length() > 0">
					and instr(','||'@busin_type'||',',','||busin_type||',')>0
				</if>
				<if condition="@staff_no != null && @staff_no.length() > 0">
					and ((instr(','||(select en_branch_nos from crh_user.operatorinfo where staff_no = '@staff_no')||',',','||operator_branch_no||',')>0 and operator_branch_no != '3') or ( select branch_No from crh_user.operatorinfo where staff_no = '@staff_no') = '3')
				</if>
			), b  as (
				select
					staff_name,
					staffid,
					operator_branch_name,
					operator_up_branch_name,
					count(1)    as call_total_num,
					sum(decode(is_answer, 1, 1, 0)) as call_answer_num_all,
					sum(decode(is_answer, 1, case when (call_duration < 5 and hang_up_side = 0) or call_duration = 0 then 0 else 1 end, 0)) as call_answer_num,
					sum(decode(is_answer, 1, call_duration, 0)) as call_duration_total,
					sum(decode(is_answer, 1, decode(case when (call_duration < 5 and hang_up_side = 0) or call_duration = 0 then 0 else 1 end, 1, case when instr(','||LABEL_OPTIONS||',',','||'2'||',')>0 then 1 else 0 end, 0), 0)) as call_answer_keynote_num,
                    sum(decode(is_answer, 1, decode(case when (call_duration < 5 and hang_up_side = 0) or call_duration = 0 then 0 else 1 end, 1, case when instr(','||LABEL_OPTIONS||',',','||'21'||',')>0 then 1 else 0 end, 0), 0)) AS call_answer_same_address_num,
                    sum(decode(is_answer, 1, decode(case when (call_duration < 5 and hang_up_side = 0) or call_duration = 0 then 0 else 1 end, 1, case when instr(','||LABEL_OPTIONS||',',','||'22'||',')>0 then 1 else 0 end, 0), 0)) AS call_answer_multiple_open_account_num
				from a
				group by staff_name, staffid, operator_branch_name, operator_up_branch_name
			), m as(
				select
					'合计' as staff_name,
					'--' as staffid,
					'--' as operator_branch_name,
					'--' as operator_up_branch_name,
					count(1)    as call_total_num,
					sum(decode(is_answer, 1, 1, 0)) as call_answer_num_all,
					nvl(sum(decode(is_answer, 1, case when (call_duration < 5 and hang_up_side = 0) or call_duration = 0 then 0 else 1 end, 0)), 0) as call_answer_num,
					nvl(sum(decode(is_answer, 1, call_duration, 0)), 0) as call_duration_total,
					nvl(sum(decode(is_answer, 1, decode(case when (call_duration < 5 and hang_up_side = 0) or call_duration = 0 then 0 else 1 end, 1, case when instr(','||LABEL_OPTIONS||',',','||'2'||',')>0 then 1 else 0 end, 0), 0)), 0) as call_answer_keynote_num,
					nvl(sum(decode(is_answer, 1, decode(case when (call_duration < 5 and hang_up_side = 0) or call_duration = 0 then 0 else 1 end, 1, case when instr(','||LABEL_OPTIONS||',',','||'21'||',')>0 then 1 else 0 end, 0), 0)),0) AS call_answer_same_address_num,
                    nvl(sum(decode(is_answer, 1, decode(case when (call_duration < 5 and hang_up_side = 0) or call_duration = 0 then 0 else 1 end, 1, case when instr(','||LABEL_OPTIONS||',',','||'22'||',')>0 then 1 else 0 end, 0), 0)),0) AS call_answer_multiple_open_account_num
				from a
			)
			select
				m.staff_name,
				m.staffid,
				m.operator_branch_name,
				m.operator_up_branch_name,
				m.call_total_num,
				m.call_answer_num,
				m.call_duration_total,
				m.call_answer_keynote_num,
				m.call_answer_same_address_num,
				m.call_answer_multiple_open_account_num,
				decode(m.call_answer_num, 0, 0, m.call_duration_total/m.call_answer_num_all) as avg_call_duration,
				to_char(100 * round(decode(m.call_total_num, 0, 0, m.call_answer_num_all/m.call_total_num), 4),'fm9999990.00') || '%' as call_answer_rate,
				rownum rn
			from
				m
			union all
			select
				*
			from
			(
				select
					staff_name,
					staffid,
					operator_branch_name,
					operator_up_branch_name,
					call_total_num,
					call_answer_num,
					call_duration_total,
					call_answer_keynote_num,
					call_answer_same_address_num,
				    call_answer_multiple_open_account_num,
					decode(call_answer_num, 0, 0, call_duration_total/call_answer_num_all) as avg_call_duration,
					to_char(100 * round(decode(call_total_num, 0, 0, call_answer_num_all/call_total_num), 4),'fm9999990.00') || '%' as call_answer_rate,
					rownum rn
				from b
				order by staffid
			)
			<if condition="@page_num != null && @page_size != null">
				WHERE rn BETWEEN ( @page_num-1 ) * @page_size + 1 AND @page_num * @page_size
			</if>
		]]></Content>
    </Func>

    <Func ID="MONITOR2035" MODULE_NAME="今日见证人产能分布" TYPE="QUERY" DESC="今日见证人产能分布">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
        </InParam>
        <Content><![CDATA[
			select
				nvl(t3.total_num, 0) as total_num,
				nvl(t3.zb_total_num, 0) as zb_total_num,
				nvl(t3.tg_total_num, 0) as tg_total_num,
				nvl(t3.headTodayTaskCount0, 0) as headTodayTaskCount0,
				nvl(t3.headTodayTaskCount100, 0) as headTodayTaskCount100,
				nvl(t3.headTodayTaskCount200, 0) as headTodayTaskCount200,
				nvl(t3.headTodayTaskCount300, 0) as headTodayTaskCount300,
				nvl(t3.headTodayTaskCount400, 0) as headTodayTaskCount400,
				nvl(t3.headTodayTaskCount500, 0) as headTodayTaskCount500,
				nvl(t3.tgTodayTaskCount0, 0) as tgTodayTaskCount0,
				nvl(t3.tgTodayTaskCount100, 0) as tgTodayTaskCount100,
				nvl(t3.tgTodayTaskCount200, 0) as tgTodayTaskCount200,
				nvl(t3.tgTodayTaskCount300, 0) as tgTodayTaskCount300,
				nvl(t3.tgTodayTaskCount400, 0) as tgTodayTaskCount400,
				nvl(t3.tgTodayTaskCount500, 0) as tgTodayTaskCount500,
				to_char(nvl(decode(t3.zb_total_num , 0 , 0 , 100 * round(t3.headTodayTaskCount0/t3.zb_total_num, 4)), 0),'fm9999990.00') || '%' as headTodayTaskCount0RadioStr,
				to_char(nvl(decode(t3.zb_total_num , 0 , 0 , 100 * round(t3.headTodayTaskCount100/t3.zb_total_num, 4)), 0),'fm9999990.00') || '%' as headTodayTaskCount100RadioStr,
				to_char(nvl(decode(t3.zb_total_num , 0 , 0 , 100 * round(t3.headTodayTaskCount200/t3.zb_total_num, 4)), 0),'fm9999990.00') || '%' as headTodayTaskCount200RadioStr,
				to_char(nvl(decode(t3.zb_total_num , 0 , 0 , 100 * round(t3.headTodayTaskCount300/t3.zb_total_num, 4)), 0),'fm9999990.00') || '%' as headTodayTaskCount300RadioStr,
				to_char(nvl(decode(t3.zb_total_num , 0 , 0 , 100 * round(t3.headTodayTaskCount400/t3.zb_total_num, 4)), 0),'fm9999990.00') || '%' as headTodayTaskCount400RadioStr,
				to_char(nvl(decode(t3.zb_total_num , 0 , 0 , 100 * round(t3.headTodayTaskCount500/t3.zb_total_num, 4)), 0),'fm9999990.00') || '%' as headTodayTaskCount500RadioStr,
				to_char(nvl(decode(t3.tg_total_num , 0 , 0 , 100 * round(t3.tgTodayTaskCount0/t3.tg_total_num, 4)), 0),'fm9999990.00') || '%' as tgTodayTaskCount0RadioStr,
				to_char(nvl(decode(t3.tg_total_num , 0 , 0 , 100 * round(t3.tgTodayTaskCount100/t3.tg_total_num, 4)), 0),'fm9999990.00') || '%' as tgTodayTaskCount100RadioStr,
				to_char(nvl(decode(t3.tg_total_num , 0 , 0 , 100 * round(t3.tgTodayTaskCount200/t3.tg_total_num, 4)), 0),'fm9999990.00') || '%' as tgTodayTaskCount200RadioStr,
				to_char(nvl(decode(t3.tg_total_num , 0 , 0 , 100 * round(t3.tgTodayTaskCount300/t3.tg_total_num, 4)), 0),'fm9999990.00') || '%' as tgTodayTaskCount300RadioStr,
				to_char(nvl(decode(t3.tg_total_num , 0 , 0 , 100 * round(t3.tgTodayTaskCount400/t3.tg_total_num, 4)), 0),'fm9999990.00') || '%' as tgTodayTaskCount400RadioStr,
				to_char(nvl(decode(t3.tg_total_num , 0 , 0 , 100 * round(t3.tgTodayTaskCount500/t3.tg_total_num, 4)), 0),'fm9999990.00') || '%' as tgTodayTaskCount500RadioStr
			from (
				select
					count(1) as total_num,
					sum(decode(zb_flag, 1, 1, 0)) as zb_total_num,
					sum(decode(zb_flag, 1, decode(sign(100 - num), 1, 1, 0), 0)) as headTodayTaskCount0,
					sum(decode(zb_flag, 1, decode(sign(200 - num), 1, 1, 0), 0)) - sum(decode(zb_flag, 1, decode(sign(100 - num), 1, 1, 0), 0)) as headTodayTaskCount100,
					sum(decode(zb_flag, 1, decode(sign(300 - num), 1, 1, 0), 0)) - sum(decode(zb_flag, 1, decode(sign(200 - num), 1, 1, 0), 0)) as headTodayTaskCount200,
					sum(decode(zb_flag, 1, decode(sign(400 - num), 1, 1, 0), 0)) - sum(decode(zb_flag, 1, decode(sign(300 - num), 1, 1, 0), 0)) as headTodayTaskCount300,
					sum(decode(zb_flag, 1, decode(sign(500 - num), 1, 1, 0), 0)) - sum(decode(zb_flag, 1, decode(sign(400 - num), 1, 1, 0), 0)) as headTodayTaskCount400,
					sum(decode(zb_flag, 1, 1, 0)) - sum(decode(zb_flag, 1, decode(sign(500 - num), -1, 0, 1), 0)) as headTodayTaskCount500,
					sum(decode(zb_flag, 1, 0, 1)) as tg_total_num,
					sum(decode(zb_flag, 0, decode(sign(100 - num), 1, 1, 0), 0)) as tgTodayTaskCount0,
					sum(decode(zb_flag, 0, decode(sign(200 - num), 1, 1, 0), 0)) - sum(decode(zb_flag, 0, decode(sign(100 - num), 1, 1, 0), 0)) as tgTodayTaskCount100,
					sum(decode(zb_flag, 0, decode(sign(300 - num), 1, 1, 0), 0)) - sum(decode(zb_flag, 0, decode(sign(200 - num), 1, 1, 0), 0)) as tgTodayTaskCount200,
					sum(decode(zb_flag, 0, decode(sign(400 - num), 1, 1, 0), 0)) - sum(decode(zb_flag, 0, decode(sign(300 - num), 1, 1, 0), 0)) as tgTodayTaskCount300,
					sum(decode(zb_flag, 0, decode(sign(500 - num), 1, 1, 0), 0)) - sum(decode(zb_flag, 0, decode(sign(400 - num), 1, 1, 0), 0)) as tgTodayTaskCount400,
					sum(decode(zb_flag, 0, 1, 0)) - sum(decode(zb_flag, 0, decode(sign(500 - num), -1, 0, 1), 0)) as tgTodayTaskCount500
				from (
					select
						count(1) as num,
						decode(t1.operator_branch_no, '3', '1', '0') as zb_flag,
						t1.operator_no
					from crh_ac.flowtaskrecorddetails t1
					where   t1.finish_datetime >= trunc(sysdate)
					and     t1.finish_datetime < trunc(sysdate) +1
					and     t1.invalid_flag = '0'
					and     t1.operator_no != ' '
					group by decode(t1.operator_branch_no, '3', '1', '0'), t1.operator_no
				)t2
			)t3
		]]></Content>
    </Func>

    <Func ID="MONITOR2036" MODULE_NAME="今日个人审核量（总部）top5" TYPE="QUERY" DESC="今日个人审核量（总部）top5">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
        </InParam>
        <Content><![CDATA[
			select
				operator_no,
				operator_name,
				total_num,
				audit_num,
				review_num
			from (
				select
					count(1) as total_num,
					sum(decode(t1.task_type, 'audit', 1, 0)) as audit_num,
					sum(decode(t1.task_type, 'review', 1, 'secondary_review', 1, 0)) as review_num,
					t1.operator_no,
					t1.operator_name
				from crh_ac.flowtaskrecorddetails t1
				where   t1.finish_datetime >= trunc(sysdate)
				and     t1.finish_datetime < trunc(sysdate) +1
				and     t1.invalid_flag = '0'
				and     t1.operator_no != ' '
				and     t1.operator_branch_no = '3'
				and     t1.task_status in ('3', '4', '8')
				group by t1.operator_no, t1.operator_name
				order by count(1) desc
			) where rownum <= 5
			order by total_num desc
		]]></Content>
    </Func>

    <Func ID="MONITOR2037" MODULE_NAME="今日个人审核量（托管）top5" TYPE="QUERY" DESC="今日个人审核量（托管）top5">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
        </InParam>
        <Content><![CDATA[
			select
				operator_no,
				operator_name,
				total_num,
				audit_num,
				review_num
			from (
				select
					count(1) as total_num,
					sum(decode(t1.task_type, 'audit', 1, 0)) as audit_num,
					sum(decode(t1.task_type, 'review', 1, 'secondary_review', 1, 0)) as review_num,
					t1.operator_no,
					t1.operator_name
				from crh_ac.flowtaskrecorddetails t1
				where   t1.finish_datetime >= trunc(sysdate)
				and     t1.finish_datetime < trunc(sysdate) +1
				and     t1.invalid_flag = '0'
				and     t1.operator_no != ' '
				and     t1.operator_branch_no != '3'
				and     t1.task_status in ('3', '4', '8')
				group by t1.operator_no, t1.operator_name
				order by count(1) desc
			) where rownum <= 5
			order by total_num desc
		]]></Content>
    </Func>

    <Func ID="MONITOR2038" MODULE_NAME="未暂存数据响应平均时长小时趋势" TYPE="QUERY"
          DESC="未暂存数据响应平均时长小时趋势">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
        </InParam>
        <Content><![CDATA[
			select
				query_time,
				avgnohanduptime
			from (
				select
					to_char(create_datetime, 'hh24') || ':00' as query_time,
                    create_datetime,
					avgnohanduptime
				from (
					select
						create_datetime,
						avgnohanduptime
					from crh_monitor.houravgnohanduptime
					order by create_datetime desc
				) where rownum < 8
                union all
                select
                    to_char(sysdate, 'hh24') || ':' || to_char(sysdate, 'mi') as query_time,
                    sysdate,
                    decode(l.finish_no_handup_sum , 0 , 0 , round(l.no_handup_deal_time/l.finish_no_handup_sum)) as avg_no_handup_time
                from (
                    select
                        nvl(sum(decode(e.flag, '2', 1, '4', 1, 0)), 0) as finish_no_handup_sum,
                        nvl(round(sum(decode(e.flag, '2', e.first_deal_datetime - e.request_datetime, '4', e.first_deal_datetime - e.request_datetime, 0)) * 24 * 60 *60), 0) as no_handup_deal_time
                    from (
                        select
                            a.request_datetime,
                            a.first_deal_datetime,
                            decode(audit_finish_datetime, null, a.pause_flag, decode(a.review_finish_datetime, null, decode(a.pause_flag, '0', '2', '1', '3', '6'), decode(a.pause_flag, '0', '4', '1', '5', '6'))) as flag
                        from crh_ac.businprocessrequestaudittrail a
                        where a.user_up_branch_no != '3'
                        and   a.user_up_branch_no != ' '
						and   a.activity_no not in ('201809_GXMSY', '201906_JXYMT', '201905_NXYXB')
                        and   a.request_datetime >= sysdate - 1/24
                    ) e
                ) l
			)
            order by create_datetime
		]]></Content>
    </Func>

    <Func ID="MONITOR2039" MODULE_NAME="未暂存数据响应平均时长日趋势" TYPE="QUERY" DESC="未暂存数据响应平均时长日趋势">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
        </InParam>
        <Content><![CDATA[
			select
				to_char(create_datetime, 'mmdd') as  query_day,
				avgnohanduptime
			from (
				select
					create_datetime,
					avgnohanduptime
				from (
					select
						create_datetime,
						avgnohanduptime
					from crh_monitor.dailyavgnohanduptime
					union all
					select
                        sysdate,
                        decode(l.finish_no_handup_sum , 0 , 0 , round(l.no_handup_deal_time/l.finish_no_handup_sum)) as avg_no_handup_time
                    from (
                        select
                            nvl(sum(decode(e.flag, '2', 1, '4', 1, 0)), 0) as finish_no_handup_sum,
                            nvl(round(sum(decode(e.flag, '2', e.first_deal_datetime - e.request_datetime, '4', e.first_deal_datetime - e.request_datetime, 0)) * 24 * 60 *60), 0) as no_handup_deal_time
                        from (
                            select
                                a.request_datetime,
                                a.first_deal_datetime,
                                decode(audit_finish_datetime, null, a.pause_flag, decode(a.review_finish_datetime, null, decode(a.pause_flag, '0', '2', '1', '3', '6'), decode(a.pause_flag, '0', '4', '1', '5', '6'))) as flag
                            from crh_ac.businprocessrequestaudittrail a
                            where a.user_up_branch_no != '3'
                            and   a.user_up_branch_no != ' '
							and   a.time_property = '1'
							and   a.activity_no not in ('201809_GXMSY', '201906_JXYMT', '201905_NXYXB')
                            and   a.request_datetime >= trunc(sysdate)
                        ) e
                    ) l
				)
				order by create_datetime desc
			) where rownum <= 31
			order by create_datetime
		]]></Content>
    </Func>
    <Func ID="MONITOR2040" MODULE_NAME="当天分公司开户量报表" TYPE="QUERY" DESC="当天分公司开户量报表">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="en_branch_nos" Name="可操作营业部"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="busin_type" Name="业务类型"/>
        </InParam>
        <Content><![CDATA[
			WITH combined_data AS (
				SELECT
					COALESCE(a.user_up_branch_no, b.user_up_branch_no) AS user_up_branch_no,
					COALESCE(a.user_up_branch_name, b.user_up_branch_name) AS user_up_branch_name,
					(COALESCE(a.review_count, 0) + COALESCE(b.secondary_count, 0)) AS time_complete_count
				FROM (
					SELECT
						user_up_branch_no,
						user_up_branch_name,
						COUNT(*) AS review_count
					FROM
						CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL
					WHERE request_status = 'review-3'
						AND review_finish_datetime BETWEEN to_date('@start_time', 'yyyy-mm-dd hh24:mi:ss') AND to_date('@end_time', 'yyyy-mm-dd hh24:mi:ss')
					<if condition="@busin_type != null && @busin_type.length() > 0">
						and instr(','||'@busin_type'||',',','||busin_type||',')>0
					</if>
					<if condition="@branch_no != null && @branch_no.length() > 0">
						AND instr(',' || '@branch_no' || ',', ',' || user_branch_no || ',') > 0
					</if>
					<if condition="@en_branch_nos != null && @en_branch_nos.length() > 0">
						AND instr(',' || '@en_branch_nos' || ',', ',' || user_branch_no || ',') > 0
					</if>
					GROUP BY
						user_up_branch_no,
						user_up_branch_name
				) a
				FULL JOIN (
					SELECT
						user_up_branch_no,
						user_up_branch_name,
						COUNT(*) AS secondary_count
					FROM
						CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL
					WHERE request_status = 'secondary_review-3'
						and sec_rv_finish_datetime BETWEEN to_date('@start_time', 'yyyy-mm-dd hh24:mi:ss') AND to_date('@end_time', 'yyyy-mm-dd hh24:mi:ss')
					<if condition="@busin_type != null && @busin_type.length() > 0">
						and instr(','||'@busin_type'||',',','||busin_type||',')>0
					</if>
					<if condition="@branch_no != null && @branch_no.length() > 0">
						AND instr(',' || '@branch_no' || ',', ',' || user_branch_no || ',') > 0
					</if>
					<if condition="@en_branch_nos != null && @en_branch_nos.length() > 0">
						AND instr(',' || '@en_branch_nos' || ',', ',' || user_branch_no || ',') > 0
					</if>
					GROUP BY
						user_up_branch_no,
						user_up_branch_name
				) b
				ON a.user_up_branch_no = b.user_up_branch_no
				AND a.user_up_branch_name = b.user_up_branch_name
			)
			SELECT
				 null AS user_up_branch_no,
				'总计' AS up_branch_name,
				SUM(time_complete_count) AS time_complete_count
				FROM combined_data
			UNION ALL
			SELECT
				user_up_branch_no,
				user_up_branch_name as up_branch_name,
				time_complete_count
			FROM combined_data
			ORDER BY time_complete_count DESC
		]]></Content>
    </Func>

    <Func ID="MONITOR2042" MODULE_NAME="历史分公司开户量报表" TYPE="QUERY" DESC="历史分公司开户量报表">
        <InParam>
            <Param Code="staff_no" Name="员工号"/>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="en_branch_nos" Name="可操作营业部"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="busin_type" Name="业务类型"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			WITH BranchData AS (
				SELECT
					SUM(complete_num) AS today_complete_num,
					SUM(month_complete_num) AS month_complete_num,
					SUM(year_complete_num) AS year_complete_num,
					up_branch_no,
					up_branch_name
				FROM
					CRH_MONITOR.BUSINREVIEWCOMPLETE
				WHERE
					curr_date = TO_NUMBER(TO_CHAR(TO_DATE('@start_time', 'yyyy-mm-dd hh24:mi:ss'), 'YYYYMMDD'))
					<if condition="@busin_type != null && @busin_type.length() > 0">
						and instr(','||'@busin_type'||',',','||busin_type||',')>0
					</if>

					<if condition="@branch_no != null && @branch_no.length() > 0">
						AND instr(',' || '@branch_no' || ',', ',' || branch_no || ',') > 0
					</if>

					<if condition="@en_branch_nos != null && @en_branch_nos.length() > 0">
						AND instr(',' || '@en_branch_nos' || ',', ',' || branch_no || ',') > 0
					</if>
					GROUP BY up_branch_no, up_branch_name
			),
			RownumData AS (
				SELECT
					b.*,
					rownum as rn
				FROM
					BranchData b
			)
			SELECT
			  '合计' AS up_branch_no,
			  '合计' AS up_branch_name,
			  COALESCE(SUM(today_complete_num), 0) AS today_complete_num,
			  COALESCE(SUM(month_complete_num), 0) AS month_complete_num,
			  COALESCE(SUM(year_complete_num), 0) AS year_complete_num
			FROM
			  BranchData
			UNION ALL
			SELECT
				r.up_branch_no,
				r.up_branch_name,
				r.today_complete_num,
				r.month_complete_num,
				r.year_complete_num
			FROM RownumData r
			<if condition="@page_num != null && @page_size != null">
				WHERE r.rn BETWEEN ( @page_num - 1 ) * @page_size + 1 AND @page_num * @page_size
			</if>
		]]></Content>
    </Func>
    <Func ID="MONITOR2043" MODULE_NAME="历史分公司开户量报表总数" TYPE="QUERY" DESC="历史分公司开户量报表总数">
        <InParam>
            <Param Code="staff_no" Name="员工号"/>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="en_branch_nos" Name="可操作营业部"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="busin_type" Name="业务类型"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			WITH BranchData AS (
				SELECT
					SUM(complete_num) AS today_complete_num,
					SUM(month_complete_num) AS month_complete_num,
					SUM(year_complete_num) AS year_complete_num,
					up_branch_no,
					up_branch_name
				FROM
					CRH_MONITOR.BUSINREVIEWCOMPLETE
				WHERE
					curr_date = TO_NUMBER(TO_CHAR(TO_DATE('@start_time', 'yyyy-mm-dd hh24:mi:ss'), 'YYYYMMDD'))
					<if condition="@busin_type != null && @busin_type.length() > 0">
						and instr(','||'@busin_type'||',',','||busin_type||',')>0
					</if>

					<if condition="@branch_no != null && @branch_no.length() > 0">
						AND instr(',' || '@branch_no' || ',', ',' || branch_no || ',') > 0
					</if>

					<if condition="@en_branch_nos != null && @en_branch_nos.length() > 0">
						AND instr(',' || '@en_branch_nos' || ',', ',' || branch_no || ',') > 0
					</if>
					GROUP BY up_branch_no, up_branch_name
			)
			SELECT
				count(*) as total_num
			FROM BranchData;
		]]></Content>
    </Func>
    <Func ID="MONITOR2044" MODULE_NAME="见证人岗位报表总数" TYPE="QUERY" DESC="见证人岗位报表总数">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
            SELECT
                COUNT(*) AS total_num
            FROM
                CRH_MONITOR.COMPANYOPERATORSTATISTICS
            WHERE 1=1
                <if condition="@branch_no != null && @branch_no.length() > 0">
                    and instr(','||'@branch_no'||',',','||branch_no||',')>0
                </if>
		]]></Content>
    </Func>
    <Func ID="MONITOR2045" MODULE_NAME="见证人岗位报表" TYPE="QUERY" DESC="见证人岗位报表">
        <InParam>
            <Param Code="start_time" Name="起始日期"/>
            <Param Code="end_time" Name="结束日期"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			WITH CompanyData AS (
				SELECT
				    branch_no,
				    branch_name,
				    operator_frozen_num,
				    operator_normal_num,
				    operator_inactive_num,
				    audit_operator_num
				FROM
					CRH_MONITOR.COMPANYOPERATORSTATISTICS
				WHERE 1=1
					<if condition="@branch_no != null && @branch_no.length() > 0">
                        and instr(','||'@branch_no'||',',','||branch_no||',')>0
                    </if>
			),
			FlowTaskData AS (
				SELECT
				    MAX(operator_up_branch_no) AS branch_no,
				    COUNT(distinct operator_no) AS complete_num
				FROM
					CRH_AC.FLOWTASKRECORDDETAILS
				WHERE
                    task_status in ('3', '4', '8')
                    and operator_up_branch_no != ' '
					and finish_datetime >= to_date('@start_time' , 'yyyy-mm-dd hh24:mi:ss')
					and finish_datetime <= to_date('@end_time' , 'yyyy-mm-dd hh24:mi:ss')
					<if condition="@branch_no != null && @branch_no.length() > 0">
                        and instr(','||'@branch_no'||',',','||operator_up_branch_no||',')>0
                    </if>
					GROUP BY operator_up_branch_no
			),
			StatisticsData AS (
				SELECT
                    c.branch_no,
                    c.branch_name,
                    c.operator_frozen_num,
				    c.operator_normal_num,
				    c.operator_inactive_num,
                    c.audit_operator_num,
                    NVL(f.complete_num, 0) AS complete_num
                FROM
                    CompanyData c
                    LEFT JOIN FlowTaskData f ON c.branch_no = f.branch_no
			),
			RownumData AS (
				SELECT
					s.*,
					rownum AS rn
				FROM
					StatisticsData s
			)
			SELECT
			  '合计' AS branch_no,
			  '合计' AS branch_name,
			  COALESCE(SUM(operator_frozen_num), 0) AS operator_frozen_num,
			  COALESCE(SUM(operator_normal_num), 0) AS operator_normal_num,
			  COALESCE(SUM(operator_inactive_num), 0) AS operator_inactive_num,
			  COALESCE(SUM(audit_operator_num), 0) AS audit_operator_num,
			  COALESCE(SUM(complete_num), 0) AS complete_num
			FROM
			  StatisticsData
			UNION ALL
			SELECT
				r.branch_no,
				r.branch_name,
				r.operator_frozen_num,
                r.operator_normal_num,
                r.operator_inactive_num,
				r.audit_operator_num,
				r.complete_num
			FROM RownumData r
			<if condition="@page_num != null && @page_size != null">
				WHERE r.rn BETWEEN ( @page_num - 1 ) * @page_size + 1 AND @page_num * @page_size
			</if>
		]]></Content>
    </Func>


    <Func ID="MONITOR10000_page" MODULE_NAME="坐席个人业务报表（任务维度）详情" TYPE="QUERY" DESC="坐席个人业务报表（任务维度）详情">
        <InParam>
            <Param Code="task_create_time" Name="任务生成的时间"/>
            <Param Code="task_create_start_time" Name="任务生成查询开始时间"/>
            <Param Code="task_create_end_time" Name="任务生成查询结束时间"/>
            <Param Code="first_task" Name="是否首次统计"/>
            <Param Code="actual_operator_no" Name="执行人工号"/>
            <Param Code="actual_operator_name" Name="执行人姓名"/>
            <Param Code="push_time" Name="任务下发时间"/>
            <Param Code="push_start_time" Name="任务下发开始时间"/>
            <Param Code="push_end_time" Name="任务下发结束时间"/>
            <Param Code="deal_time" Name="任务处理时间"/>
            <Param Code="deal_start_time" Name="任务处理时间查询开始时间"/>
            <Param Code="deal_end_time" Name="任务处理时间查询结束时间"/>
            <Param Code="finish_time" Name="任务结束时间"/>
            <Param Code="finish_start_time" Name="任务结束时间查询开始时间"/>
            <Param Code="finish_end_time" Name="任务结束时间查询结束时间"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			WITH BranchData AS (
              SELECT
              ACTUAL_OPERATOR_NO AS operator_no,
              MAX( ACTUAL_OPERATOR_NAME ) AS operator_name,
              count( CASE WHEN MERGE_TYPE != 2 THEN 1 END ) AS task_complete_count,
              nvl(sum(CASE WHEN MERGE_TYPE != 2 THEN CALL_NUM END), 0) AS call_count,
              count( CASE WHEN ANSWER_STATUS = '2' AND MERGE_TYPE != 2 THEN 1 END ) AS call_connect_count,
              ROUND( count( CASE WHEN ANSWER_STATUS = '2' AND MERGE_TYPE != 2 THEN 1 END ) / NULLIF(sum(CASE WHEN MERGE_TYPE != 2 THEN CALL_NUM END), 0 ) * 100, 2 ) AS call_connect_rate,
              ROUND(sum( ( DEAL_DATETIME - PUSH_DATETIME ) * 24 * 60 * 60 ) / NULLIF( count( ( DEAL_DATETIME - PUSH_DATETIME ) * 24 * 60 * 60 ), 0 ),0) AS avg_response_duration,
              ROUND(sum( ( FINISH_DATETIME - DEAL_DATETIME ) * 24 * 60 * 60 ) / NULLIF( count( ( FINISH_DATETIME - DEAL_DATETIME ) * 24 * 60 * 60 ), 0 ),0) AS avg_handling_duration,
              ROUND( count( CASE WHEN HANDLE_STATUS = '1' THEN 1 END ) / NULLIF( COUNT( * ), 0 ) * 100, 2 ) AS visit_pass_rate,
              ROUND( count( CASE WHEN HANDLE_STATUS = '5' THEN 1 END ) / NULLIF( COUNT( * ), 0 ) * 100, 2 ) AS invalid_rate,
              ROUND( count( CASE WHEN HANDLE_STATUS = '6' THEN 1 END ) / NULLIF( COUNT( * ), 0 ) * 100, 2 ) AS reject_rate,
              ROUND( count( CASE WHEN QNAIRE_COMPLETION = 1 THEN 1 END ) / NULLIF( COUNT( * ), 0 ) * 100, 2 ) AS survey_completion_rate,
              ROUND(count( CASE WHEN ( DEAL_DATETIME - PUSH_DATETIME ) * 24 * 60 * 60 < 5 * 60 THEN 1 END ) / NULLIF( COUNT( * ), 0 ) * 100,2) AS response_0_5m_rate,
              ROUND(count(CASE WHEN ( DEAL_DATETIME - PUSH_DATETIME ) * 24 * 60 * 60 >= 5 * 60 AND ( DEAL_DATETIME - PUSH_DATETIME ) * 24 * 60 * 60 < 10 * 60 THEN 1 END) / NULLIF( COUNT( * ), 0 ) * 100,2) AS response_5_10m_rate,
              ROUND(count(CASE WHEN ( DEAL_DATETIME - PUSH_DATETIME ) * 24 * 60 * 60 >= 10 * 60 AND ( DEAL_DATETIME - PUSH_DATETIME ) * 24 * 60 * 60 < 15 * 60 THEN 1 END) / NULLIF( COUNT( * ), 0 ) * 100,2) AS response_10_15m_rate,
              ROUND(count( CASE WHEN ( DEAL_DATETIME - PUSH_DATETIME ) * 24 * 60 * 60 >= 15 * 60 THEN 1 END ) / NULLIF( COUNT( * ), 0 ) * 100,2) AS response_over_15m_rate,
              ROUND(count( CASE WHEN ( DEAL_DATETIME - PUSH_DATETIME ) * 24 * 60 * 60 >= 30 * 60 THEN 1 END ) / NULLIF( COUNT( * ), 0 ) * 100,2) AS response_over_30m_rate,
              ROUND(count( CASE WHEN ( DEAL_DATETIME - PUSH_DATETIME ) * 24 * 60 * 60 >= 45 * 60 THEN 1 END ) / NULLIF( COUNT( * ), 0 ) * 100,2) AS response_over_45m_rate
              FROM
                CRH_FUS.FOLLOWUPTASK f
            WHERE TASK_STATUS= '50'
            <if condition="@task_create_start_time != null && @task_create_start_time.length() > 0">
                AND CREATE_DATETIME >= TO_DATE('@task_create_start_time', 'YYYY-MM-DD HH24:MI:SS')
                AND CREATE_DATETIME <= TO_DATE('@task_create_end_time', 'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if condition="@first_task != null && @first_task.length() > 0">
                AND IS_FIRST_TASK = '@first_task'
            </if>
            <if condition="@actual_operator_no != null && @actual_operator_no.length() > 0">
                AND actual_operator_no = '@actual_operator_no'
            </if>
            <if condition="@actual_operator_name != null && @actual_operator_name.length() > 0">
                AND actual_operator_name LIKE CONCAT('@actual_operator_name', '%')
            </if>
            <if condition="@push_start_time != null && @push_start_time.length() > 0">
                AND PUSH_DATETIME >= TO_DATE('@push_start_time', 'YYYY-MM-DD HH24:MI:SS')
                AND PUSH_DATETIME <= TO_DATE('@push_end_time', 'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if condition="@deal_start_time != null && @deal_start_time.length() > 0">
                AND DEAL_DATETIME >= TO_DATE('@deal_start_time', 'YYYY-MM-DD HH24:MI:SS')
                AND DEAL_DATETIME <= TO_DATE('@deal_end_time', 'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if condition="@finish_start_time != null && @finish_start_time.length() > 0">
                AND FINISH_DATETIME >= TO_DATE('@finish_start_time', 'YYYY-MM-DD HH24:MI:SS')
                AND FINISH_DATETIME <= TO_DATE('@finish_end_time', 'YYYY-MM-DD HH24:MI:SS')
            </if>
            GROUP BY ACTUAL_OPERATOR_NO
            ),

			RownumData AS (
				SELECT
					b.*,
					rownum as rn
				FROM
					BranchData b
			)

			SELECT
				*
			FROM RownumData r
			<if condition="@page_num != null && @page_size != null">
				WHERE r.rn BETWEEN ( @page_num - 1 ) * @page_size + 1 AND @page_num * @page_size
			</if>
            ]]></Content>
    </Func>


    <Func ID="MONITOR10000_total" MODULE_NAME="坐席个人业务报表（任务维度）总数" TYPE="QUERY" DESC="坐席个人业务报表（任务维度）总数">
        <InParam>
            <Param Code="task_create_time" Name="任务生成的时间"/>
            <Param Code="task_create_start_time" Name="任务生成查询开始时间"/>
            <Param Code="task_create_end_time" Name="任务生成查询结束时间"/>
            <Param Code="first_task" Name="是否首次统计"/>
            <Param Code="actual_operator_no" Name="执行人工号"/>
            <Param Code="actual_operator_name" Name="执行人姓名"/>
            <Param Code="push_time" Name="任务下发时间"/>
            <Param Code="push_start_time" Name="任务下发开始时间"/>
            <Param Code="push_end_time" Name="任务下发结束时间"/>
            <Param Code="deal_time" Name="任务处理时间"/>
            <Param Code="deal_start_time" Name="任务处理时间查询开始时间"/>
            <Param Code="deal_end_time" Name="任务处理时间查询结束时间"/>
            <Param Code="finish_time" Name="任务结束时间"/>
            <Param Code="finish_start_time" Name="任务结束时间查询开始时间"/>
            <Param Code="finish_end_time" Name="任务结束时间查询结束时间"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			WITH BranchData AS (

              SELECT
                ACTUAL_OPERATOR_NO AS operator_no
            FROM
              CRH_FUS.FOLLOWUPTASK f
            WHERE TASK_STATUS= '50'
            <if condition="@task_create_start_time != null && @task_create_start_time.length() > 0">
                AND CREATE_DATETIME >= TO_DATE('@task_create_start_time', 'YYYY-MM-DD HH24:MI:SS')
                AND CREATE_DATETIME <= TO_DATE('@task_create_end_time', 'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if condition="@first_task != null && @first_task.length() > 0">
                AND IS_FIRST_TASK = '@first_task'
            </if>
            <if condition="@actual_operator_no != null && @actual_operator_no.length() > 0">
                AND actual_operator_no = '@actual_operator_no'
            </if>
            <if condition="@actual_operator_name != null && @actual_operator_name.length() > 0">
                AND actual_operator_name LIKE CONCAT('@actual_operator_name', '%')
            </if>
            <if condition="@push_start_time != null && @push_start_time.length() > 0">
                AND PUSH_DATETIME >= TO_DATE('@push_start_time', 'YYYY-MM-DD HH24:MI:SS')
                AND PUSH_DATETIME <= TO_DATE('@push_end_time', 'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if condition="@deal_start_time != null && @deal_start_time.length() > 0">
                AND DEAL_DATETIME >= TO_DATE('@deal_start_time', 'YYYY-MM-DD HH24:MI:SS')
                AND DEAL_DATETIME <= TO_DATE('@deal_end_time', 'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if condition="@finish_start_time != null && @finish_start_time.length() > 0">
                AND FINISH_DATETIME >= TO_DATE('@finish_start_time', 'YYYY-MM-DD HH24:MI:SS')
                AND FINISH_DATETIME <= TO_DATE('@finish_end_time', 'YYYY-MM-DD HH24:MI:SS')
            </if>
            GROUP BY ACTUAL_OPERATOR_NO
            )

            SELECT
            count(*) total
            FROM
                BranchData b;
            ]]></Content>
    </Func>


    <Func ID="MONITOR10000_sum" MODULE_NAME="坐席个人业务报表（任务维度）合计" TYPE="QUERY" DESC="坐席个人业务报表（任务维度）合计">
        <InParam>
            <Param Code="task_create_time" Name="任务生成的时间"/>
            <Param Code="task_create_start_time" Name="任务生成查询开始时间"/>
            <Param Code="task_create_end_time" Name="任务生成查询结束时间"/>
            <Param Code="first_task" Name="是否首次统计"/>
            <Param Code="actual_operator_no" Name="执行人工号"/>
            <Param Code="actual_operator_name" Name="执行人姓名"/>
            <Param Code="push_time" Name="任务下发时间"/>
            <Param Code="push_start_time" Name="任务下发开始时间"/>
            <Param Code="push_end_time" Name="任务下发结束时间"/>
            <Param Code="deal_time" Name="任务处理时间"/>
            <Param Code="deal_start_time" Name="任务处理时间查询开始时间"/>
            <Param Code="deal_end_time" Name="任务处理时间查询结束时间"/>
            <Param Code="finish_time" Name="任务结束时间"/>
            <Param Code="finish_start_time" Name="任务结束时间查询开始时间"/>
            <Param Code="finish_end_time" Name="任务结束时间查询结束时间"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			WITH BranchData AS (
            SELECT
                '合计' AS operator_no,
                '-' AS operator_name,
                count( CASE WHEN MERGE_TYPE != 2 THEN 1 END ) AS task_complete_count,
                nvl(sum(CASE WHEN MERGE_TYPE != 2 THEN CALL_NUM END), 0) AS call_count,
                count( CASE WHEN ANSWER_STATUS = '2' THEN 1 END ) AS call_connect_count,
                ROUND(count( CASE WHEN ANSWER_STATUS = '2' THEN 1 END ) / NULLIF(sum(CASE WHEN MERGE_TYPE != 2 THEN CALL_NUM END), 0 ) * 100, 2 ) AS call_connect_rate,
                ROUND(sum( ( DEAL_DATETIME - PUSH_DATETIME ) * 24 * 60 * 60 ) / NULLIF(count(( DEAL_DATETIME - PUSH_DATETIME ) * 24 * 60 * 60), 0) , 0 ) AS avg_response_duration,
                ROUND( nvl( sum( ( FINISH_DATETIME - DEAL_DATETIME ) * 24 * 60 * 60 ) / NULLIF(count(( FINISH_DATETIME - DEAL_DATETIME ) * 24 * 60 * 60), 0), 0 ), 0 ) AS avg_handling_duration,
                ROUND(count( CASE WHEN HANDLE_STATUS = '1' THEN 1 END ) / NULLIF(COUNT(*), 0) * 100 , 2 ) AS visit_pass_rate,
                ROUND(count( CASE WHEN HANDLE_STATUS = '5' THEN 1 END ) / NULLIF(COUNT(*), 0) * 100 , 2 ) AS invalid_rate,
                ROUND(count( CASE WHEN HANDLE_STATUS = '6' THEN 1 END ) / NULLIF(COUNT(*), 0) * 100 , 2 ) AS reject_rate,
                ROUND(count( CASE WHEN QNAIRE_COMPLETION = 1 THEN 1 END ) / NULLIF(COUNT(*), 0) * 100 , 2 ) AS survey_completion_rate,
                ROUND(count( CASE WHEN ( DEAL_DATETIME - PUSH_DATETIME ) * 24 * 60 * 60 < 5 * 60 THEN 1 END ) / NULLIF(COUNT(*), 0) * 100 , 2 ) AS response_0_5m_rate,
                ROUND(count( CASE WHEN ( DEAL_DATETIME - PUSH_DATETIME ) * 24 * 60 * 60 >= 5 * 60 AND ( DEAL_DATETIME - PUSH_DATETIME ) * 24 * 60 * 60 < 10 * 60 THEN 1 END ) / NULLIF(COUNT(*), 0) * 100 , 2 ) AS response_5_10m_rate,
                ROUND(count( CASE WHEN ( DEAL_DATETIME - PUSH_DATETIME ) * 24 * 60 * 60 >= 10 * 60 AND ( DEAL_DATETIME - PUSH_DATETIME ) * 24 * 60 * 60 < 15 * 60 THEN 1 END ) / NULLIF(COUNT(*), 0) * 100 , 2 ) AS response_10_15m_rate,
                ROUND(count( CASE WHEN ( DEAL_DATETIME - PUSH_DATETIME ) * 24 * 60 * 60 >= 15 * 60 THEN 1 END ) / NULLIF(COUNT(*), 0) * 100 , 2 ) AS response_over_15m_rate,
                ROUND(count( CASE WHEN ( DEAL_DATETIME - PUSH_DATETIME ) * 24 * 60 * 60 >= 30 * 60 THEN 1 END ) / NULLIF(COUNT(*), 0) * 100 , 2 ) AS response_over_30m_rate,
                ROUND(count( CASE WHEN ( DEAL_DATETIME - PUSH_DATETIME ) * 24 * 60 * 60 >= 45 * 60 THEN 1 END ) / NULLIF(COUNT(*), 0) * 100 , 2 ) AS response_over_45m_rate
            FROM
                CRH_FUS.FOLLOWUPTASK f
            WHERE TASK_STATUS= '50'
            <if condition="@task_create_start_time != null && @task_create_start_time.length() > 0">
                AND CREATE_DATETIME >= TO_DATE('@task_create_start_time', 'YYYY-MM-DD HH24:MI:SS')
                AND CREATE_DATETIME <= TO_DATE('@task_create_end_time', 'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if condition="@first_task != null && @first_task.length() > 0">
                AND IS_FIRST_TASK = '@first_task'
            </if>
            <if condition="@actual_operator_no != null && @actual_operator_no.length() > 0">
                AND actual_operator_no = '@actual_operator_no'
            </if>
            <if condition="@actual_operator_name != null && @actual_operator_name.length() > 0">
                AND actual_operator_name LIKE CONCAT('@actual_operator_name', '%')
            </if>
            <if condition="@push_start_time != null && @push_start_time.length() > 0">
                AND PUSH_DATETIME >= TO_DATE('@push_start_time', 'YYYY-MM-DD HH24:MI:SS')
                AND PUSH_DATETIME <= TO_DATE('@push_end_time', 'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if condition="@deal_start_time != null && @deal_start_time.length() > 0">
                AND DEAL_DATETIME >= TO_DATE('@deal_start_time', 'YYYY-MM-DD HH24:MI:SS')
                AND DEAL_DATETIME <= TO_DATE('@deal_end_time', 'YYYY-MM-DD HH24:MI:SS')
            </if>
            <if condition="@finish_start_time != null && @finish_start_time.length() > 0">
                AND FINISH_DATETIME >= TO_DATE('@finish_start_time', 'YYYY-MM-DD HH24:MI:SS')
                AND FINISH_DATETIME <= TO_DATE('@finish_end_time', 'YYYY-MM-DD HH24:MI:SS')
            </if>
            )

            SELECT
            *
            FROM
                BranchData b;
            ]]></Content>
    </Func>

    <Func ID="MONITOR10100_page" MODULE_NAME="回访业务量报表（申请维度|分公司|分页）" TYPE="QUERY" DESC="回访业务量报表（申请维度|分公司|分页）">
        <InParam>
            <Param Code="apply_start_time" Name="回访申请创建的开始时间"/>
            <Param Code="apply_end_time" Name="回访申请创建的结束时间"/>
            <Param Code="apply_time" Name="申请时间"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="busin_type" Name="申请类型"/>
            <Param Code="status" Name="申请状态"/>
            <Param Code="label" Name="标签"/>
            <Param Code="activity_name" Name="活动名称"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="channel_name" Name="渠道名称"/>
            <Param Code="marketing_team" Name="营销团队"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
        with page_data as (SELECT
            up_branch_no AS up_branch_no,
            COUNT(*) AS application_count,
            SUM(CASE WHEN status in ('7','8','9','10','11','12') THEN 1 ELSE 0 END) AS completed_count,
            ROUND(SUM(CASE WHEN status in ('7','8','9','10','11','12') THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS completion_rate,

            SUM(CASE WHEN status = '7' THEN 1 ELSE 0 END) AS approved_count,
            ROUND(SUM(CASE WHEN status = '7' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS approval_rate,

            SUM(CASE WHEN status = '8' THEN 1 ELSE 0 END) AS nonCompliant_count,
            ROUND(SUM(CASE WHEN status = '8' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS nonCompliance_rate,

            SUM(CASE WHEN status = '9' THEN 1 ELSE 0 END) AS refuse_follow_up_count,
            ROUND(SUM(CASE WHEN status = '9' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS refuse_follow_up_rate,

            SUM(CASE WHEN status = '11' THEN 1 ELSE 0 END) AS strategy_execution_Count,
            ROUND(SUM(CASE WHEN status = '11' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS strategy_execution_rate,

            SUM(CASE WHEN status = '10' THEN 1 ELSE 0 END) AS manual_end_count,
            ROUND(SUM(CASE WHEN status = '10' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS manual_end_rate,

            SUM(CASE WHEN LAST_QNAIRE_COMPLETION = 1 THEN 1 ELSE 0 END) AS questionnaire_completed_count,
            ROUND(SUM(CASE WHEN LAST_QNAIRE_COMPLETION = 1 THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS questionnaire_completed_rate
        FROM
            CRH_FUS.FOLLOWUPAPPLY
        WHERE
            CREATE_DATETIME >= TO_DATE('@apply_start_time', 'YYYY-MM-DD HH24:MI:SS')
            AND CREATE_DATETIME <= TO_DATE('@apply_end_time', 'YYYY-MM-DD HH24:MI:SS')

            <if condition="@up_branch_no != null && @up_branch_no.length() > 0">
			    and instr(','||'@up_branch_no'||',',','||up_branch_no||',')>0
			</if>

            <if condition="@busin_type != null && @busin_type.length() > 0">
			    and instr(','||'@busin_type'||',',','||busin_type||',')>0
			</if>

			<if condition="@label != null && @label.length() > 0">
			    and instr(','||'@label'||',',','||LABEL_OPTIONS||',')>0
			</if>

			<if condition="@activity_name != null && @activity_name.length() > 0">
			    AND ACTIVITY_NAME = '@activity_name'
			</if>

			<if condition="@channel_code != null && @channel_code.length() > 0">
			    AND CHANNEL_CODE = '@channel_code'
			</if>

			<if condition="@channel_name != null && @channel_name.length() > 0">
			    AND CHANNEL_NAME = '@channel_name'
			</if>

			<if condition="@marketing_team != null && @marketing_team.length() > 0">
			    AND MARKETING_TEAM = '@marketing_team'
			</if>
        GROUP BY
            up_branch_no
        ORDER BY
            application_count DESC
        ),
        RownumData AS (
				SELECT
					b.*,
					rownum as rn
				FROM
					page_data b
			)

			SELECT
				*
			FROM RownumData r
			<if condition="@page_num != null && @page_size != null">
				WHERE r.rn BETWEEN ( @page_num - 1 ) * @page_size + 1 AND @page_num * @page_size
			</if>
        ]]></Content>
    </Func>

    <Func ID="MONITOR10100_sum" MODULE_NAME="回访业务量报表（申请维度|分公司|合计）" TYPE="QUERY" DESC="回访业务量报表（申请维度|分公司|合计）">
        <InParam>
            <Param Code="apply_start_time" Name="回访申请创建的开始时间"/>
            <Param Code="apply_end_time" Name="回访申请创建的结束时间"/>
            <Param Code="apply_time" Name="申请时间"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="busin_type" Name="申请类型"/>
            <Param Code="status" Name="申请状态"/>
            <Param Code="label" Name="标签"/>
            <Param Code="activity_name" Name="活动名称"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="channel_name" Name="渠道名称"/>
            <Param Code="marketing_team" Name="营销团队"/>
        </InParam>
        <Content><![CDATA[
        SELECT

            '合计' as up_branch_no,
            COUNT(*) AS application_count,

            SUM(CASE WHEN status in ('7','8','9','10','11','12') THEN 1 ELSE 0 END) AS completed_count,
            ROUND(SUM(CASE WHEN status in ('7','8','9','10','11','12') THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS completion_rate,

            SUM(CASE WHEN status = '7' THEN 1 ELSE 0 END) AS approved_count,
            ROUND(SUM(CASE WHEN status = '7' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS approval_rate,

            SUM(CASE WHEN status = '8' THEN 1 ELSE 0 END) AS nonCompliant_count,
            ROUND(SUM(CASE WHEN status = '8' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS nonCompliance_rate,

            SUM(CASE WHEN status = '9' THEN 1 ELSE 0 END) AS refuse_follow_up_count,
            ROUND(SUM(CASE WHEN status = '9' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS refuse_follow_up_rate,

            SUM(CASE WHEN status = '11' THEN 1 ELSE 0 END) AS strategy_execution_Count,
            ROUND(SUM(CASE WHEN status = '11' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS strategy_execution_rate,

            SUM(CASE WHEN status = '10' THEN 1 ELSE 0 END) AS manual_end_count,
            ROUND(SUM(CASE WHEN status = '10' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS manual_end_rate,

            SUM(CASE WHEN LAST_QNAIRE_COMPLETION = 1 THEN 1 ELSE 0 END) AS questionnaire_completed_count,
            ROUND(SUM(CASE WHEN LAST_QNAIRE_COMPLETION = 1 THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS questionnaire_completed_rate
        FROM
            CRH_FUS.FOLLOWUPAPPLY
        WHERE
            CREATE_DATETIME >= TO_DATE('@apply_start_time', 'YYYY-MM-DD HH24:MI:SS')
            AND CREATE_DATETIME <= TO_DATE('@apply_end_time', 'YYYY-MM-DD HH24:MI:SS')

            <if condition="@up_branch_no != null && @up_branch_no.length() > 0">
			    and instr(','||'@up_branch_no'||',',','||up_branch_no||',')>0
			</if>

            <if condition="@busin_type != null && @busin_type.length() > 0">
			    and instr(','||'@busin_type'||',',','||busin_type||',')>0
			</if>

			<if condition="@label != null && @label.length() > 0">
			    and instr(','||'@label'||',',','||LABEL_OPTIONS||',')>0
			</if>

			<if condition="@activity_name != null && @activity_name.length() > 0">
			    AND ACTIVITY_NAME = '@activity_name'
			</if>

			<if condition="@channel_code != null && @channel_code.length() > 0">
			    AND CHANNEL_CODE = '@channel_code'
			</if>

			<if condition="@channel_name != null && @channel_name.length() > 0">
			    AND CHANNEL_NAME = '@channel_name'
			</if>

			<if condition="@marketing_team != null && @marketing_team.length() > 0">
			    AND MARKETING_TEAM = '@marketing_team'
			</if>

        ]]></Content>
    </Func>

    <Func ID="MONITOR10100_total" MODULE_NAME="回访业务量报表（申请维度|分公司|总数）" TYPE="QUERY" DESC="回访业务量报表（申请维度|分公司|总数）">
        <InParam>
            <Param Code="apply_start_time" Name="回访申请创建的开始时间"/>
            <Param Code="apply_end_time" Name="回访申请创建的结束时间"/>
            <Param Code="apply_time" Name="申请时间"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="busin_type" Name="申请类型"/>
            <Param Code="status" Name="申请状态"/>
            <Param Code="label" Name="标签"/>
            <Param Code="activity_name" Name="活动名称"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="channel_name" Name="渠道名称"/>
            <Param Code="marketing_team" Name="营销团队"/>
        </InParam>
        <Content><![CDATA[
        SELECT

            COUNT(DISTINCT up_branch_no) AS total
        FROM
            CRH_FUS.FOLLOWUPAPPLY
        WHERE
            CREATE_DATETIME >= TO_DATE('@apply_start_time', 'YYYY-MM-DD HH24:MI:SS')
            AND CREATE_DATETIME <= TO_DATE('@apply_end_time', 'YYYY-MM-DD HH24:MI:SS')

            <if condition="@up_branch_no != null && @up_branch_no.length() > 0">
			    and instr(','||'@up_branch_no'||',',','||up_branch_no||',')>0
			</if>

            <if condition="@busin_type != null && @busin_type.length() > 0">
			    and instr(','||'@busin_type'||',',','||busin_type||',')>0
			</if>

			<if condition="@label != null && @label.length() > 0">
			    and instr(','||'@label'||',',','||LABEL_OPTIONS||',')>0
			</if>

			<if condition="@activity_name != null && @activity_name.length() > 0">
			    AND ACTIVITY_NAME = '@activity_name'
			</if>

			<if condition="@channel_code != null && @channel_code.length() > 0">
			    AND CHANNEL_CODE = '@channel_code'
			</if>

			<if condition="@channel_name != null && @channel_name.length() > 0">
			    AND CHANNEL_NAME = '@channel_name'
			</if>

			<if condition="@marketing_team != null && @marketing_team.length() > 0">
			    AND MARKETING_TEAM = '@marketing_team'
			</if>
        ]]></Content>
    </Func>

    <Func ID="MONITOR10101_page" MODULE_NAME="回访业务量报表（申请维度|营业部|分页）" TYPE="QUERY" DESC="回访业务量报表（申请维度|营业部|分页）">
        <InParam>
            <Param Code="apply_start_time" Name="回访申请创建的开始时间"/>
            <Param Code="apply_end_time" Name="回访申请创建的结束时间"/>
            <Param Code="apply_time" Name="申请时间"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="busin_type" Name="申请类型"/>
            <Param Code="status" Name="申请状态"/>
            <Param Code="label" Name="标签"/>
            <Param Code="activity_name" Name="活动名称"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="channel_name" Name="渠道名称"/>
            <Param Code="marketing_team" Name="营销团队"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
        with page_data as (SELECT
            branch_no AS branch_no,
            COUNT(*) AS application_count,
            SUM(CASE WHEN status in ('7','8','9','10','11','12') THEN 1 ELSE 0 END) AS completed_count,
            ROUND(SUM(CASE WHEN status in ('7','8','9','10','11','12') THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS completion_rate,

            SUM(CASE WHEN status = '7' THEN 1 ELSE 0 END) AS approved_count,
            ROUND(SUM(CASE WHEN status = '7' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS approval_rate,

            SUM(CASE WHEN status = '8' THEN 1 ELSE 0 END) AS nonCompliant_count,
            ROUND(SUM(CASE WHEN status = '8' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS nonCompliance_rate,

            SUM(CASE WHEN status = '9' THEN 1 ELSE 0 END) AS refuse_follow_up_count,
            ROUND(SUM(CASE WHEN status = '9' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS refuse_follow_up_rate,

            SUM(CASE WHEN status = '11' THEN 1 ELSE 0 END) AS strategy_execution_Count,
            ROUND(SUM(CASE WHEN status = '11' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS strategy_execution_rate,

            SUM(CASE WHEN status = '10' THEN 1 ELSE 0 END) AS manual_end_count,
            ROUND(SUM(CASE WHEN status = '10' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS manual_end_rate,

            SUM(CASE WHEN LAST_QNAIRE_COMPLETION = 1 THEN 1 ELSE 0 END) AS questionnaire_completed_count,
            ROUND(SUM(CASE WHEN LAST_QNAIRE_COMPLETION = 1 THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS questionnaire_completed_rate
        FROM
            CRH_FUS.FOLLOWUPAPPLY
        WHERE
            CREATE_DATETIME >= TO_DATE('@apply_start_time', 'YYYY-MM-DD HH24:MI:SS')
            AND CREATE_DATETIME <= TO_DATE('@apply_end_time', 'YYYY-MM-DD HH24:MI:SS')

            <if condition="@branch_no != null && @branch_no.length() > 0">
			    and instr(','||'@branch_no'||',',','||branch_no||',')>0
			</if>

            <if condition="@busin_type != null && @busin_type.length() > 0">
			    and instr(','||'@busin_type'||',',','||busin_type||',')>0
			</if>

			<if condition="@label != null && @label.length() > 0">
			    and instr(','||'@label'||',',','||LABEL_OPTIONS||',')>0
			</if>

			<if condition="@activity_name != null && @activity_name.length() > 0">
			    AND ACTIVITY_NAME = '@activity_name'
			</if>

			<if condition="@channel_code != null && @channel_code.length() > 0">
			    AND CHANNEL_CODE = '@channel_code'
			</if>

			<if condition="@channel_name != null && @channel_name.length() > 0">
			    AND CHANNEL_NAME = '@channel_name'
			</if>

			<if condition="@marketing_team != null && @marketing_team.length() > 0">
			    AND MARKETING_TEAM = '@marketing_team'
			</if>
        GROUP BY
            branch_no
        ORDER BY
            application_count DESC
        ),

        RownumData AS (
				SELECT
					b.*,
					rownum as rn
				FROM
					page_data b
			)

			SELECT
				*
			FROM RownumData r
			<if condition="@page_num != null && @page_size != null">
				WHERE r.rn BETWEEN ( @page_num - 1 ) * @page_size + 1 AND @page_num * @page_size
			</if>
        ]]></Content>
    </Func>

    <Func ID="MONITOR10101_sum" MODULE_NAME="回访业务量报表（申请维度|营业部|合计）" TYPE="QUERY" DESC="回访业务量报表（申请维度|营业部|合计）">
        <InParam>
            <Param Code="apply_start_time" Name="回访申请创建的开始时间"/>
            <Param Code="apply_end_time" Name="回访申请创建的结束时间"/>
            <Param Code="apply_time" Name="申请时间"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="busin_type" Name="申请类型"/>
            <Param Code="status" Name="申请状态"/>
            <Param Code="label" Name="标签"/>
            <Param Code="activity_name" Name="活动名称"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="channel_name" Name="渠道名称"/>
            <Param Code="marketing_team" Name="营销团队"/>
        </InParam>
        <Content><![CDATA[
        SELECT

            '合计' as branch_no,
            COUNT(*) AS application_count,

            SUM(CASE WHEN status in ('7','8','9','10','11','12') THEN 1 ELSE 0 END) AS completed_count,
            ROUND(SUM(CASE WHEN status in ('7','8','9','10','11','12') THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS completion_rate,

            SUM(CASE WHEN status = '7' THEN 1 ELSE 0 END) AS approved_count,
            ROUND(SUM(CASE WHEN status = '7' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS approval_rate,

            SUM(CASE WHEN status = '8' THEN 1 ELSE 0 END) AS nonCompliant_count,
            ROUND(SUM(CASE WHEN status = '8' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS nonCompliance_rate,

            SUM(CASE WHEN status = '9' THEN 1 ELSE 0 END) AS refuse_follow_up_count,
            ROUND(SUM(CASE WHEN status = '9' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS refuse_follow_up_rate,

            SUM(CASE WHEN status = '11' THEN 1 ELSE 0 END) AS strategy_execution_Count,
            ROUND(SUM(CASE WHEN status = '11' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS strategy_execution_rate,

            SUM(CASE WHEN status = '10' THEN 1 ELSE 0 END) AS manual_end_count,
            ROUND(SUM(CASE WHEN status = '10' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS manual_end_rate,

            SUM(CASE WHEN LAST_QNAIRE_COMPLETION = 1 THEN 1 ELSE 0 END) AS questionnaire_completed_count,
            ROUND(SUM(CASE WHEN LAST_QNAIRE_COMPLETION = 1 THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS questionnaire_completed_rate
        FROM
            CRH_FUS.FOLLOWUPAPPLY
        WHERE
            CREATE_DATETIME >= TO_DATE('@apply_start_time', 'YYYY-MM-DD HH24:MI:SS')
            AND CREATE_DATETIME <= TO_DATE('@apply_end_time', 'YYYY-MM-DD HH24:MI:SS')

            <if condition="@branch_no != null && @branch_no.length() > 0">
			    and instr(','||'@branch_no'||',',','||branch_no||',')>0
			</if>

            <if condition="@busin_type != null && @busin_type.length() > 0">
			    and instr(','||'@busin_type'||',',','||busin_type||',')>0
			</if>

			<if condition="@label != null && @label.length() > 0">
			    and instr(','||'@label'||',',','||LABEL_OPTIONS||',')>0
			</if>

			<if condition="@activity_name != null && @activity_name.length() > 0">
			    AND ACTIVITY_NAME = '@activity_name'
			</if>

			<if condition="@channel_code != null && @channel_code.length() > 0">
			    AND CHANNEL_CODE = '@channel_code'
			</if>

			<if condition="@channel_name != null && @channel_name.length() > 0">
			    AND CHANNEL_NAME = '@channel_name'
			</if>

			<if condition="@marketing_team != null && @marketing_team.length() > 0">
			    AND MARKETING_TEAM = '@marketing_team'
			</if>

        ]]></Content>
    </Func>

    <Func ID="MONITOR10101_total" MODULE_NAME="回访业务量报表（申请维度|营业部|总数）" TYPE="QUERY" DESC="回访业务量报表（申请维度|营业部|总数）">
        <InParam>
            <Param Code="apply_start_time" Name="回访申请创建的开始时间"/>
            <Param Code="apply_end_time" Name="回访申请创建的结束时间"/>
            <Param Code="apply_time" Name="申请时间"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="busin_type" Name="申请类型"/>
            <Param Code="status" Name="申请状态"/>
            <Param Code="label" Name="标签"/>
            <Param Code="activity_name" Name="活动名称"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="channel_name" Name="渠道名称"/>
            <Param Code="marketing_team" Name="营销团队"/>
        </InParam>
        <Content><![CDATA[
        SELECT
            COUNT(DISTINCT branch_no) AS total
        FROM
            CRH_FUS.FOLLOWUPAPPLY
        WHERE
            CREATE_DATETIME >= TO_DATE('@apply_start_time', 'YYYY-MM-DD HH24:MI:SS')
            AND CREATE_DATETIME <= TO_DATE('@apply_end_time', 'YYYY-MM-DD HH24:MI:SS')

            <if condition="@branch_no != null && @branch_no.length() > 0">
			    and instr(','||'@branch_no'||',',','||branch_no||',')>0
			</if>

            <if condition="@busin_type != null && @busin_type.length() > 0">
			    and instr(','||'@busin_type'||',',','||busin_type||',')>0
			</if>

			<if condition="@label != null && @label.length() > 0">
			    and instr(','||'@label'||',',','||LABEL_OPTIONS||',')>0
			</if>

			<if condition="@activity_name != null && @activity_name.length() > 0">
			    AND ACTIVITY_NAME = '@activity_name'
			</if>

			<if condition="@channel_code != null && @channel_code.length() > 0">
			    AND CHANNEL_CODE = '@channel_code'
			</if>

			<if condition="@channel_name != null && @channel_name.length() > 0">
			    AND CHANNEL_NAME = '@channel_name'
			</if>

			<if condition="@marketing_team != null && @marketing_team.length() > 0">
			    AND MARKETING_TEAM = '@marketing_team'
			</if>
        ]]></Content>
    </Func>

    <Func ID="MONITOR10102_page" MODULE_NAME="回访外呼响应报表（申请维度|分公司|分页）" TYPE="QUERY" DESC="回访外呼响应报表（申请维度|分公司|分页）">
        <InParam>
            <Param Code="apply_start_time" Name="回访申请创建的开始时间"/>
            <Param Code="apply_end_time" Name="回访申请创建的结束时间"/>
            <Param Code="apply_time" Name="申请时间"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="busin_type" Name="申请类型"/>
            <Param Code="status" Name="申请状态"/>
            <Param Code="label" Name="标签"/>
            <Param Code="activity_name" Name="活动名称"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="channel_name" Name="渠道名称"/>
            <Param Code="marketing_team" Name="营销团队"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content>
            <![CDATA[
                with page_data as (SELECT
                    up_branch_no AS up_branch_no,
                    NVL(ROUND(AVG(FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 * 60,0), 0) AS avg_minutes_diff,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 0 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 < 3
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS zero_three_rate,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 3 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 < 10
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS three_ten_rate,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 10 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 < 15
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS ten_fifteen_rate,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 15 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 < 30
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS fifteen_thirty_rate,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 30 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 < 45
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS thirty_fortyFive_rate,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 45 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 < 60
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS fortyFive_sixty_rate,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 60
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS over_sixty_rate
                FROM
                    CRH_FUS.FOLLOWUPAPPLY
                WHERE
                    CREATE_DATETIME >= TO_DATE('@apply_start_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND CREATE_DATETIME <= TO_DATE('@apply_end_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND FIRST_DEAL_TIME is not null 

                    <if condition="@up_branch_no != null && @up_branch_no.length() > 0">
                        and instr(','||'@up_branch_no'||',',','||up_branch_no||',')>0
                    </if>

                    <if condition="@busin_type != null && @busin_type.length() > 0">
                        and instr(','||'@busin_type'||',',','||busin_type||',')>0
                    </if>

                    <if condition="@label != null && @label.length() > 0">
                        and instr(','||'@label'||',',','||LABEL_OPTIONS||',')>0
                    </if>

                    <if condition="@activity_name != null && @activity_name.length() > 0">
                        AND ACTIVITY_NAME = '@activity_name'
                    </if>

                    <if condition="@channel_code != null && @channel_code.length() > 0">
                        AND CHANNEL_CODE = '@channel_code'
                    </if>

                    <if condition="@channel_name != null && @channel_name.length() > 0">
                        AND CHANNEL_NAME = '@channel_name'
                    </if>

                    <if condition="@marketing_team != null && @marketing_team.length() > 0">
                        AND MARKETING_TEAM = '@marketing_team'
                    </if>
                GROUP BY
                    up_branch_no
                ),

            RownumData AS (
				SELECT
					b.*,
					rownum as rn
				FROM
					page_data b
			)

			SELECT
				*
			FROM RownumData r
			<if condition="@page_num != null && @page_size != null">
				WHERE r.rn BETWEEN ( @page_num - 1 ) * @page_size + 1 AND @page_num * @page_size
			</if>
            ]]>
        </Content>
    </Func>

    <Func ID="MONITOR10102_sum" MODULE_NAME="回访外呼响应报表（申请维度|分公司|合计）" TYPE="QUERY" DESC="回访外呼响应报表（申请维度|分公司|合计）">
        <InParam>
            <Param Code="apply_start_time" Name="回访申请创建的开始时间"/>
            <Param Code="apply_end_time" Name="回访申请创建的结束时间"/>
            <Param Code="apply_time" Name="申请时间"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="busin_type" Name="申请类型"/>
            <Param Code="status" Name="申请状态"/>
            <Param Code="label" Name="标签"/>
            <Param Code="activity_name" Name="活动名称"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="channel_name" Name="渠道名称"/>
            <Param Code="marketing_team" Name="营销团队"/>
        </InParam>
        <Content>
            <![CDATA[
                SELECT
                    '合计' AS up_branch_no,
                    NVL(ROUND(AVG(FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 * 60,0), 0) AS avg_minutes_diff,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 0 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 < 3
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS zero_three_rate,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 3 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 < 10
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS three_ten_rate,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 10 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 < 15
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS ten_fifteen_rate,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 15 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 < 30
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS fifteen_thirty_rate,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 30 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 < 45
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS thirty_fortyFive_rate,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 45 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 < 60
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS fortyFive_sixty_rate,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 60
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS over_sixty_rate
                FROM
                    CRH_FUS.FOLLOWUPAPPLY
                WHERE
                    CREATE_DATETIME >= TO_DATE('@apply_start_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND CREATE_DATETIME <= TO_DATE('@apply_end_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND FIRST_DEAL_TIME is not null 

                    <if condition="@up_branch_no != null && @up_branch_no.length() > 0">
                        and instr(','||'@up_branch_no'||',',','||up_branch_no||',')>0
                    </if>

                    <if condition="@busin_type != null && @busin_type.length() > 0">
                        and instr(','||'@busin_type'||',',','||busin_type||',')>0
                    </if>

                    <if condition="@label != null && @label.length() > 0">
                        and instr(','||'@label'||',',','||LABEL_OPTIONS||',')>0
                    </if>

                    <if condition="@activity_name != null && @activity_name.length() > 0">
                        AND ACTIVITY_NAME = '@activity_name'
                    </if>

                    <if condition="@channel_code != null && @channel_code.length() > 0">
                        AND CHANNEL_CODE = '@channel_code'
                    </if>

                    <if condition="@channel_name != null && @channel_name.length() > 0">
                        AND CHANNEL_NAME = '@channel_name'
                    </if>

                    <if condition="@marketing_team != null && @marketing_team.length() > 0">
                        AND MARKETING_TEAM = '@marketing_team'
                    </if>
            ]]>
        </Content>
    </Func>

    <Func ID="MONITOR10102_total" MODULE_NAME="回访外呼响应报表（申请维度|分公司|总数）" TYPE="QUERY" DESC="回访外呼响应报表（申请维度|分公司|总数）">
        <InParam>
            <Param Code="apply_start_time" Name="回访申请创建的开始时间"/>
            <Param Code="apply_end_time" Name="回访申请创建的结束时间"/>
            <Param Code="apply_time" Name="申请时间"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="busin_type" Name="申请类型"/>
            <Param Code="status" Name="申请状态"/>
            <Param Code="label" Name="标签"/>
            <Param Code="activity_name" Name="活动名称"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="channel_name" Name="渠道名称"/>
            <Param Code="marketing_team" Name="营销团队"/>
        </InParam>
        <Content>
            <![CDATA[
                SELECT
                    COUNT(DISTINCT up_branch_no) AS total
                FROM
                    CRH_FUS.FOLLOWUPAPPLY
                WHERE
                    CREATE_DATETIME >= TO_DATE('@apply_start_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND CREATE_DATETIME <= TO_DATE('@apply_end_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND FIRST_DEAL_TIME is not null 

                    <if condition="@up_branch_no != null && @up_branch_no.length() > 0">
                        and instr(','||'@up_branch_no'||',',','||up_branch_no||',')>0
                    </if>

                    <if condition="@busin_type != null && @busin_type.length() > 0">
                        and instr(','||'@busin_type'||',',','||busin_type||',')>0
                    </if>

                    <if condition="@label != null && @label.length() > 0">
                        and instr(','||'@label'||',',','||LABEL_OPTIONS||',')>0
                    </if>

                    <if condition="@activity_name != null && @activity_name.length() > 0">
                        AND ACTIVITY_NAME = '@activity_name'
                    </if>

                    <if condition="@channel_code != null && @channel_code.length() > 0">
                        AND CHANNEL_CODE = '@channel_code'
                    </if>

                    <if condition="@channel_name != null && @channel_name.length() > 0">
                        AND CHANNEL_NAME = '@channel_name'
                    </if>

                    <if condition="@marketing_team != null && @marketing_team.length() > 0">
                        AND MARKETING_TEAM = '@marketing_team'
                    </if>
            ]]>
        </Content>
    </Func>


    <Func ID="MONITOR10103_page" MODULE_NAME="回访外呼响应报表（申请维度|营业部|分页）" TYPE="QUERY" DESC="回访外呼响应报表（申请维度|营业部|分页）">
        <InParam>
            <Param Code="apply_start_time" Name="回访申请创建的开始时间"/>
            <Param Code="apply_end_time" Name="回访申请创建的结束时间"/>
            <Param Code="apply_time" Name="申请时间"/>
            <Param Code="branch_no" Name="营业部"/>
            <Param Code="busin_type" Name="申请类型"/>
            <Param Code="status" Name="申请状态"/>
            <Param Code="label" Name="标签"/>
            <Param Code="activity_name" Name="活动名称"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="channel_name" Name="渠道名称"/>
            <Param Code="marketing_team" Name="营销团队"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content>
            <![CDATA[
            with page_data as(
                SELECT
                    branch_no AS branch_no,
                    NVL(ROUND(AVG(FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 * 60,0), 0) AS avg_minutes_diff,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 0 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 < 3
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS zero_three_rate,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 3 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 < 10
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS three_ten_rate,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 10 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 < 15
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS ten_fifteen_rate,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 15 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 < 30
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS fifteen_thirty_rate,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 30 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 < 45
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS thirty_fortyFive_rate,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 45 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 < 60
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS fortyFive_sixty_rate,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 60
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS over_sixty_rate
                FROM
                    CRH_FUS.FOLLOWUPAPPLY
                WHERE
                    CREATE_DATETIME >= TO_DATE('@apply_start_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND CREATE_DATETIME <= TO_DATE('@apply_end_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND FIRST_DEAL_TIME is not null 

                    <if condition="@branch_no != null && @branch_no.length() > 0">
                        and instr(','||'@branch_no'||',',','||branch_no||',')>0
                    </if>

                    <if condition="@busin_type != null && @busin_type.length() > 0">
                        and instr(','||'@busin_type'||',',','||busin_type||',')>0
                    </if>

                    <if condition="@label != null && @label.length() > 0">
                        and instr(','||'@label'||',',','||LABEL_OPTIONS||',')>0
                    </if>

                    <if condition="@activity_name != null && @activity_name.length() > 0">
                        AND ACTIVITY_NAME = '@activity_name'
                    </if>

                    <if condition="@channel_code != null && @channel_code.length() > 0">
                        AND CHANNEL_CODE = '@channel_code'
                    </if>

                    <if condition="@channel_name != null && @channel_name.length() > 0">
                        AND CHANNEL_NAME = '@channel_name'
                    </if>

                    <if condition="@marketing_team != null && @marketing_team.length() > 0">
                        AND MARKETING_TEAM = '@marketing_team'
                    </if>
                GROUP BY
                    branch_no
                ),
            RownumData AS (
				SELECT
					b.*,
					rownum as rn
				FROM
					page_data b
			)
			SELECT
				*
			FROM RownumData r
			<if condition="@page_num != null && @page_size != null">
				WHERE r.rn BETWEEN ( @page_num - 1 ) * @page_size + 1 AND @page_num * @page_size
			</if>
            ]]>
        </Content>
    </Func>

    <Func ID="MONITOR10103_sum" MODULE_NAME="回访外呼响应报表（申请维度|营业部|合计）" TYPE="QUERY" DESC="回访外呼响应报表（申请维度|营业部|合计）">
        <InParam>
            <Param Code="apply_start_time" Name="回访申请创建的开始时间"/>
            <Param Code="apply_end_time" Name="回访申请创建的结束时间"/>
            <Param Code="apply_time" Name="申请时间"/>
            <Param Code="branch_no" Name="营业部"/>
            <Param Code="busin_type" Name="申请类型"/>
            <Param Code="status" Name="申请状态"/>
            <Param Code="label" Name="标签"/>
            <Param Code="activity_name" Name="活动名称"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="channel_name" Name="渠道名称"/>
            <Param Code="marketing_team" Name="营销团队"/>
        </InParam>
        <Content>
            <![CDATA[
                SELECT
                    '合计' AS branch_no,
                    NVL(ROUND(AVG(FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 * 60,0), 0) AS avg_minutes_diff,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 0 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 < 3
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS zero_three_rate,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 3 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 < 10
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS three_ten_rate,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 10 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 < 15
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS ten_fifteen_rate,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 15 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 < 30
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS fifteen_thirty_rate,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 30 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 < 45
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS thirty_fortyFive_rate,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 45 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 < 60
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS fortyFive_sixty_rate,

                    ROUND(SUM(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 >= 60
                    THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS over_sixty_rate
                FROM
                    CRH_FUS.FOLLOWUPAPPLY
                WHERE
                    CREATE_DATETIME >= TO_DATE('@apply_start_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND CREATE_DATETIME <= TO_DATE('@apply_end_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND FIRST_DEAL_TIME is not null 

                    <if condition="@branch_no != null && @branch_no.length() > 0">
                        and instr(','||'@branch_no'||',',','||branch_no||',')>0
                    </if>

                    <if condition="@busin_type != null && @busin_type.length() > 0">
                        and instr(','||'@busin_type'||',',','||busin_type||',')>0
                    </if>

                    <if condition="@label != null && @label.length() > 0">
                        and instr(','||'@label'||',',','||LABEL_OPTIONS||',')>0
                    </if>

                    <if condition="@activity_name != null && @activity_name.length() > 0">
                        AND ACTIVITY_NAME = '@activity_name'
                    </if>

                    <if condition="@channel_code != null && @channel_code.length() > 0">
                        AND CHANNEL_CODE = '@channel_code'
                    </if>

                    <if condition="@channel_name != null && @channel_name.length() > 0">
                        AND CHANNEL_NAME = '@channel_name'
                    </if>

                    <if condition="@marketing_team != null && @marketing_team.length() > 0">
                        AND MARKETING_TEAM = '@marketing_team'
                    </if>
            ]]>
        </Content>
    </Func>

    <Func ID="MONITOR10103_total" MODULE_NAME="回访外呼响应报表（申请维度|营业部|总数）" TYPE="QUERY" DESC="回访外呼响应报表（申请维度|营业部|总数）">
        <InParam>
            <Param Code="apply_start_time" Name="回访申请创建的开始时间"/>
            <Param Code="apply_end_time" Name="回访申请创建的结束时间"/>
            <Param Code="apply_time" Name="申请时间"/>
            <Param Code="branch_no" Name="营业部"/>
            <Param Code="busin_type" Name="申请类型"/>
            <Param Code="status" Name="申请状态"/>
            <Param Code="label" Name="标签"/>
            <Param Code="activity_name" Name="活动名称"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="channel_name" Name="渠道名称"/>
            <Param Code="marketing_team" Name="营销团队"/>
        </InParam>
        <Content>
            <![CDATA[
                SELECT
                    COUNT(DISTINCT branch_no) AS total
                FROM
                    CRH_FUS.FOLLOWUPAPPLY
                WHERE
                    CREATE_DATETIME >= TO_DATE('@apply_start_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND CREATE_DATETIME <= TO_DATE('@apply_end_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND FIRST_DEAL_TIME is not null 

                    <if condition="@branch_no != null && @branch_no.length() > 0">
                        and instr(','||'@branch_no'||',',','||branch_no||',')>0
                    </if>

                    <if condition="@busin_type != null && @busin_type.length() > 0">
                        and instr(','||'@busin_type'||',',','||busin_type||',')>0
                    </if>

                    <if condition="@label != null && @label.length() > 0">
			            and instr(','||'@label'||',',','||LABEL_OPTIONS||',')>0

                    </if>

                    <if condition="@activity_name != null && @activity_name.length() > 0">
                        AND ACTIVITY_NAME = '@activity_name'
                    </if>

                    <if condition="@channel_code != null && @channel_code.length() > 0">
                        AND CHANNEL_CODE = '@channel_code'
                    </if>

                    <if condition="@channel_name != null && @channel_name.length() > 0">
                        AND CHANNEL_NAME = '@channel_name'
                    </if>

                    <if condition="@marketing_team != null && @marketing_team.length() > 0">
                        AND MARKETING_TEAM = '@marketing_team'
                    </if>
            ]]>
        </Content>
    </Func>

    <Func ID="MONITOR10104_page" MODULE_NAME="一级驳回原因分布表（分页）" TYPE="QUERY" DESC="一级驳回原因分布表（分页）">
        <InParam>
            <Param Code="start_time" Name="回访申请创建的开始时间"/>
            <Param Code="end_time" Name="回访申请创建的结束时间"/>
            <Param Code="apply_time" Name="申请时间"/>
            <Param Code="branch_no" Name="营业部"/>
            <Param Code="activity_name" Name="活动名称"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="marketing_team" Name="营销团队"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>

        <Content>
            <![CDATA[
                with page_data as(
                    select
                        CASE WHEN REASON_GROUP = ' ' THEN '自定义原因' ELSE REASON_GROUP END AS reject_reason,
                        COUNT(*) as num,
                        ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as rate
                        from CRH_AC.TASKREASONRECORD
                        WHERE
                            TASK_APPLY_DATETIME >= TO_DATE('@start_time', 'YYYY-MM-DD HH24:MI:SS')
                            AND TASK_APPLY_DATETIME <= TO_DATE('@end_time', 'YYYY-MM-DD HH24:MI:SS')

                            <if condition="@branch_no != null && @branch_no.length() > 0">
                                and BRANCH_NO = '@branch_no'
                            </if>
                            <if condition="@activity_name != null && @activity_name.length() > 0">
                                AND ACTIVITY_NAME = '@activity_name'
                            </if>

                            <if condition="@channel_code != null && @channel_code.length() > 0">
                                AND CHANNEL_CODE = '@channel_code'
                            </if>

                            <if condition="@marketing_team != null && @marketing_team.length() > 0">
                                AND MARKETING_TEAM = '@marketing_team'
                            </if>
                        GROUP BY REASON_GROUP
                        order by num desc
                        ),
                RownumData AS (
                                SELECT
                                    b.*,
                                    rownum as rn
                                FROM
                                    page_data b
                            )
                SELECT
                    *
                FROM RownumData r
                <if condition="@page_num != null && @page_size != null">
                    WHERE r.rn BETWEEN ( @page_num - 1 ) * @page_size + 1 AND @page_num * @page_size
                </if>
            ]]>
        </Content>
    </Func>

    <Func ID="MONITOR10104_total" MODULE_NAME="一级驳回原因分布表（总数）" TYPE="QUERY" DESC="一级驳回原因分布表（总数）">
        <InParam>
            <Param Code="start_time" Name="回访申请创建的开始时间"/>
            <Param Code="end_time" Name="回访申请创建的结束时间"/>
            <Param Code="apply_time" Name="申请时间"/>
            <Param Code="branch_no" Name="营业部"/>
            <Param Code="activity_name" Name="活动名称"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="marketing_team" Name="营销团队"/>
        </InParam>

        <Content>
            <![CDATA[
                SELECT
                    COUNT(DISTINCT REASON_GROUP) AS total
                from CRH_AC.TASKREASONRECORD
                WHERE
                    TASK_APPLY_DATETIME >= TO_DATE('@start_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND TASK_APPLY_DATETIME <= TO_DATE('@end_time', 'YYYY-MM-DD HH24:MI:SS')

                    <if condition="@branch_no != null && @branch_no.length() > 0">
                        and BRANCH_NO = '@branch_no'
                    </if>

                    <if condition="@activity_name != null && @activity_name.length() > 0">
                        AND ACTIVITY_NAME = '@activity_name'
                    </if>

                    <if condition="@channel_code != null && @channel_code.length() > 0">
                        AND CHANNEL_CODE = '@channel_code'
                    </if>

                    <if condition="@marketing_team != null && @marketing_team.length() > 0">
                        AND MARKETING_TEAM = '@marketing_team'
                    </if>
            ]]>
        </Content>
    </Func>


    <Func ID="MONITOR10105" MODULE_NAME="一级驳回原因分布表（总览）" TYPE="QUERY" DESC="一级驳回原因分布表（总览）">
        <InParam>
            <Param Code="start_time" Name="回访申请创建的开始时间"/>
            <Param Code="end_time" Name="回访申请创建的结束时间"/>
            <Param Code="apply_time" Name="申请时间"/>
            <Param Code="branch_no" Name="营业部"/>
            <Param Code="activity_name" Name="活动名称"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="marketing_team" Name="营销团队"/>
        </InParam>

        <Content>
            <![CDATA[
                select
                    CASE WHEN REASON_GROUP = ' ' THEN '自定义原因' ELSE REASON_GROUP END AS reject_reason,
                    COUNT(*) as num,
                    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as rate,
                    SUM(COUNT(*)) OVER () as total_num
                    from CRH_AC.TASKREASONRECORD
                    WHERE
                        TASK_APPLY_DATETIME >= TO_DATE('@start_time', 'YYYY-MM-DD HH24:MI:SS')
                        AND TASK_APPLY_DATETIME <= TO_DATE('@end_time', 'YYYY-MM-DD HH24:MI:SS')

                        <if condition="@branch_no != null && @branch_no.length() > 0">
                            and BRANCH_NO = '@branch_no'
                        </if>
                        <if condition="@activity_name != null && @activity_name.length() > 0">
                            AND ACTIVITY_NAME = '@activity_name'
                        </if>

                        <if condition="@channel_code != null && @channel_code.length() > 0">
                            AND CHANNEL_CODE = '@channel_code'
                        </if>

                        <if condition="@marketing_team != null && @marketing_team.length() > 0">
                            AND MARKETING_TEAM = '@marketing_team'
                        </if>
                    GROUP BY REASON_GROUP
            ]]>
        </Content>
    </Func>



    <Func ID="MONITOR10106_page" MODULE_NAME="二级驳回原因分布表（分页）" TYPE="QUERY" DESC="二级驳回原因分布表（分页）">
        <InParam>
            <Param Code="start_time" Name="回访申请创建的开始时间"/>
            <Param Code="end_time" Name="回访申请创建的结束时间"/>
            <Param Code="apply_time" Name="申请时间"/>
            <Param Code="branch_no" Name="营业部"/>
            <Param Code="activity_name" Name="活动名称"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="marketing_team" Name="营销团队"/>
            <Param Code="reject_reason" Name="一级原因"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>

        <Content>
            <![CDATA[
                with page_data as(
                    select
                        reason_desc as reject_reason,
                        COUNT(*) as num,
                        ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as rate
                        from CRH_AC.TASKREASONRECORD
                        WHERE
                            TASK_APPLY_DATETIME >= TO_DATE('@start_time', 'YYYY-MM-DD HH24:MI:SS')
                            AND TASK_APPLY_DATETIME <= TO_DATE('@end_time', 'YYYY-MM-DD HH24:MI:SS')

                            <if condition="@reject_reason != null && @reject_reason.length() > 0">
                                and REASON_GROUP = '@reject_reason'
                            </if>

                            <if condition="@branch_no != null && @branch_no.length() > 0">
                                and BRANCH_NO = '@branch_no'
                            </if>
                            <if condition="@activity_name != null && @activity_name.length() > 0">
                                AND ACTIVITY_NAME = '@activity_name'
                            </if>

                            <if condition="@channel_code != null && @channel_code.length() > 0">
                                AND CHANNEL_CODE = '@channel_code'
                            </if>

                            <if condition="@marketing_team != null && @marketing_team.length() > 0">
                                AND MARKETING_TEAM = '@marketing_team'
                            </if>
                        GROUP BY reason_desc
                        order by num desc
                        ),

                RownumData AS (
                                SELECT
                                    b.*,
                                    rownum as rn
                                FROM
                                    page_data b
                            )

                SELECT
                    *
                FROM RownumData r
                <if condition="@page_num != null && @page_size != null">
                    WHERE r.rn BETWEEN ( @page_num - 1 ) * @page_size + 1 AND @page_num * @page_size
                </if>
            ]]>
        </Content>
    </Func>

    <Func ID="MONITOR10106_total" MODULE_NAME="二级驳回原因分布表（总数）" TYPE="QUERY" DESC="二级驳回原因分布表（总数）">
        <InParam>
            <Param Code="start_time" Name="回访申请创建的开始时间"/>
            <Param Code="end_time" Name="回访申请创建的结束时间"/>
            <Param Code="apply_time" Name="申请时间"/>
            <Param Code="branch_no" Name="营业部"/>
            <Param Code="activity_name" Name="活动名称"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="marketing_team" Name="营销团队"/>
            <Param Code="reject_reason" Name="一级原因"/>
        </InParam>

        <Content>
            <![CDATA[

                SELECT
                    COUNT(DISTINCT reason_desc) AS total
                from CRH_AC.TASKREASONRECORD
                WHERE
                    TASK_APPLY_DATETIME >= TO_DATE('@start_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND TASK_APPLY_DATETIME <= TO_DATE('@end_time', 'YYYY-MM-DD HH24:MI:SS')

                    <if condition="@reject_reason != null && @reject_reason.length() > 0">
                        and REASON_GROUP = '@reject_reason'
                    </if>

                    <if condition="@branch_no != null && @branch_no.length() > 0">
                        and BRANCH_NO = '@branch_no'
                    </if>

                    <if condition="@activity_name != null && @activity_name.length() > 0">
                        AND ACTIVITY_NAME = '@activity_name'
                    </if>

                    <if condition="@channel_code != null && @channel_code.length() > 0">
                        AND CHANNEL_CODE = '@channel_code'
                    </if>

                    <if condition="@marketing_team != null && @marketing_team.length() > 0">
                        AND MARKETING_TEAM = '@marketing_team'
                    </if>
            ]]>
        </Content>
    </Func>

    <Func ID="MONITOR10107" MODULE_NAME="二级驳回原因分布表（总览）" TYPE="QUERY" DESC="二级驳回原因分布表（总览）">
        <InParam>
            <Param Code="start_time" Name="回访申请创建的开始时间"/>
            <Param Code="end_time" Name="回访申请创建的结束时间"/>
            <Param Code="apply_time" Name="申请时间"/>
            <Param Code="branch_no" Name="营业部"/>
            <Param Code="activity_name" Name="活动名称"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="marketing_team" Name="营销团队"/>
            <Param Code="reject_reason" Name="一级原因"/>
        </InParam>

        <Content>
            <![CDATA[
                select
                    reason_desc as reject_reason,
                    COUNT(*) as num,
                    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER (), 2) as rate,
                    SUM(COUNT(*)) OVER () as total_num
                    from CRH_AC.TASKREASONRECORD
                    WHERE
                        TASK_APPLY_DATETIME >= TO_DATE('@start_time', 'YYYY-MM-DD HH24:MI:SS')
                        AND TASK_APPLY_DATETIME <= TO_DATE('@end_time', 'YYYY-MM-DD HH24:MI:SS')

                        <if condition="@reject_reason != null && @reject_reason.length() > 0">
                            and REASON_GROUP = '@reject_reason'
                        </if>

                        <if condition="@branch_no != null && @branch_no.length() > 0">
                            and BRANCH_NO = '@branch_no'
                        </if>
                        <if condition="@activity_name != null && @activity_name.length() > 0">
                            AND ACTIVITY_NAME = '@activity_name'
                        </if>

                        <if condition="@channel_code != null && @channel_code.length() > 0">
                            AND CHANNEL_CODE = '@channel_code'
                        </if>

                        <if condition="@marketing_team != null && @marketing_team.length() > 0">
                            AND MARKETING_TEAM = '@marketing_team'
                        </if>
                    GROUP BY reason_desc
            ]]>
        </Content>
    </Func>



    <Func ID="MONITOR10001_page" MODULE_NAME="坐席在岗统计报表（任务维度）详情" TYPE="QUERY" DESC="坐席在岗统计报表（任务维度）详情">
        <InParam>
            <Param Code="task_create_time" Name="任务生成的时间"/>
            <Param Code="task_create_start_time" Name="任务生成查询开始时间"/>
            <Param Code="task_create_end_time" Name="任务生成查询结束时间"/>
            <Param Code="actual_operator_no" Name="执行人工号"/>
            <Param Code="actual_operator_name" Name="执行人姓名"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			WITH BranchData AS (
              SELECT
            OPERATOR_NO AS operator_no,
            OPERATOR_NAME AS operator_name,
            EARLIEST_LOGIN_TIME AS first_login_time,
            LATEST_LOGOUT_TIME AS last_logout_time,
            ROUND( TOTAL_ONLINE_SECONDS / ( 60 ), 2 ) AS online_duration_hours,
            FIRST_PROCESS_TIME AS first_process_time,
            LAST_PROCESS_TIME AS last_process_time,
            ROUND( TOTAL_PROCESS_DUR_SECONDS / ( 60 ), 2 ) AS total_process_duration,
            ROUND( TOTAL_PROCESS_DUR_SECONDS / TOTAL_ONLINE_SECONDS, 2 ) AS work_efficiency,
            ROUND( TOTAL_CALL_NUM / ( TOTAL_ONLINE_SECONDS / ( 1 * 60 * 60 ) ), 2 ) AS call_efficiency
        FROM
            CRH_MONITOR.FOLLOWUPOPERATORSTATISTICS f
        where 1=1
                <if condition="@task_create_start_time != null && @task_create_start_time.length() > 0">
                    AND STAT_DATE >= TO_DATE('@task_create_start_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND STAT_DATE <= TO_DATE('@task_create_end_time', 'YYYY-MM-DD HH24:MI:SS')
                </if>
                <if condition="@actual_operator_no != null && @actual_operator_no.length() > 0">
                    AND OPERATOR_NO = '@actual_operator_no'
                </if>
                <if condition="@actual_operator_name != null && @actual_operator_name.length() > 0">
                    AND OPERATOR_NAME LIKE CONCAT('@actual_operator_name', '%')
                </if>
            ),

			RownumData AS (
				SELECT
					b.*,
					rownum as rn
				FROM
					BranchData b
			)

			SELECT
				*
			FROM RownumData r
			<if condition="@page_num != null && @page_size != null">
				WHERE r.rn BETWEEN ( @page_num - 1 ) * @page_size + 1 AND @page_num * @page_size
			</if>
            ]]></Content>
    </Func>


    <Func ID="MONITOR10001_total" MODULE_NAME="坐席在岗统计报表（任务维度）总数" TYPE="QUERY" DESC="坐席在岗统计报表（任务维度）总数">
        <InParam>
            <Param Code="task_create_time" Name="任务生成的时间"/>
            <Param Code="task_create_start_time" Name="任务生成查询开始时间"/>
            <Param Code="task_create_end_time" Name="任务生成查询结束时间"/>
            <Param Code="actual_operator_no" Name="执行人工号"/>
            <Param Code="actual_operator_name" Name="执行人姓名"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			WITH BranchData AS (
              SELECT
            OPERATOR_NO AS operator_no
        FROM
            CRH_MONITOR.FOLLOWUPOPERATORSTATISTICS f
        where 1=1
                <if condition="@task_create_start_time != null && @task_create_start_time.length() > 0">
                    AND STAT_DATE >= TO_DATE('@task_create_start_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND STAT_DATE <= TO_DATE('@task_create_end_time', 'YYYY-MM-DD HH24:MI:SS')
                </if>
                <if condition="@actual_operator_no != null && @actual_operator_no.length() > 0">
                    AND OPERATOR_NO = '@actual_operator_no'
                </if>
                <if condition="@actual_operator_name != null && @actual_operator_name.length() > 0">
                    AND OPERATOR_NAME LIKE CONCAT('@actual_operator_name', '%')
                </if>
            )

            SELECT
            count(*) total
            FROM
                BranchData b;
            ]]></Content>
    </Func>


    <Func ID="MONITOR10001_sum" MODULE_NAME="坐席在岗统计报表（任务维度）合计" TYPE="QUERY" DESC="坐席在岗统计报表（任务维度）合计">
        <InParam>
            <Param Code="task_create_time" Name="任务生成的时间"/>
            <Param Code="task_create_start_time" Name="任务生成查询开始时间"/>
            <Param Code="task_create_end_time" Name="任务生成查询结束时间"/>
            <Param Code="actual_operator_no" Name="执行人工号"/>
            <Param Code="actual_operator_name" Name="执行人姓名"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			WITH BranchData AS (

              SELECT
            '合计' AS operator_no,
            '-' AS operator_name,
            min( EARLIEST_LOGIN_TIME ) AS first_login_time,
            max( LATEST_LOGOUT_TIME ) AS last_logout_time,
            ROUND( sum( TOTAL_ONLINE_SECONDS ) / ( 60 ), 2 ) AS online_duration_hours,
            min( FIRST_PROCESS_TIME ) AS first_process_time,
            max( LAST_PROCESS_TIME ) AS last_process_time,
            ROUND( sum( TOTAL_PROCESS_DUR_SECONDS ) / ( 60 ), 2 ) AS total_process_duration,
            ROUND( sum( TOTAL_PROCESS_DUR_SECONDS ) / sum( TOTAL_ONLINE_SECONDS ), 2 ) AS work_efficiency,
            ROUND( sum( TOTAL_CALL_NUM ) / ( sum( TOTAL_ONLINE_SECONDS ) / ( 1 * 60 * 60 ) ), 2 ) AS call_efficiency
        FROM
            CRH_MONITOR.FOLLOWUPOPERATORSTATISTICS f
        WHERE 1=1
                <if condition="@task_create_start_time != null && @task_create_start_time.length() > 0">
                    AND STAT_DATE >= TO_DATE('@task_create_start_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND STAT_DATE <= TO_DATE('@task_create_end_time', 'YYYY-MM-DD HH24:MI:SS')
                </if>
                <if condition="@actual_operator_no != null && @actual_operator_no.length() > 0">
                    AND OPERATOR_NO = '@actual_operator_no'
                </if>
                <if condition="@actual_operator_name != null && @actual_operator_name.length() > 0">
                    AND OPERATOR_NAME LIKE CONCAT('@actual_operator_name', '%')
                </if>
            )

            SELECT
            *
            FROM
                BranchData b;
            ]]></Content>
    </Func>




    <Func ID="MONITOR10002_page" MODULE_NAME="每日报表(任务维度)详情" TYPE="QUERY" DESC="每日报表(任务维度)详情">
        <InParam>
            <Param Code="task_create_time" Name="任务生成的时间"/>
            <Param Code="task_create_start_time" Name="任务生成查询开始时间"/>
            <Param Code="task_create_end_time" Name="任务生成查询结束时间"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			WITH BranchData AS (
                SELECT
                    TO_CHAR(min(CREATE_DATETIME), 'YYYY-MM-DD') create_time,
                    count(*) AS task_create_count,
                    count( CASE WHEN PUSH_DATETIME is not null AND MERGE_TYPE != 2 THEN 1 END ) AS task_push_count,
                    count( CASE WHEN TASK_STATUS = '50' THEN 1 END ) AS task_complete_count,
                    count( CASE WHEN MERGE_TYPE = 2 THEN 1 END ) AS merge_count,
                    ROUND(count( CASE WHEN TASK_STATUS = '50' THEN 1 END ) / NULLIF(count(*), 0) * 100, 2 ) AS task_complete_rate,
                    nvl(sum(CALL_NUM), 0) AS call_total_count,
                    COUNT( CASE WHEN TRIM(ANSWER_STATUS) = '2' THEN 1 END ) AS connect_total_count,
                    round( sum( CASE WHEN TO_CHAR(f.CREATE_DATETIME, 'HH24:MI:SS') BETWEEN '09:00:00' AND '17:30:00' THEN (DEAL_DATETIME - PUSH_DATETIME) * 24 * 60 * 60 ELSE 0 END )/ NULLIF(COUNT(CASE WHEN TASK_STATUS = '50'  AND TO_CHAR(f.CREATE_DATETIME, 'HH24:MI:SS') BETWEEN '09:00:00' AND '17:30:00' THEN 1 END), 0), 0 ) AS avg_response_time
                FROM
                    CRH_FUS.FOLLOWUPTASK f
                where
                CREATE_DATETIME >= TO_DATE('@task_create_start_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND CREATE_DATETIME <= TO_DATE('@task_create_end_time', 'YYYY-MM-DD HH24:MI:SS')
                    ),
            onlineUserCount AS (
                SELECT
                  count( CASE WHEN TOTAL_ONLINE_SECONDS >= 7 * 60 * 60 THEN 1 END ) AS online_user_count
                  FROM
                    CRH_MONITOR.FOLLOWUPOPERATORSTATISTICS ss
                  WHERE 1=1
                <if condition="@task_create_start_time != null && @task_create_start_time.length() > 0">
                    AND STAT_DATE >= TO_DATE('@task_create_start_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND STAT_DATE <= TO_DATE('@task_create_end_time', 'YYYY-MM-DD HH24:MI:SS')
                </if>
                  ),
            todayHasOnlineUserCount AS (
                SELECT count(DISTINCT OPERATOR_NO) AS today_has_online_user_count
                from CRH_FUS.FOLLOWUPONLINERECORD
                where STATUS = 1
                <if condition="@task_create_start_time != null && @task_create_start_time.length() > 0">
                    AND CREATE_DATETIME >= TO_DATE('@task_create_start_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND CREATE_DATETIME <= TO_DATE('@task_create_end_time', 'YYYY-MM-DD HH24:MI:SS')
                </if>
                  )

              SELECT
              b.*,c.online_user_count ,
              d.today_has_online_user_count,
              ROUND(b.connect_total_count/NULLIF(b.call_total_count, 0) * 100, 2 ) AS connect_rate,
              ROUND(b.call_total_count/NULLIF(c.online_user_count, 0), 2 ) AS operator_avg_daily_calls_count,
              ROUND(b.connect_total_count/NULLIF(c.online_user_count, 0), 2 )AS operator_avg_daily_connect_count,
              ROUND(b.call_total_count/NULLIF(c.online_user_count, 0), 2) AS daily_capacity
            FROM
              BranchData b ,onlineUserCount c ,todayHasOnlineUserCount d;
            ]]></Content>
    </Func>


    <Func ID="MONITOR10002_total" MODULE_NAME="每日报表(任务维度)总数" TYPE="QUERY" DESC="每日报表(任务维度)总数">
        <InParam>
            <Param Code="task_create_time" Name="任务生成的时间"/>
            <Param Code="task_create_start_time" Name="任务生成查询开始时间"/>
            <Param Code="task_create_end_time" Name="任务生成查询结束时间"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
        SELECT 1 total FROM DUAL
            ]]></Content>
    </Func>


    <Func ID="MONITOR10002_sum" MODULE_NAME="每日报表(任务维度)合计" TYPE="QUERY" DESC="每日报表(任务维度)合计">
        <InParam>
            <Param Code="task_create_time" Name="任务生成的时间"/>
            <Param Code="task_create_start_time" Name="任务生成查询开始时间"/>
            <Param Code="task_create_end_time" Name="任务生成查询结束时间"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
        SELECT 1 FROM DUAL
            ]]></Content>
    </Func>






    <Func ID="MONITOR10003_page" MODULE_NAME="每日报表(申请维度)详情" TYPE="QUERY" DESC="每日报表(申请维度)详情">
        <InParam>
            <Param Code="apply_time" Name="回访申请的时间"/>
            <Param Code="apply_start_time" Name="回访申请创建的开始时间"/>
            <Param Code="apply_end_time" Name="回访申请创建的结束时间"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			WITH BranchData AS (
            SELECT
                TO_CHAR(min(CREATE_DATETIME), 'YYYY-MM-DD') create_time,
                count(*) AS apply_count,
                count( CASE WHEN STATUS = '7' THEN 1 WHEN STATUS = '8' THEN 1 WHEN STATUS = '9' THEN 1 WHEN STATUS = '10' THEN 1 WHEN STATUS = '11' THEN 1 END ) AS complete_count,
                count( CASE WHEN STATUS = '7' THEN 1 END ) AS pass_count,
                count( CASE WHEN STATUS = '8' THEN 1 END ) AS invalid_count,
                count( CASE WHEN STATUS = '9' THEN 1 END ) AS reject_count,
                count( CASE WHEN STATUS = '11' THEN 1 END ) AS strategy_complete_count,
                count( CASE WHEN STATUS = '10' THEN 1 END ) AS manual_end_count,
                ROUND(count( CASE WHEN STATUS = '10' THEN 1 END ) / NULLIF(COUNT(*), 0) * 100 , 2 ) AS manual_end_rate,
                count( CASE WHEN LAST_QNAIRE_COMPLETION = 1 THEN 1 END ) AS survey_complete_count,
                ROUND(count( CASE WHEN LAST_QNAIRE_COMPLETION = 1 AND (STATUS = '7' OR STATUS = '8' OR STATUS = '9' OR STATUS = '10' OR STATUS = '11') THEN 1 END ) / NULLIF(count( CASE WHEN STATUS = '7' THEN 1 WHEN STATUS = '8' THEN 1 WHEN STATUS = '9' THEN 1 WHEN STATUS = '10' THEN 1 WHEN STATUS = '11' THEN 1 END ), 0) * 100 , 2 ) AS survey_complete_rate,
                ROUND(AVG(CASE WHEN TO_CHAR(CREATE_DATETIME, 'HH24:MI:SS') BETWEEN '09:30:00' AND '16:00:00' THEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 * 60  ELSE NULL END ), 0 ) AS avg_first_response_time,
                count( CASE WHEN BUSIN_TYPE = '1' THEN 1 END ) AS apply_online_success,
                count( CASE WHEN BUSIN_TYPE = '2' THEN 1 END ) AS apply_business_handling,
                count( CASE WHEN BUSIN_TYPE = '3' THEN 1 END ) AS apply_initiative_launch,
                count( CASE WHEN BUSIN_TYPE = '4' THEN 1 END ) AS apply_online_submit,
                count( CASE WHEN BUSIN_TYPE = '1' AND STATUS = '7' THEN 1 END ) AS pass_online_success,
                count( CASE WHEN BUSIN_TYPE = '2' AND STATUS = '7' THEN 1 END ) AS pass_business_handling,
                count( CASE WHEN BUSIN_TYPE = '3' AND STATUS = '7' THEN 1 END ) AS pass_initiative_launch,
                count( CASE WHEN BUSIN_TYPE = '4' AND STATUS = '7' THEN 1 END ) AS pass_online_submit,
                count( CASE WHEN BUSIN_TYPE = '1' AND STATUS = '8' THEN 1 END ) AS no_pass_online_success,
                count( CASE WHEN BUSIN_TYPE = '2' AND STATUS = '8' THEN 1 END ) AS no_pass_business_handling,
                count( CASE WHEN BUSIN_TYPE = '3' AND STATUS = '8' THEN 1 END ) AS no_pass_initiative_launch,
                count( CASE WHEN BUSIN_TYPE = '4' AND STATUS = '8' THEN 1 END ) AS no_pass_online_submit,
                count( CASE WHEN IS_ANSWER= '0' THEN 1 END ) AS not_connect_count

            FROM
                CRH_FUS.FOLLOWUPAPPLY f
            where 1=1
                    <if condition="@apply_start_time != null && @apply_start_time.length() > 0">
                        AND CREATE_DATETIME >= TO_DATE('@apply_start_time', 'YYYY-MM-DD HH24:MI:SS')
                        AND CREATE_DATETIME <= TO_DATE('@apply_end_time', 'YYYY-MM-DD HH24:MI:SS')
                    </if>
            ),

			RownumData AS (
				SELECT
					b.*,
                    ROUND(b.complete_count / NULLIF(b.apply_count, 0) * 100 , 2 ) AS complete_rate,
                    ROUND(b.pass_count / NULLIF(b.apply_count, 0) * 100 , 2 ) AS pass_rate,
                    ROUND(b.invalid_count / NULLIF(b.apply_count, 0) * 100 , 2 ) AS invalid_rate,
                    ROUND(b.reject_count / NULLIF(b.apply_count, 0) * 100 , 2 ) AS reject_rate,
                    ROUND(b.strategy_complete_count / NULLIF(b.apply_count, 0) * 100 , 2 ) AS strategy_complete_rate,
                    ROUND(b.not_connect_count / NULLIF(b.apply_count, 0) * 100 , 2 ) AS not_connect_rate,
                    (b.strategy_complete_count-b.not_connect_count) AS resp_not_completed_count,
                    ROUND((b.strategy_complete_count-b.not_connect_count) / NULLIF(b.apply_count, 0) * 100 , 2 ) AS resp_not_completed_rate,
                    (b.reject_count+(b.strategy_complete_count-b.not_connect_count)) AS connect_uncooperative_count,
                    ROUND((b.reject_count+(b.strategy_complete_count-b.not_connect_count)) / NULLIF(b.apply_count, 0) * 100 , 2 ) AS connect_uncooperative_rate,

					rownum as rn
				FROM
					BranchData b
			)

			SELECT
				*
			FROM RownumData r
            ]]></Content>
    </Func>


    <Func ID="MONITOR10003_total" MODULE_NAME="每日报表(申请维度)总数" TYPE="QUERY" DESC="每日报表(申请维度)总数">
        <InParam>
            <Param Code="apply_time" Name="回访申请的时间"/>
            <Param Code="apply_start_time" Name="回访申请创建的开始时间"/>
            <Param Code="apply_end_time" Name="回访申请创建的结束时间"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
        SELECT 1 total FROM DUAL
            ]]></Content>
    </Func>


    <Func ID="MONITOR10003_sum" MODULE_NAME="每日报表(申请维度)合计" TYPE="QUERY" DESC="每日报表(申请维度)合计">
        <InParam>
            <Param Code="apply_start_time" Name="回访申请创建的开始时间"/>
            <Param Code="apply_end_time" Name="回访申请创建的结束时间"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
        SELECT 1 FROM DUAL
            ]]></Content>
    </Func>


    <Func ID="MONITOR10004_page" MODULE_NAME="回访流水报表（任务维度）详情" TYPE="QUERY" DESC="回访流水报表（任务维度）详情">
        <InParam>
            <Param Code="task_create_time" Name="任务生成的时间"/>
            <Param Code="actual_operator_no" Name="执行人工号"/>
            <Param Code="client_name" Name="客户姓名"/>
            <Param Code="task_create_start_time" Name="任务生成查询开始时间"/>
            <Param Code="task_create_end_time" Name="任务生成查询结束时间"/>
            <Param Code="busin_type" Name="申请类型"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="answer_status" Name="接通情况"/>
            <Param Code="first_task" Name="是否首次统计"/>
            <Param Code="push_time" Name="任务下发时间"/>
            <Param Code="push_start_time" Name="任务下发开始时间"/>
            <Param Code="push_end_time" Name="任务下发结束时间"/>
            <Param Code="deal_time" Name="任务处理时间"/>
            <Param Code="deal_start_time" Name="任务处理时间查询开始时间"/>
            <Param Code="deal_end_time" Name="任务处理时间查询结束时间"/>
            <Param Code="finish_time" Name="任务结束时间"/>
            <Param Code="finish_start_time" Name="任务结束时间查询开始时间"/>
            <Param Code="finish_end_time" Name="任务结束时间查询结束时间"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			WITH BranchData AS (
                SELECT
	                QNAIRE_CONTENT ,
                    CLIENT_NAME ,
                    CASE
                    WHEN TRIM(LEADING '0' FROM SUBSTR(CLIENT_ID, 3)) = ''
                    THEN '0'
                    ELSE TRIM(LEADING '0' FROM SUBSTR(CLIENT_ID, 3))
                    END
                    AS CLIENT_ID,
                    UP_BRANCH_NO ,
                    BRANCH_NO ,
                    ACTUAL_OPERATOR_NAME ,
                    ACTUAL_OPERATOR_NO ,
                    IS_FIRST_TASK ,
                    CREATE_DATETIME ,
                    PUSH_DATETIME ,
                    DEAL_DATETIME ,
                    FINISH_DATETIME ,
                    ROUND((DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60,0) AS task_response_duration ,
                    ROUND((DEAL_DATETIME - PUSH_DATETIME) * 24 * 60 * 60,0) AS operator_response_duration ,
                    ROUND((FINISH_DATETIME - DEAL_DATETIME) * 24 * 60 * 60,0) AS operator_deal_duration ,
                    ROUND((PUSH_DATETIME - CREATE_DATETIME) * 24 * 60 * 60,0) AS push_duration ,
                    BUSIN_TYPE ,
                    TASK_STATUS ,
                    HANDLE_STATUS ,
                    CASE
                        WHEN TASK_STATUS = '10' THEN '待处理'
                        WHEN TASK_STATUS = '15' THEN '待处理'
                        WHEN TASK_STATUS = '20' THEN '处理中'
                        WHEN TASK_STATUS = '30' THEN '待核实'
                        WHEN TASK_STATUS = '40' THEN '再核实'
                        WHEN TASK_STATUS = '50' THEN '已完成'
                        WHEN TASK_STATUS = '60' THEN '已关闭'
                        ELSE TASK_STATUS
                    END AS TASK_STATUS_NAME,
                    CASE
                        WHEN HANDLE_STATUS = '1' THEN '通过'
                        WHEN HANDLE_STATUS = '2' THEN '预约下次'
                        WHEN HANDLE_STATUS = '3' THEN '未接通'
                        WHEN HANDLE_STATUS = '4' THEN '待核实'
                        WHEN HANDLE_STATUS = '5' THEN '存在不合规答案'
                        WHEN HANDLE_STATUS = '6' THEN '接通但不配合回访'
                        WHEN HANDLE_STATUS = '7' THEN '应答未完成'
                        ELSE HANDLE_STATUS
                    END AS HANDLE_STATUS_NAME,
                    ANSWER_STATUS ,
                    ACTIVITY_NAME ,
                    CHANNEL_CODE ,
                    CHANNEL_NAME ,
                    MARKETING_TEAM
                FROM
                    CRH_FUS.FOLLOWUPTASK f
                where 1=1
                <if condition="@task_create_start_time != null && @task_create_start_time.length() > 0">
                    AND CREATE_DATETIME >= TO_DATE('@task_create_start_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND CREATE_DATETIME <= TO_DATE('@task_create_end_time', 'YYYY-MM-DD HH24:MI:SS')
                </if>
                <if condition="@actual_operator_no != null && @actual_operator_no.length() > 0">
                    AND actual_operator_no = '@actual_operator_no'
                </if>
                <if condition="@client_name != null && @client_name.length() > 0">
                    AND CLIENT_NAME = '@client_name'
                </if>
                <if condition="@first_task != null && @first_task.length() > 0">
                    AND IS_FIRST_TASK = '@first_task'
                </if>
                <if condition="@busin_type != null && @busin_type.length() > 0">
                    AND BUSIN_TYPE = '@busin_type'
                </if>
                <if condition="@branch_no != null && @branch_no.length() > 0">
                    and instr(','||'@branch_no'||',',','||branch_no||',')>0
                </if>
                <if condition="@up_branch_no != null && @up_branch_no.length() > 0">
                    and instr(','||'@up_branch_no'||',',','||up_branch_no||',')>0
                </if>
                <if condition="@answer_status != null && @answer_status.length() > 0">
                    AND answer_status = '@answer_status'
                </if>
                <if condition="@push_start_time != null && @push_start_time.length() > 0">
                    AND PUSH_DATETIME >= TO_DATE('@push_start_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND PUSH_DATETIME <= TO_DATE('@push_end_time', 'YYYY-MM-DD HH24:MI:SS')
                </if>
                <if condition="@deal_start_time != null && @deal_start_time.length() > 0">
                    AND DEAL_DATETIME >= TO_DATE('@deal_start_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND DEAL_DATETIME <= TO_DATE('@deal_end_time', 'YYYY-MM-DD HH24:MI:SS')
                </if>
                <if condition="@finish_start_time != null && @finish_start_time.length() > 0">
                    AND FINISH_DATETIME >= TO_DATE('@finish_start_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND FINISH_DATETIME <= TO_DATE('@finish_end_time', 'YYYY-MM-DD HH24:MI:SS')
                </if>
            ),

			RownumData AS (
				SELECT
					b.*,
					rownum as rn
				FROM
					BranchData b
			)

			SELECT
				*
			FROM RownumData r
			<if condition="@page_num != null && @page_size != null">
				WHERE r.rn BETWEEN ( @page_num - 1 ) * @page_size + 1 AND @page_num * @page_size
			</if>
            ]]></Content>
    </Func>


    <Func ID="MONITOR10004_total" MODULE_NAME="回访流水报表（任务维度）总数" TYPE="QUERY" DESC="回访流水报表（任务维度）总数">
        <InParam>
            <Param Code="task_create_time" Name="任务生成的时间"/>
            <Param Code="actual_operator_no" Name="执行人工号"/>
            <Param Code="client_name" Name="客户姓名"/>
            <Param Code="task_create_start_time" Name="任务生成查询开始时间"/>
            <Param Code="task_create_end_time" Name="任务生成查询结束时间"/>
            <Param Code="busin_type" Name="申请类型"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="answer_status" Name="接通情况"/>
            <Param Code="first_task" Name="是否首次统计"/>
            <Param Code="push_time" Name="任务下发时间"/>
            <Param Code="push_start_time" Name="任务下发开始时间"/>
            <Param Code="push_end_time" Name="任务下发结束时间"/>
            <Param Code="deal_time" Name="任务处理时间"/>
            <Param Code="deal_start_time" Name="任务处理时间查询开始时间"/>
            <Param Code="deal_end_time" Name="任务处理时间查询结束时间"/>
            <Param Code="finish_time" Name="任务结束时间"/>
            <Param Code="finish_start_time" Name="任务结束时间查询开始时间"/>
            <Param Code="finish_end_time" Name="任务结束时间查询结束时间"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			WITH BranchData AS (
                SELECT
	                rownum
                FROM
                    CRH_FUS.FOLLOWUPTASK f
                where 1=1
                <if condition="@task_create_start_time != null && @task_create_start_time.length() > 0">
                    AND CREATE_DATETIME >= TO_DATE('@task_create_start_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND CREATE_DATETIME <= TO_DATE('@task_create_end_time', 'YYYY-MM-DD HH24:MI:SS')
                </if>
                <if condition="@actual_operator_no != null && @actual_operator_no.length() > 0">
                    AND actual_operator_no = '@actual_operator_no'
                </if>
                <if condition="@client_name != null && @client_name.length() > 0">
                    AND CLIENT_NAME = '@client_name'
                </if>
                <if condition="@first_task != null && @first_task.length() > 0">
                    AND IS_FIRST_TASK = '@first_task'
                </if>
                <if condition="@busin_type != null && @busin_type.length() > 0">
                    AND BUSIN_TYPE = '@busin_type'
                </if>
                <if condition="@branch_no != null && @branch_no.length() > 0">
                    and instr(','||'@branch_no'||',',','||branch_no||',')>0
                </if>
                <if condition="@up_branch_no != null && @up_branch_no.length() > 0">
                    and instr(','||'@up_branch_no'||',',','||up_branch_no||',')>0
                </if>
                <if condition="@answer_status != null && @answer_status.length() > 0">
                    AND answer_status = '@answer_status'
                </if>
                <if condition="@push_start_time != null && @push_start_time.length() > 0">
                    AND PUSH_DATETIME >= TO_DATE('@push_start_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND PUSH_DATETIME <= TO_DATE('@push_end_time', 'YYYY-MM-DD HH24:MI:SS')
                </if>
                <if condition="@deal_start_time != null && @deal_start_time.length() > 0">
                    AND DEAL_DATETIME >= TO_DATE('@deal_start_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND DEAL_DATETIME <= TO_DATE('@deal_end_time', 'YYYY-MM-DD HH24:MI:SS')
                </if>
                <if condition="@finish_start_time != null && @finish_start_time.length() > 0">
                    AND FINISH_DATETIME >= TO_DATE('@finish_start_time', 'YYYY-MM-DD HH24:MI:SS')
                    AND FINISH_DATETIME <= TO_DATE('@finish_end_time', 'YYYY-MM-DD HH24:MI:SS')
                </if>
            )

            SELECT
            count(*) total
            FROM
                BranchData b;
            ]]></Content>
    </Func>


    <Func ID="MONITOR10004_sum" MODULE_NAME="回访流水报表（任务维度）合计" TYPE="QUERY" DESC="回访流水报表（任务维度）合计">
        <InParam>
            <Param Code="task_create_time" Name="任务生成的时间"/>
            <Param Code="actual_operator_no" Name="执行人工号"/>
            <Param Code="client_name" Name="客户姓名"/>
            <Param Code="task_create_start_time" Name="任务生成查询开始时间"/>
            <Param Code="task_create_end_time" Name="任务生成查询结束时间"/>
            <Param Code="busin_type" Name="申请类型"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="所属分公司"/>
            <Param Code="answer_status" Name="接通情况"/>
            <Param Code="first_task" Name="是否首次统计"/>
            <Param Code="push_time" Name="任务下发时间"/>
            <Param Code="push_start_time" Name="任务下发开始时间"/>
            <Param Code="push_end_time" Name="任务下发结束时间"/>
            <Param Code="deal_time" Name="任务处理时间"/>
            <Param Code="deal_start_time" Name="任务处理时间查询开始时间"/>
            <Param Code="deal_end_time" Name="任务处理时间查询结束时间"/>
            <Param Code="finish_time" Name="任务结束时间"/>
            <Param Code="finish_start_time" Name="任务结束时间查询开始时间"/>
            <Param Code="finish_end_time" Name="任务结束时间查询结束时间"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
        SELECT 1 FROM DUAL
            ]]></Content>
    </Func>

    <Func ID="MONITOR3001" MODULE_NAME="业务类型概况查询" TYPE="QUERY" DESC="业务类型概况查询">
        <InParam>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			WITH BranchData AS (
                SELECT
                  BUSIN_TYPE,
                  count( * )  AS NUM
                FROM
                  CRH_FUS.FOLLOWUPAPPLY
                WHERE
                  CREATE_DATETIME >= TRUNC( SYSDATE )
                GROUP BY
                  BUSIN_TYPE
			)
			SELECT
				*
			FROM BranchData;
		]]></Content>
    </Func>

    <Func ID="MONITOR3002" MODULE_NAME="回访申请量小时趋势图" TYPE="QUERY" DESC="回访申请量小时趋势图">
        <InParam>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
              WITH hour_sequence AS (
                SELECT
                TRUNC(SYSDATE) + (LEVEL-1)/24 AS hour_start
                FROM DUAL
                CONNECT BY LEVEL <= 24
            )
            SELECT
                TO_CHAR(hs.hour_start, 'HH24":00"') AS query_hour,
                NVL(COUNT(t.CREATE_DATETIME), 0) AS count
            FROM hour_sequence hs
            LEFT JOIN CRH_FUS.FOLLOWUPAPPLY t
                ON t.CREATE_DATETIME BETWEEN hs.hour_start AND hs.hour_start + 1/24 - INTERVAL '1' SECOND
            WHERE hs.hour_start <= TRUNC(SYSDATE, 'HH24')
            GROUP BY hs.hour_start
            ORDER BY hs.hour_start;

		]]></Content>
    </Func>

    <Func ID="MONITOR3003" MODULE_NAME="回访申请量日趋势" TYPE="QUERY" DESC="回访申请量日趋势">
        <InParam>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
            WITH date_sequence AS (
                SELECT
                    TRUNC(SYSDATE) - (LEVEL - 1) AS day_start,
                    TRUNC(SYSDATE) - (LEVEL - 1) + 1 AS day_end
                FROM DUAL
                CONNECT BY LEVEL <= 8
            )
            SELECT
                TO_CHAR(ds.day_start, 'MMDD') AS date_group,
                NVL(COUNT(t.CREATE_DATETIME), 0) AS count
            FROM date_sequence ds
            LEFT JOIN CRH_FUS.FOLLOWUPAPPLY t
                ON t.CREATE_DATETIME >= ds.day_start
                AND t.CREATE_DATETIME < ds.day_end
            GROUP BY ds.day_start
            ORDER BY ds.day_start
		]]></Content>
    </Func>

    <Func ID="MONITOR3004" MODULE_NAME="平均首次响应时长小时趋势" TYPE="QUERY" DESC="平均首次响应时长小时趋势">
        <InParam>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
            WITH hour_sequence AS (
                SELECT
                TRUNC(SYSDATE) + (LEVEL-1)/24 AS hour_start
                FROM DUAL
                CONNECT BY LEVEL <= 24
            )
            SELECT
                TO_CHAR(hs.hour_start, 'HH24":00"') AS query_hour,
                NVL(ROUND(avg(( t.FIRST_DEAL_TIME - t.CREATE_DATETIME ) * 24 * 60 * 60 ),1), 0) AS avg_response_seconds
            FROM hour_sequence hs
            LEFT JOIN CRH_FUS.FOLLOWUPAPPLY t
                ON t.CREATE_DATETIME BETWEEN hs.hour_start AND hs.hour_start + 1/24 - INTERVAL '1' SECOND
            WHERE hs.hour_start <= TRUNC(SYSDATE, 'HH24')
            GROUP BY hs.hour_start
            ORDER BY hs.hour_start;
		]]></Content>
    </Func>

    <Func ID="MONITOR3005" MODULE_NAME="平均响应时长日趋势图（工作时段）" TYPE="QUERY" DESC="平均响应时长日趋势图（工作时段）">
        <InParam>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
            WITH date_sequence AS (
                SELECT
                    TRUNC(SYSDATE) - (LEVEL - 1) AS day_start
                FROM DUAL
                CONNECT BY LEVEL <= 8
            )
            SELECT
                TO_CHAR(ds.day_start, 'MMDD') AS date_group,
                ROUND(NVL(AVG((t.FIRST_DEAL_TIME - t.CREATE_DATETIME)  * 24 * 60 * 60), 0), 0) AS avg_response_seconds
            FROM date_sequence ds
            LEFT JOIN CRH_FUS.FOLLOWUPAPPLY t
                ON TRUNC(t.CREATE_DATETIME) = ds.day_start  -- 按天关联
                AND t.CREATE_DATETIME BETWEEN
                    ds.day_start + INTERVAL '9:30' HOUR TO MINUTE  -- 开始时间：当天9:30
                    AND ds.day_start + INTERVAL '16:00' HOUR TO MINUTE  -- 结束时间：当天16:00
            GROUP BY ds.day_start
            ORDER BY ds.day_start
		]]></Content>
    </Func>

    <Func ID="MONITOR3006" MODULE_NAME="回访申请概况接口" TYPE="QUERY" DESC="回访申请概况接口">
        <InParam>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
            SELECT
            count( * ) apply_count,-- 回访申请总量
            count( CASE WHEN FIRST_DEAL_TIME is not null THEN 1 END ) AS completed_count,-- 已执行量
            round( count( CASE WHEN STATUS IN ( '7' ) THEN 1 END ) / NULLIF(count( * ), 0) , 2 ) * 100 AS pass_rate, -- 回访通过率
            round( count( CASE WHEN STATUS IN ( '7', '8', '9', '10', '11' ) THEN 1 END ) / NULLIF(count( * ), 0) , 2 ) * 100 AS completed_rate -- 已完成率

            FROM
              CRH_FUS.FOLLOWUPAPPLY f
            WHERE
              f.CREATE_DATETIME >= trunc( SYSDATE )
		]]></Content>
    </Func>

    <Func ID="MONITOR3007" MODULE_NAME="回访首次响应时长分布" TYPE="QUERY" DESC="回访首次响应时长分布">
        <InParam>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
            SELECT
            count(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 * 60 >= 0 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 * 60 < 3 * 60 THEN 1 END) AS greater_0_less_3_minutes_count,
            count(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 * 60 >= 3 * 60 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 * 60 < 10 * 60 THEN 1 END) AS greater_3_less_10_minutes_count,
            count(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 * 60 >= 10 * 60 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 * 60 < 30 * 60 THEN 1 END) AS greater_10_less_30_minutes_count,
            count(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 * 60 >= 30 * 60 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 * 60 < 60 * 60 THEN 1 END) AS greater_30_less_60_minutes_count,
            count(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 * 60 >= 60 * 60 THEN 1 END) AS greater_60_minutes_count,
            ROUND(count(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 * 60 >= 0 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 * 60 < 3 * 60 THEN 1 END) * 100.0 / NULLIF(COUNT(FIRST_DEAL_TIME - CREATE_DATETIME), 0), 2) AS greater_0_less_3_minutes_rate,
            ROUND(count(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 * 60 >= 3 * 60 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 * 60 < 10 * 60 THEN 1 END) * 100.0 / NULLIF(COUNT(FIRST_DEAL_TIME - CREATE_DATETIME), 0), 2) AS greater_3_less_10_minutes_rate,
            ROUND(count(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 * 60 >= 10 * 60 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 * 60 < 30 * 60 THEN 1 END) * 100.0 / NULLIF(COUNT(FIRST_DEAL_TIME - CREATE_DATETIME), 0), 2) AS greater_10_less_30_minutes_rate,
            ROUND(count(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 * 60 >= 30 * 60 AND (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 * 60 < 60 * 60 THEN 1 END) * 100.0 / NULLIF(COUNT(FIRST_DEAL_TIME - CREATE_DATETIME), 0), 2) AS greater_30_less_60_minutes_rate,
            ROUND(count(CASE WHEN (FIRST_DEAL_TIME - CREATE_DATETIME) * 24 * 60 * 60 >= 60 * 60 THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) AS greater_60_minutes_rate

            FROM
                CRH_FUS.FOLLOWUPAPPLY f
            WHERE
              f.CREATE_DATETIME >= trunc( SYSDATE )
		]]></Content>
    </Func>

    <Func ID="MONITOR3008" MODULE_NAME="分公司任务量TOP8" TYPE="QUERY" DESC="分公司任务量TOP8">
        <InParam>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			WITH BranchData AS (
                SELECT
                f.UP_BRANCH_NO,
                count( * ) apply_count,-- 回访申请总量
                count( CASE WHEN FIRST_DEAL_TIME is null THEN 1 END ) AS non_completed_count

                FROM
                  CRH_FUS.FOLLOWUPAPPLY f
                WHERE
                 f.CREATE_DATETIME >= trunc( SYSDATE )
                group by f.UP_BRANCH_NO
                order by apply_count desc
            ),
            allApplyCountData As (
              select count(*) as allApplyCount from CRH_FUS.FOLLOWUPAPPLY f
                WHERE
                 f.CREATE_DATETIME >= trunc( SYSDATE )
            ),
			RownumData AS (
				SELECT
					b.*,
					rownum as rn
				FROM
					BranchData b
			)

			SELECT
				r.*,NVL(ROUND(r.apply_count * 100/NULLIF(a.allApplyCount, 0), 2), 0) AS non_completed_rate -- 计算占比
			FROM RownumData r,allApplyCountData a
				WHERE r.rn BETWEEN 1 AND 8
		]]></Content>
    </Func>

    <Func ID="MONITOR3009" MODULE_NAME="回访派单队列流速" TYPE="QUERY" DESC="回访派单队列流速">
        <InParam>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			WITH hour_sequence AS (
                SELECT
                TRUNC(SYSDATE) + (LEVEL-1)/24 AS hour_start
                FROM DUAL
                CONNECT BY LEVEL <= 24
            )
            SELECT
              TO_CHAR(hs.hour_start, 'HH24":00"') AS query_hour,
              NVL(SUM(CASE WHEN t.queue_level = 1 THEN 1 ELSE 0 END), 0) AS queue_level0_num,
              NVL(SUM(CASE WHEN t.queue_level = 2 THEN 1 ELSE 0 END), 0) AS queue_level1_num,
              NVL(SUM(CASE WHEN t.queue_level = 3 THEN 1 ELSE 0 END), 0) AS queue_level2_num,
              NVL(SUM(CASE WHEN t.queue_level = 4 THEN 1 ELSE 0 END), 0) AS queue_level3_num,
              NVL(SUM(CASE WHEN t.queue_level = 5 THEN 1 ELSE 0 END), 0) AS queue_level4_num
            FROM hour_sequence hs
            LEFT JOIN crh_ads_fus.dispatchtask t
              ON t.dispatch_datetime BETWEEN hs.hour_start AND hs.hour_start + 1/24 - INTERVAL '1' SECOND
              AND t.subsys_id = '27'
              where hs.hour_start <= TRUNC(SYSDATE, 'HH24')
            GROUP BY hs.hour_start
            ORDER BY hs.hour_start

		]]></Content>
    </Func>

    <Func ID="MONITOR3010" MODULE_NAME="派发成功率分布" TYPE="QUERY" DESC="派发成功率分布">
        <InParam>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			SELECT
                COUNT(CASE WHEN dispatch_fail_count >= 4 THEN 1 END) AS dispatch5Times,
                COUNT(CASE WHEN dispatch_fail_count >=2 AND dispatch_fail_count < 4 THEN 1 END) AS dispatch3Times,
                COUNT(CASE WHEN dispatch_fail_count =1 THEN 1 END) AS dispatch2Times,
                COUNT(CASE WHEN dispatch_fail_count =0 THEN 1 END) AS dispatch1Times,
                NVL(ROUND(COUNT(CASE WHEN dispatch_fail_count >= 4 THEN 1 END) * 100/NULLIF(count( * ), 0), 0), 0) AS dispatch5TimesRadioStr,
                NVL(ROUND(COUNT(CASE WHEN dispatch_fail_count >=2 AND dispatch_fail_count < 4 THEN 1 END) * 100/NULLIF(count( * ), 0), 0), 0) AS dispatch3TimesRadioStr,
                NVL(ROUND(COUNT(CASE WHEN dispatch_fail_count =1 THEN 1 END) * 100/NULLIF(count( * ), 0), 0), 0) AS dispatch2TimesRadioStr,
                NVL(ROUND(COUNT(CASE WHEN dispatch_fail_count =0 THEN 1 END) * 100/NULLIF(count( * ), 0), 0), 0) AS dispatch1TimesRadioStr
            FROM crh_ads_fus.dispatchtask
            WHERE dispatch_status = '3'
                AND subsys_id = '27'
                AND finish_datetime > TRUNC(SYSDATE)
		]]></Content>
    </Func>

    <Func ID="MONITOR3011" MODULE_NAME="待处理客户等待时长TOP5" TYPE="QUERY" DESC="待处理客户等待时长TOP5">
        <InParam>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			WITH BranchData AS (
                SELECT
                  CASE
                    WHEN
                      LENGTH( CLIENT_NAME ) = 1 THEN
                        '*'
                    WHEN LENGTH( CLIENT_NAME ) = 2 THEN
                        SUBSTR( CLIENT_NAME, 1, 1 ) || '*' ELSE SUBSTR( CLIENT_NAME, 1, 1 ) || LPAD( '*', LENGTH( CLIENT_NAME ) - 2, '*' ) || SUBSTR( CLIENT_NAME, - 1, 1 )
                  END AS CLIENT_NAME,
                to_char(CREATE_DATETIME , 'yyyy-mm-dd hh24:mi:ss') AS APPLY_TIME,
                ROUND((SYSDATE - CREATE_DATETIME) * 24 * 60 * 60, 0) AS duration
                FROM
                  CRH_FUS.FOLLOWUPAPPLY
                WHERE
                CREATE_DATETIME >= trunc( SYSDATE )
                AND FIRST_DEAL_TIME is null
                ORDER BY  (SYSDATE - CREATE_DATETIME) * 24 * 60 * 60 desc
            ),

			RownumData AS (
				SELECT
					b.*,
					rownum as rn
				FROM
					BranchData b
			)

			SELECT
				*
			FROM RownumData r
				WHERE r.rn BETWEEN 1 AND 5
		]]></Content>
    </Func>

    <Func ID="MONITOR3012" MODULE_NAME="派单平均耗时" TYPE="QUERY" DESC="派单平均耗时">
        <InParam>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
			SELECT
             ROUND(avg (( DISPATCH_DATETIME - CREATE_DATETIME ) * 24 * 60 * 60), 0) AS avg_duration
            FROM
              CRH_ADS_FUS.DISPATCHTASK
            WHERE
              CREATE_DATETIME BETWEEN
                    trunc( SYSDATE ) + INTERVAL '9:30' HOUR TO MINUTE
                    AND trunc( SYSDATE ) + INTERVAL '16:00' HOUR TO MINUTE
		]]></Content>
    </Func>

    <Func ID="MONITOR3013" MODULE_NAME="今日个人处理量TOP8接口" TYPE="QUERY" DESC="今日个人处理量TOP8接口">
        <InParam>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
            WITH operators AS (
                SELECT DISTINCT OPERATOR_NO
                FROM CRH_FUS.FOLLOWUPONLINERECORD
                WHERE CREATE_DATETIME >= TRUNC(SYSDATE)
                AND STATUS = 1
            )
            SELECT *
            FROM (
                SELECT inner_query.*, ROWNUM rn
                FROM (
                    SELECT
                        f.ACTUAL_OPERATOR_NO,
                        max(f.actual_operator_name) as actual_operator_name,
                        COUNT(*) AS task_handle_num
                    FROM CRH_FUS.FOLLOWUPTASK f
                    JOIN operators o ON f.ACTUAL_OPERATOR_NO = o.OPERATOR_NO
                    WHERE f.TASK_STATUS = '50'
                    AND f.CREATE_DATETIME >= TRUNC(SYSDATE)
                    GROUP BY f.ACTUAL_OPERATOR_NO
                    ORDER BY task_handle_num DESC
                ) inner_query
                WHERE ROWNUM <= 8
            )
		]]></Content>
    </Func>


    <Func ID="MONITOR10201_page" MODULE_NAME="回访质检总体数据报表（营业部|分页）" TYPE="QUERY" DESC="回访质检总体数据报表（营业部|分页）">
        <InParam>
            <Param Code="distribute_datetime_start" Name="质检分配时间开始"/>
            <Param Code="distribute_datetime_end" Name="质检分配时间结束"/>
            <Param Code="qcr_finish_datetime_start" Name="质检完成时间开始"/>
            <Param Code="qcr_finish_datetime_end" Name="质检完成时间结束"/>
            <Param Code="quality_task_type" Name="任务类型"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="分公司"/>
            <Param Code="label_options" Name="标签"/>
            <Param Code="activity_name" Name="活动名称"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="channel_name" Name="渠道名称"/>
            <Param Code="marketing_team" Name="营销团队"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
        with page_data as (SELECT
            branch_no AS branch_no,
            count( CASE WHEN DISTRIBUTE_STATUS = '1' THEN 1 END ) AS distribute_count,
            count(*) AS task_count,
            count( CASE WHEN QUALITY_TASK_STATUS !='14' THEN 1 END ) AS pass_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' THEN 1 END ) AS no_pass_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE in ('1','2','3') THEN 1 END ) AS not_standard_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '1' THEN 1 END ) AS compliance_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '2' THEN 1 END ) AS important_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '3' THEN 1 END ) AS general_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '4' THEN 1 END ) AS other_questions_count
        FROM
            CRH_FUS.QCRTASK
        WHERE
            QCR_FINISH_DATETIME is not null
            <if condition="@distribute_datetime_start != null && @distribute_datetime_start.length() > 0">
			    AND DISTRIBUTE_DATETIME >= TO_DATE('@distribute_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND DISTRIBUTE_DATETIME <= TO_DATE('@distribute_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

            <if condition="@qcr_finish_datetime_start != null && @qcr_finish_datetime_start.length() > 0">
			    AND QCR_FINISH_DATETIME >= TO_DATE('@qcr_finish_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND QCR_FINISH_DATETIME <= TO_DATE('@qcr_finish_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

            <if condition="@up_branch_no != null && @up_branch_no.length() > 0">
			    and instr(','||'@up_branch_no'||',',','||up_branch_no||',')>0
			</if>

            <if condition="@branch_no != null && @branch_no.length() > 0">
			    and instr(','||'@branch_no'||',',','||branch_no||',')>0
			</if>

            <if condition="@label_options != null && @label_options.length() > 0">
			    and instr(','||'@label_options'||',',','||label_options||',')>0
			</if>

			<if condition="@activity_name != null && @activity_name.length() > 0">
			    AND ACTIVITY_NAME = '@activity_name'
			</if>

			<if condition="@channel_code != null && @channel_code.length() > 0">
			    AND CHANNEL_CODE = '@channel_code'
			</if>

			<if condition="@channel_name != null && @channel_name.length() > 0">
			    AND CHANNEL_NAME = '@channel_name'
			</if>

			<if condition="@marketing_team != null && @marketing_team.length() > 0">
			    AND MARKETING_TEAM = '@marketing_team'
			</if>


        GROUP BY
            branch_no
        ORDER BY
            branch_no DESC
        ),

        RownumData AS (
				SELECT
					b.*,
					ROUND(no_pass_count * 100.0 / NULLIF(task_count, 0), 2) as no_pass_rate,
					ROUND(not_standard_count * 100.0 / NULLIF(task_count, 0), 2) as not_standard_rate,
					ROUND(compliance_error_count * 100.0 / NULLIF(task_count, 0), 2) as compliance_error_rate,
					ROUND(important_error_count * 100.0 / NULLIF(task_count, 0), 2) as important_error_rate,
					ROUND(general_error_count * 100.0 / NULLIF(task_count, 0), 2) as general_error_rate,
					ROUND(other_questions_count * 100.0 / NULLIF(task_count, 0), 2) as other_questions_rate,
					rownum as rn
				FROM
					page_data b
			)

			SELECT
				*
			FROM RownumData r
			<if condition="@page_num != null && @page_size != null">
				WHERE r.rn BETWEEN ( @page_num - 1 ) * @page_size + 1 AND @page_num * @page_size
			</if>
        ]]></Content>
    </Func>

    <Func ID="MONITOR10201_sum" MODULE_NAME="回访质检总体数据报表（营业部|合计）" TYPE="QUERY" DESC="回访质检总体数据报表（营业部|合计）">
        <InParam>
            <Param Code="distribute_datetime_start" Name="质检分配时间开始"/>
            <Param Code="distribute_datetime_end" Name="质检分配时间结束"/>
            <Param Code="qcr_finish_datetime_start" Name="质检完成时间开始"/>
            <Param Code="qcr_finish_datetime_end" Name="质检完成时间结束"/>
            <Param Code="quality_task_type" Name="任务类型"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="分公司"/>
            <Param Code="label_options" Name="标签"/>
            <Param Code="activity_name" Name="活动名称"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="channel_name" Name="渠道名称"/>
            <Param Code="marketing_team" Name="营销团队"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
        with page_data as (SELECT
            '合计' AS branch_no,
            count( CASE WHEN DISTRIBUTE_STATUS = '1' THEN 1 END ) AS distribute_count,
            count(*) AS task_count,
            count( CASE WHEN QUALITY_TASK_STATUS !='14' THEN 1 END ) AS pass_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' THEN 1 END ) AS no_pass_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE in ('1','2','3') THEN 1 END ) AS not_standard_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '1' THEN 1 END ) AS compliance_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '2' THEN 1 END ) AS important_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '3' THEN 1 END ) AS general_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '4' THEN 1 END ) AS other_questions_count
        FROM
            CRH_FUS.QCRTASK
        WHERE
            QCR_FINISH_DATETIME is not null
            <if condition="@distribute_datetime_start != null && @distribute_datetime_start.length() > 0">
			    AND DISTRIBUTE_DATETIME >= TO_DATE('@distribute_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND DISTRIBUTE_DATETIME <= TO_DATE('@distribute_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

            <if condition="@qcr_finish_datetime_start != null && @qcr_finish_datetime_start.length() > 0">
			    AND QCR_FINISH_DATETIME >= TO_DATE('@qcr_finish_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND QCR_FINISH_DATETIME <= TO_DATE('@qcr_finish_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

            <if condition="@up_branch_no != null && @up_branch_no.length() > 0">
			    and instr(','||'@up_branch_no'||',',','||up_branch_no||',')>0
			</if>

            <if condition="@branch_no != null && @branch_no.length() > 0">
			    and instr(','||'@branch_no'||',',','||branch_no||',')>0
			</if>

            <if condition="@label_options != null && @label_options.length() > 0">
			    and instr(','||'@label_options'||',',','||label_options||',')>0
			</if>

			<if condition="@activity_name != null && @activity_name.length() > 0">
			    AND ACTIVITY_NAME = '@activity_name'
			</if>

			<if condition="@channel_code != null && @channel_code.length() > 0">
			    AND CHANNEL_CODE = '@channel_code'
			</if>

			<if condition="@channel_name != null && @channel_name.length() > 0">
			    AND CHANNEL_NAME = '@channel_name'
			</if>

			<if condition="@marketing_team != null && @marketing_team.length() > 0">
			    AND MARKETING_TEAM = '@marketing_team'
			</if>

        ),

        RownumData AS (
				SELECT
					b.*,
					ROUND(no_pass_count * 100.0 / NULLIF(task_count, 0), 2) as no_pass_rate,
					ROUND(not_standard_count * 100.0 / NULLIF(task_count, 0), 2) as not_standard_rate,
					ROUND(compliance_error_count * 100.0 / NULLIF(task_count, 0), 2) as compliance_error_rate,
					ROUND(important_error_count * 100.0 / NULLIF(task_count, 0), 2) as important_error_rate,
					ROUND(general_error_count * 100.0 / NULLIF(task_count, 0), 2) as general_error_rate,
					ROUND(other_questions_count * 100.0 / NULLIF(task_count, 0), 2) as other_questions_rate,
					rownum as rn
				FROM
					page_data b
			)

			SELECT
				*
			FROM RownumData r
        ]]></Content>
    </Func>

    <Func ID="MONITOR10201_total" MODULE_NAME="回访质检总体数据报表（营业部|总数）" TYPE="QUERY" DESC="回访质检总体数据报表（营业部|总数）">
        <InParam>
            <Param Code="distribute_datetime_start" Name="质检分配时间开始"/>
            <Param Code="distribute_datetime_end" Name="质检分配时间结束"/>
            <Param Code="qcr_finish_datetime_start" Name="质检完成时间开始"/>
            <Param Code="qcr_finish_datetime_end" Name="质检完成时间结束"/>
            <Param Code="quality_task_type" Name="任务类型"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="分公司"/>
            <Param Code="label_options" Name="标签"/>
            <Param Code="activity_name" Name="活动名称"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="channel_name" Name="渠道名称"/>
            <Param Code="marketing_team" Name="营销团队"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
        SELECT
            COUNT(DISTINCT branch_no) AS total
        FROM CRH_FUS.QCRTASK
        WHERE
            QCR_FINISH_DATETIME is not null
            <if condition="@distribute_datetime_start != null && @distribute_datetime_start.length() > 0">
			    AND DISTRIBUTE_DATETIME >= TO_DATE('@distribute_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND DISTRIBUTE_DATETIME <= TO_DATE('@distribute_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

            <if condition="@qcr_finish_datetime_start != null && @qcr_finish_datetime_start.length() > 0">
			    AND QCR_FINISH_DATETIME >= TO_DATE('@qcr_finish_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND QCR_FINISH_DATETIME <= TO_DATE('@qcr_finish_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

            <if condition="@up_branch_no != null && @up_branch_no.length() > 0">
			    and instr(','||'@up_branch_no'||',',','||up_branch_no||',')>0
			</if>

            <if condition="@branch_no != null && @branch_no.length() > 0">
			    and instr(','||'@branch_no'||',',','||branch_no||',')>0
			</if>

            <if condition="@label_options != null && @label_options.length() > 0">
			    and instr(','||'@label_options'||',',','||label_options||',')>0
			</if>

			<if condition="@activity_name != null && @activity_name.length() > 0">
			    AND ACTIVITY_NAME = '@activity_name'
			</if>

			<if condition="@channel_code != null && @channel_code.length() > 0">
			    AND CHANNEL_CODE = '@channel_code'
			</if>

			<if condition="@channel_name != null && @channel_name.length() > 0">
			    AND CHANNEL_NAME = '@channel_name'
			</if>

			<if condition="@marketing_team != null && @marketing_team.length() > 0">
			    AND MARKETING_TEAM = '@marketing_team'
			</if>
        ]]></Content>
    </Func>


    <Func ID="MONITOR10202_page" MODULE_NAME="回访质检总体数据报表（分公司|分页）" TYPE="QUERY" DESC="回访质检总体数据报表（分公司|分页）">
        <InParam>
            <Param Code="distribute_datetime_start" Name="质检分配时间开始"/>
            <Param Code="distribute_datetime_end" Name="质检分配时间结束"/>
            <Param Code="qcr_finish_datetime_start" Name="质检完成时间开始"/>
            <Param Code="qcr_finish_datetime_end" Name="质检完成时间结束"/>
            <Param Code="quality_task_type" Name="任务类型"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="分公司"/>
            <Param Code="label_options" Name="标签"/>
            <Param Code="activity_name" Name="活动名称"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="channel_name" Name="渠道名称"/>
            <Param Code="marketing_team" Name="营销团队"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
        with page_data as (SELECT
            up_branch_no AS up_branch_no,
            count( CASE WHEN DISTRIBUTE_STATUS = '1' THEN 1 END ) AS distribute_count,
            count(*) AS task_count,
            count( CASE WHEN QUALITY_TASK_STATUS !='14' THEN 1 END ) AS pass_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' THEN 1 END ) AS no_pass_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE in ('1','2','3') THEN 1 END ) AS not_standard_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '1' THEN 1 END ) AS compliance_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '2' THEN 1 END ) AS important_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '3' THEN 1 END ) AS general_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '4' THEN 1 END ) AS other_questions_count
        FROM
            CRH_FUS.QCRTASK
        WHERE
            QCR_FINISH_DATETIME is not null
            <if condition="@distribute_datetime_start != null && @distribute_datetime_start.length() > 0">
			    AND DISTRIBUTE_DATETIME >= TO_DATE('@distribute_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND DISTRIBUTE_DATETIME <= TO_DATE('@distribute_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

            <if condition="@qcr_finish_datetime_start != null && @qcr_finish_datetime_start.length() > 0">
			    AND QCR_FINISH_DATETIME >= TO_DATE('@qcr_finish_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND QCR_FINISH_DATETIME <= TO_DATE('@qcr_finish_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

            <if condition="@up_branch_no != null && @up_branch_no.length() > 0">
			    and instr(','||'@up_branch_no'||',',','||up_branch_no||',')>0
			</if>

            <if condition="@branch_no != null && @branch_no.length() > 0">
			    and instr(','||'@branch_no'||',',','||branch_no||',')>0
			</if>

            <if condition="@label_options != null && @label_options.length() > 0">
			    and instr(','||'@label_options'||',',','||label_options||',')>0
			</if>

			<if condition="@activity_name != null && @activity_name.length() > 0">
			    AND ACTIVITY_NAME = '@activity_name'
			</if>

			<if condition="@channel_code != null && @channel_code.length() > 0">
			    AND CHANNEL_CODE = '@channel_code'
			</if>

			<if condition="@channel_name != null && @channel_name.length() > 0">
			    AND CHANNEL_NAME = '@channel_name'
			</if>

			<if condition="@marketing_team != null && @marketing_team.length() > 0">
			    AND MARKETING_TEAM = '@marketing_team'
			</if>


        GROUP BY
            up_branch_no
        ORDER BY
            up_branch_no DESC
        ),

        RownumData AS (
				SELECT
					b.*,
					ROUND(no_pass_count * 100.0 / NULLIF(task_count, 0), 2) as no_pass_rate,
					ROUND(not_standard_count * 100.0 / NULLIF(task_count, 0), 2) as not_standard_rate,
					ROUND(compliance_error_count * 100.0 / NULLIF(task_count, 0), 2) as compliance_error_rate,
					ROUND(important_error_count * 100.0 / NULLIF(task_count, 0), 2) as important_error_rate,
					ROUND(general_error_count * 100.0 / NULLIF(task_count, 0), 2) as general_error_rate,
					ROUND(other_questions_count * 100.0 / NULLIF(task_count, 0), 2) as other_questions_rate,
					rownum as rn
				FROM
					page_data b
			)

			SELECT
				*
			FROM RownumData r
			<if condition="@page_num != null && @page_size != null">
				WHERE r.rn BETWEEN ( @page_num - 1 ) * @page_size + 1 AND @page_num * @page_size
			</if>
        ]]></Content>
    </Func>

    <Func ID="MONITOR10202_sum" MODULE_NAME="回访质检总体数据报表（分公司|合计）" TYPE="QUERY" DESC="回访质检总体数据报表（分公司|合计）">
        <InParam>
            <Param Code="distribute_datetime_start" Name="质检分配时间开始"/>
            <Param Code="distribute_datetime_end" Name="质检分配时间结束"/>
            <Param Code="qcr_finish_datetime_start" Name="质检完成时间开始"/>
            <Param Code="qcr_finish_datetime_end" Name="质检完成时间结束"/>
            <Param Code="quality_task_type" Name="任务类型"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="分公司"/>
            <Param Code="label_options" Name="标签"/>
            <Param Code="activity_name" Name="活动名称"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="channel_name" Name="渠道名称"/>
            <Param Code="marketing_team" Name="营销团队"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
        with page_data as (SELECT
            '合计' AS up_branch_no,
            count( CASE WHEN DISTRIBUTE_STATUS = '1' THEN 1 END ) AS distribute_count,
            count(*) AS task_count,
            count( CASE WHEN QUALITY_TASK_STATUS !='14' THEN 1 END ) AS pass_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' THEN 1 END ) AS no_pass_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE in ('1','2','3') THEN 1 END ) AS not_standard_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '1' THEN 1 END ) AS compliance_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '2' THEN 1 END ) AS important_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '3' THEN 1 END ) AS general_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '4' THEN 1 END ) AS other_questions_count
        FROM
            CRH_FUS.QCRTASK
        WHERE
            QCR_FINISH_DATETIME is not null
            <if condition="@distribute_datetime_start != null && @distribute_datetime_start.length() > 0">
			    AND DISTRIBUTE_DATETIME >= TO_DATE('@distribute_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND DISTRIBUTE_DATETIME <= TO_DATE('@distribute_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

            <if condition="@qcr_finish_datetime_start != null && @qcr_finish_datetime_start.length() > 0">
			    AND QCR_FINISH_DATETIME >= TO_DATE('@qcr_finish_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND QCR_FINISH_DATETIME <= TO_DATE('@qcr_finish_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

            <if condition="@up_branch_no != null && @up_branch_no.length() > 0">
			    and instr(','||'@up_branch_no'||',',','||up_branch_no||',')>0
			</if>

            <if condition="@branch_no != null && @branch_no.length() > 0">
			    and instr(','||'@branch_no'||',',','||branch_no||',')>0
			</if>

            <if condition="@label_options != null && @label_options.length() > 0">
			    and instr(','||'@label_options'||',',','||label_options||',')>0
			</if>

			<if condition="@activity_name != null && @activity_name.length() > 0">
			    AND ACTIVITY_NAME = '@activity_name'
			</if>

			<if condition="@channel_code != null && @channel_code.length() > 0">
			    AND CHANNEL_CODE = '@channel_code'
			</if>

			<if condition="@channel_name != null && @channel_name.length() > 0">
			    AND CHANNEL_NAME = '@channel_name'
			</if>

			<if condition="@marketing_team != null && @marketing_team.length() > 0">
			    AND MARKETING_TEAM = '@marketing_team'
			</if>

        ),

        RownumData AS (
				SELECT
					b.*,
					ROUND(no_pass_count * 100.0 / NULLIF(task_count, 0), 2) as no_pass_rate,
					ROUND(not_standard_count * 100.0 / NULLIF(task_count, 0), 2) as not_standard_rate,
					ROUND(compliance_error_count * 100.0 / NULLIF(task_count, 0), 2) as compliance_error_rate,
					ROUND(important_error_count * 100.0 / NULLIF(task_count, 0), 2) as important_error_rate,
					ROUND(general_error_count * 100.0 / NULLIF(task_count, 0), 2) as general_error_rate,
					ROUND(other_questions_count * 100.0 / NULLIF(task_count, 0), 2) as other_questions_rate,
					rownum as rn
				FROM
					page_data b
			)

			SELECT
				*
			FROM RownumData r
        ]]></Content>
    </Func>

    <Func ID="MONITOR10202_total" MODULE_NAME="回访质检总体数据报表（分公司|总数）" TYPE="QUERY" DESC="回访质检总体数据报表（分公司|总数）">
        <InParam>
            <Param Code="distribute_datetime_start" Name="质检分配时间开始"/>
            <Param Code="distribute_datetime_end" Name="质检分配时间结束"/>
            <Param Code="qcr_finish_datetime_start" Name="质检完成时间开始"/>
            <Param Code="qcr_finish_datetime_end" Name="质检完成时间结束"/>
            <Param Code="quality_task_type" Name="任务类型"/>
            <Param Code="branch_no" Name="所属营业部"/>
            <Param Code="up_branch_no" Name="分公司"/>
            <Param Code="label_options" Name="标签"/>
            <Param Code="activity_name" Name="活动名称"/>
            <Param Code="channel_code" Name="渠道码"/>
            <Param Code="channel_name" Name="渠道名称"/>
            <Param Code="marketing_team" Name="营销团队"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
        SELECT
            COUNT(DISTINCT up_branch_no) AS total
        FROM CRH_FUS.QCRTASK
        WHERE
            QCR_FINISH_DATETIME is not null
            <if condition="@distribute_datetime_start != null && @distribute_datetime_start.length() > 0">
			    AND DISTRIBUTE_DATETIME >= TO_DATE('@distribute_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND DISTRIBUTE_DATETIME <= TO_DATE('@distribute_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

            <if condition="@qcr_finish_datetime_start != null && @qcr_finish_datetime_start.length() > 0">
			    AND QCR_FINISH_DATETIME >= TO_DATE('@qcr_finish_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND QCR_FINISH_DATETIME <= TO_DATE('@qcr_finish_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

            <if condition="@up_branch_no != null && @up_branch_no.length() > 0">
			    and instr(','||'@up_branch_no'||',',','||up_branch_no||',')>0
			</if>

            <if condition="@branch_no != null && @branch_no.length() > 0">
			    and instr(','||'@branch_no'||',',','||branch_no||',')>0
			</if>

            <if condition="@label_options != null && @label_options.length() > 0">
			    and instr(','||'@label_options'||',',','||label_options||',')>0
			</if>

			<if condition="@activity_name != null && @activity_name.length() > 0">
			    AND ACTIVITY_NAME = '@activity_name'
			</if>

			<if condition="@channel_code != null && @channel_code.length() > 0">
			    AND CHANNEL_CODE = '@channel_code'
			</if>

			<if condition="@channel_name != null && @channel_name.length() > 0">
			    AND CHANNEL_NAME = '@channel_name'
			</if>

			<if condition="@marketing_team != null && @marketing_team.length() > 0">
			    AND MARKETING_TEAM = '@marketing_team'
			</if>
        ]]></Content>
    </Func>


    <Func ID="MONITOR10203_page" MODULE_NAME="个人质检数据报表（初检人|分页）" TYPE="QUERY" DESC="个人质检数据报表（初检人|分页）">
        <InParam>
            <Param Code="distribute_datetime_start" Name="质检分配时间开始"/>
            <Param Code="distribute_datetime_end" Name="质检分配时间结束"/>
            <Param Code="qcr_finish_datetime_start" Name="质检完成时间开始"/>
            <Param Code="qcr_finish_datetime_end" Name="质检完成时间结束"/>
            <Param Code="quality_operator_name" Name="初检人姓名"/>
            <Param Code="quality_operator_no" Name="初检人工号"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
        with page_data as (SELECT
            quality_operator_no AS quality_operator_no,
            MAX(quality_operator_name) AS quality_operator_name,
            count(*) AS task_count,
            count( CASE WHEN QUALITY_TASK_STATUS !='14' THEN 1 END ) AS pass_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' THEN 1 END ) AS no_pass_count,
            count( CASE WHEN QUALITY_FAIL_REASON_ID !=' ' AND REVIEW_FAIL_REASON_ID !=' ' AND QUALITY_FAIL_REASON_ID != REVIEW_FAIL_REASON_ID THEN 1 END ) AS misjudgment_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '1' THEN 1 END ) AS compliance_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '2' THEN 1 END ) AS important_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '3' THEN 1 END ) AS general_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '4' THEN 1 END ) AS other_questions_count
        FROM
            CRH_FUS.QCRTASK
        WHERE
            QCR_FINISH_DATETIME is not null
            <if condition="@distribute_datetime_start != null && @distribute_datetime_start.length() > 0">
			    AND DISTRIBUTE_DATETIME >= TO_DATE('@distribute_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND DISTRIBUTE_DATETIME <= TO_DATE('@distribute_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

            <if condition="@qcr_finish_datetime_start != null && @qcr_finish_datetime_start.length() > 0">
			    AND QCR_FINISH_DATETIME >= TO_DATE('@qcr_finish_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND QCR_FINISH_DATETIME <= TO_DATE('@qcr_finish_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

			<if condition="@quality_operator_no != null && @quality_operator_no.length() > 0">
			    AND QUALITY_OPERATOR_NO = '@quality_operator_no'
			</if>

			<if condition="@quality_operator_name != null && @quality_operator_name.length() > 0">
			    AND QUALITY_OPERATOR_NAME = '@quality_operator_name'
			</if>

        GROUP BY
            quality_operator_no
        ORDER BY
            quality_operator_no DESC
        ),

        RownumData AS (
				SELECT
					b.*,
					ROUND(no_pass_count * 100.0 / NULLIF(task_count, 0), 2) as no_pass_rate,
					ROUND(compliance_error_count * 100.0 / NULLIF(task_count, 0), 2) as compliance_error_rate,
					ROUND(important_error_count * 100.0 / NULLIF(task_count, 0), 2) as important_error_rate,
					ROUND(general_error_count * 100.0 / NULLIF(task_count, 0), 2) as general_error_rate,
					ROUND(other_questions_count * 100.0 / NULLIF(task_count, 0), 2) as other_questions_rate,
					rownum as rn
				FROM
					page_data b
			)

			SELECT
				*
			FROM RownumData r
			<if condition="@page_num != null && @page_size != null">
				WHERE r.rn BETWEEN ( @page_num - 1 ) * @page_size + 1 AND @page_num * @page_size
			</if>
        ]]></Content>
    </Func>

    <Func ID="MONITOR10203_sum" MODULE_NAME="个人质检数据报表（初检人|合计）" TYPE="QUERY" DESC="个人质检数据报表（初检人|合计）">
        <InParam>
            <Param Code="distribute_datetime_start" Name="质检分配时间开始"/>
            <Param Code="distribute_datetime_end" Name="质检分配时间结束"/>
            <Param Code="qcr_finish_datetime_start" Name="质检完成时间开始"/>
            <Param Code="qcr_finish_datetime_end" Name="质检完成时间结束"/>
            <Param Code="quality_operator_name" Name="初检人姓名"/>
            <Param Code="quality_operator_no" Name="初检人工号"/>
        </InParam>
        <Content><![CDATA[
        with page_data as (SELECT
            '合计' AS quality_operator_no,
            '-' AS quality_operator_name,
            count(*) AS task_count,
            count( CASE WHEN QUALITY_TASK_STATUS !='14' THEN 1 END ) AS pass_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' THEN 1 END ) AS no_pass_count,
            count( CASE WHEN QUALITY_FAIL_REASON_ID !=' ' AND REVIEW_FAIL_REASON_ID !=' ' AND QUALITY_FAIL_REASON_ID != REVIEW_FAIL_REASON_ID THEN 1 END ) AS misjudgment_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '1' THEN 1 END ) AS compliance_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '2' THEN 1 END ) AS important_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '3' THEN 1 END ) AS general_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '4' THEN 1 END ) AS other_questions_count
        FROM
            CRH_FUS.QCRTASK
        WHERE
            QCR_FINISH_DATETIME is not null
            <if condition="@distribute_datetime_start != null && @distribute_datetime_start.length() > 0">
			    AND DISTRIBUTE_DATETIME >= TO_DATE('@distribute_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND DISTRIBUTE_DATETIME <= TO_DATE('@distribute_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

            <if condition="@qcr_finish_datetime_start != null && @qcr_finish_datetime_start.length() > 0">
			    AND QCR_FINISH_DATETIME >= TO_DATE('@qcr_finish_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND QCR_FINISH_DATETIME <= TO_DATE('@qcr_finish_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

			<if condition="@quality_operator_no != null && @quality_operator_no.length() > 0">
			    AND QUALITY_OPERATOR_NO = '@quality_operator_no'
			</if>

			<if condition="@quality_operator_name != null && @quality_operator_name.length() > 0">
			    AND QUALITY_OPERATOR_NAME = '@quality_operator_name'
			</if>

        ),

        RownumData AS (
				SELECT
					b.*,
					ROUND(no_pass_count * 100.0 / NULLIF(task_count, 0), 2) as no_pass_rate,
					ROUND(compliance_error_count * 100.0 / NULLIF(task_count, 0), 2) as compliance_error_rate,
					ROUND(important_error_count * 100.0 / NULLIF(task_count, 0), 2) as important_error_rate,
					ROUND(general_error_count * 100.0 / NULLIF(task_count, 0), 2) as general_error_rate,
					ROUND(other_questions_count * 100.0 / NULLIF(task_count, 0), 2) as other_questions_rate,
					rownum as rn
				FROM
					page_data b
			)

			SELECT
				*
			FROM RownumData r
        ]]></Content>
    </Func>

    <Func ID="MONITOR10203_total" MODULE_NAME="个人质检数据报表（初检人|总数）" TYPE="QUERY" DESC="个人质检数据报表（初检人|总数）">
        <InParam>
            <Param Code="distribute_datetime_start" Name="质检分配时间开始"/>
            <Param Code="distribute_datetime_end" Name="质检分配时间结束"/>
            <Param Code="qcr_finish_datetime_start" Name="质检完成时间开始"/>
            <Param Code="qcr_finish_datetime_end" Name="质检完成时间结束"/>
            <Param Code="quality_operator_name" Name="初检人姓名"/>
            <Param Code="quality_operator_no" Name="初检人工号"/>
        </InParam>
        <Content><![CDATA[
        SELECT
            COUNT(DISTINCT quality_operator_no) AS total
        FROM
            CRH_FUS.QCRTASK
        WHERE
            QCR_FINISH_DATETIME is not null
            <if condition="@distribute_datetime_start != null && @distribute_datetime_start.length() > 0">
			    AND DISTRIBUTE_DATETIME >= TO_DATE('@distribute_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND DISTRIBUTE_DATETIME <= TO_DATE('@distribute_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

            <if condition="@qcr_finish_datetime_start != null && @qcr_finish_datetime_start.length() > 0">
			    AND QCR_FINISH_DATETIME >= TO_DATE('@qcr_finish_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND QCR_FINISH_DATETIME <= TO_DATE('@qcr_finish_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

			<if condition="@quality_operator_no != null && @quality_operator_no.length() > 0">
			    AND QUALITY_OPERATOR_NO = '@quality_operator_no'
			</if>

			<if condition="@quality_operator_name != null && @quality_operator_name.length() > 0">
			    AND QUALITY_OPERATOR_NAME = '@quality_operator_name'
			</if>
        ]]></Content>
    </Func>


    <Func ID="MONITOR10204_page" MODULE_NAME="个人质检数据报表（复检人|分页）" TYPE="QUERY" DESC="个人质检数据报表（复检人|分页）">
        <InParam>
            <Param Code="distribute_datetime_start" Name="质检分配时间开始"/>
            <Param Code="distribute_datetime_end" Name="质检分配时间结束"/>
            <Param Code="qcr_finish_datetime_start" Name="质检完成时间开始"/>
            <Param Code="qcr_finish_datetime_end" Name="质检完成时间结束"/>
            <Param Code="review_quality_operator_name" Name="复检人姓名"/>
            <Param Code="review_quality_operator_no" Name="复检人工号"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
        with page_data as (SELECT
            review_quality_operator_no AS review_quality_operator_no,
            MAX(review_quality_operator_name) AS review_quality_operator_name,
            count(*) AS task_count,
            count( CASE WHEN QUALITY_TASK_STATUS !='14' THEN 1 END ) AS pass_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' THEN 1 END ) AS no_pass_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '1' THEN 1 END ) AS compliance_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '2' THEN 1 END ) AS important_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '3' THEN 1 END ) AS general_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '4' THEN 1 END ) AS other_questions_count
        FROM
            CRH_FUS.QCRTASK
        WHERE
            QCR_FINISH_DATETIME is not null
            and review_quality_operator_no != ' '
            <if condition="@distribute_datetime_start != null && @distribute_datetime_start.length() > 0">
			    AND DISTRIBUTE_DATETIME >= TO_DATE('@distribute_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND DISTRIBUTE_DATETIME <= TO_DATE('@distribute_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

            <if condition="@qcr_finish_datetime_start != null && @qcr_finish_datetime_start.length() > 0">
			    AND QCR_FINISH_DATETIME >= TO_DATE('@qcr_finish_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND QCR_FINISH_DATETIME <= TO_DATE('@qcr_finish_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

			<if condition="@review_quality_operator_no != null && @review_quality_operator_no.length() > 0">
			    AND REVIEW_QUALITY_OPERATOR_NO = '@review_quality_operator_no'
			</if>

			<if condition="@review_quality_operator_name != null && @review_quality_operator_name.length() > 0">
			    AND REVIEW_QUALITY_OPERATOR_NAME = '@review_quality_operator_name'
			</if>

        GROUP BY
            review_quality_operator_no
        ORDER BY
            review_quality_operator_no DESC
        ),

        RownumData AS (
				SELECT
					b.*,
					ROUND(no_pass_count * 100.0 / NULLIF(task_count, 0), 2) as no_pass_rate,
					ROUND(compliance_error_count * 100.0 / NULLIF(task_count, 0), 2) as compliance_error_rate,
					ROUND(important_error_count * 100.0 / NULLIF(task_count, 0), 2) as important_error_rate,
					ROUND(general_error_count * 100.0 / NULLIF(task_count, 0), 2) as general_error_rate,
					ROUND(other_questions_count * 100.0 / NULLIF(task_count, 0), 2) as other_questions_rate,
					rownum as rn
				FROM
					page_data b
			)

			SELECT
				*
			FROM RownumData r
			<if condition="@page_num != null && @page_size != null">
				WHERE r.rn BETWEEN ( @page_num - 1 ) * @page_size + 1 AND @page_num * @page_size
			</if>
        ]]></Content>
    </Func>

    <Func ID="MONITOR10204_sum" MODULE_NAME="个人质检数据报表（复检人|合计）" TYPE="QUERY" DESC="个人质检数据报表（复检人|合计）">
        <InParam>
            <Param Code="distribute_datetime_start" Name="质检分配时间开始"/>
            <Param Code="distribute_datetime_end" Name="质检分配时间结束"/>
            <Param Code="qcr_finish_datetime_start" Name="质检完成时间开始"/>
            <Param Code="qcr_finish_datetime_end" Name="质检完成时间结束"/>
            <Param Code="review_quality_operator_name" Name="复检人姓名"/>
            <Param Code="review_quality_operator_no" Name="复检人工号"/>
        </InParam>
        <Content><![CDATA[
        with page_data as (SELECT
            '合计' AS review_quality_operator_no,
            '-' AS review_quality_operator_name,
            count(*) AS task_count,
            count( CASE WHEN QUALITY_TASK_STATUS !='14' THEN 1 END ) AS pass_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' THEN 1 END ) AS no_pass_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '1' THEN 1 END ) AS compliance_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '2' THEN 1 END ) AS important_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '3' THEN 1 END ) AS general_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '4' THEN 1 END ) AS other_questions_count
        FROM
            CRH_FUS.QCRTASK
        WHERE
            QCR_FINISH_DATETIME is not null
            <if condition="@distribute_datetime_start != null && @distribute_datetime_start.length() > 0">
			    AND DISTRIBUTE_DATETIME >= TO_DATE('@distribute_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND DISTRIBUTE_DATETIME <= TO_DATE('@distribute_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

            <if condition="@qcr_finish_datetime_start != null && @qcr_finish_datetime_start.length() > 0">
			    AND QCR_FINISH_DATETIME >= TO_DATE('@qcr_finish_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND QCR_FINISH_DATETIME <= TO_DATE('@qcr_finish_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

			<if condition="@review_quality_operator_no != null && @review_quality_operator_no.length() > 0">
			    AND REVIEW_QUALITY_OPERATOR_NO = '@review_quality_operator_no'
			</if>

			<if condition="@review_quality_operator_name != null && @review_quality_operator_name.length() > 0">
			    AND REVIEW_QUALITY_OPERATOR_NAME = '@review_quality_operator_name'
			</if>

        ),

        RownumData AS (
				SELECT
					b.*,
					ROUND(no_pass_count * 100.0 / NULLIF(task_count, 0), 2) as no_pass_rate,
					ROUND(compliance_error_count * 100.0 / NULLIF(task_count, 0), 2) as compliance_error_rate,
					ROUND(important_error_count * 100.0 / NULLIF(task_count, 0), 2) as important_error_rate,
					ROUND(general_error_count * 100.0 / NULLIF(task_count, 0), 2) as general_error_rate,
					ROUND(other_questions_count * 100.0 / NULLIF(task_count, 0), 2) as other_questions_rate,
					rownum as rn
				FROM
					page_data b
			)

			SELECT
				*
			FROM RownumData r
        ]]></Content>
    </Func>

    <Func ID="MONITOR10204_total" MODULE_NAME="个人质检数据报表（复检人|总数）" TYPE="QUERY" DESC="个人质检数据报表（复检人|总数）">
        <InParam>
            <Param Code="distribute_datetime_start" Name="质检分配时间开始"/>
            <Param Code="distribute_datetime_end" Name="质检分配时间结束"/>
            <Param Code="qcr_finish_datetime_start" Name="质检完成时间开始"/>
            <Param Code="qcr_finish_datetime_end" Name="质检完成时间结束"/>
            <Param Code="review_quality_operator_name" Name="复检人姓名"/>
            <Param Code="review_quality_operator_no" Name="复检人工号"/>
        </InParam>
        <Content><![CDATA[
        SELECT
            COUNT(DISTINCT review_quality_operator_no) AS total
        FROM
            CRH_FUS.QCRTASK
        WHERE
            QCR_FINISH_DATETIME is not null
            <if condition="@distribute_datetime_start != null && @distribute_datetime_start.length() > 0">
			    AND DISTRIBUTE_DATETIME >= TO_DATE('@distribute_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND DISTRIBUTE_DATETIME <= TO_DATE('@distribute_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

            <if condition="@qcr_finish_datetime_start != null && @qcr_finish_datetime_start.length() > 0">
			    AND QCR_FINISH_DATETIME >= TO_DATE('@qcr_finish_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND QCR_FINISH_DATETIME <= TO_DATE('@qcr_finish_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

			<if condition="@review_quality_operator_no != null && @review_quality_operator_no.length() > 0">
			    AND REVIEW_QUALITY_OPERATOR_NO = '@review_quality_operator_no'
			</if>

			<if condition="@review_quality_operator_name != null && @review_quality_operator_name.length() > 0">
			    AND REVIEW_QUALITY_OPERATOR_NAME = '@review_quality_operator_name'
			</if>
        ]]></Content>
    </Func>


    <Func ID="MONITOR10205_page" MODULE_NAME="个人回访质量报表|分页" TYPE="QUERY" DESC="个人回访质量报表|分页">
        <InParam>
            <Param Code="qcr_finish_datetime_start" Name="质检完成时间开始"/>
            <Param Code="qcr_finish_datetime_end" Name="质检完成时间结束"/>
            <Param Code="last_operator_name" Name="被检人姓名"/>
            <Param Code="last_operator_no" Name="被检人工号"/>
            <Param Code="quality_task_source" Name="抽检类型"/>
            <Param Code="page_num" Name="页号"/>
            <Param Code="page_size" Name="每页记录数"/>
        </InParam>
        <Content><![CDATA[
        with page_data as (SELECT
            last_operator_no AS last_operator_no,
            MAX(last_operator_name) AS last_operator_name,
            count(*) AS task_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '1' THEN 1 END ) AS compliance_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '2' THEN 1 END ) AS important_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '3' THEN 1 END ) AS general_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '4' THEN 1 END ) AS other_questions_count
        FROM
            CRH_FUS.QCRTASK
        WHERE
            QCR_FINISH_DATETIME is not null

            <if condition="@qcr_finish_datetime_start != null && @qcr_finish_datetime_start.length() > 0">
			    AND QCR_FINISH_DATETIME >= TO_DATE('@qcr_finish_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND QCR_FINISH_DATETIME <= TO_DATE('@qcr_finish_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

			<if condition="@last_operator_no != null && @last_operator_no.length() > 0">
			    AND LAST_OPERATOR_NO = '@last_operator_no'
			</if>

			<if condition="@last_operator_name != null && @last_operator_name.length() > 0">
			    AND LAST_OPERATOR_NAME = '@last_operator_name'
			</if>

			<if condition="@quality_task_source != null && @quality_task_source.length() > 0">
			    AND QUALITY_TASK_SOURCE = '@quality_task_source'
			</if>

        GROUP BY
            last_operator_no
        ORDER BY
            last_operator_no DESC
        ),

        RownumData AS (
				SELECT
					b.*,
					rownum as rn
				FROM
					page_data b
			)

			SELECT
				*
			FROM RownumData r
			<if condition="@page_num != null && @page_size != null">
				WHERE r.rn BETWEEN ( @page_num - 1 ) * @page_size + 1 AND @page_num * @page_size
			</if>
        ]]></Content>
    </Func>

    <Func ID="MONITOR10205_sum" MODULE_NAME="个人回访质量报表|合计" TYPE="QUERY" DESC="个人回访质量报表|合计">
        <InParam>
            <Param Code="qcr_finish_datetime_start" Name="质检完成时间开始"/>
            <Param Code="qcr_finish_datetime_end" Name="质检完成时间结束"/>
            <Param Code="last_operator_name" Name="被检人姓名"/>
            <Param Code="last_operator_no" Name="被检人工号"/>
            <Param Code="quality_task_source" Name="抽检类型"/>
        </InParam>
        <Content><![CDATA[
        with page_data as (SELECT
            '合计' AS last_operator_no,
            '-' AS last_operator_name,
            count(*) AS task_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '1' THEN 1 END ) AS compliance_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '2' THEN 1 END ) AS important_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '3' THEN 1 END ) AS general_error_count,
            count( CASE WHEN QUALITY_TASK_STATUS ='14' AND QCR_FAIL_REASON_TYPE = '4' THEN 1 END ) AS other_questions_count
        FROM
            CRH_FUS.QCRTASK
        WHERE
            QCR_FINISH_DATETIME is not null

            <if condition="@qcr_finish_datetime_start != null && @qcr_finish_datetime_start.length() > 0">
			    AND QCR_FINISH_DATETIME >= TO_DATE('@qcr_finish_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND QCR_FINISH_DATETIME <= TO_DATE('@qcr_finish_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

			<if condition="@last_operator_no != null && @last_operator_no.length() > 0">
			    AND LAST_OPERATOR_NO = '@last_operator_no'
			</if>

			<if condition="@last_operator_name != null && @last_operator_name.length() > 0">
			    AND LAST_OPERATOR_NAME = '@last_operator_name'
			</if>

			<if condition="@quality_task_source != null && @quality_task_source.length() > 0">
			    AND QUALITY_TASK_SOURCE = '@quality_task_source'
			</if>

        ),

        RownumData AS (
				SELECT
					b.*,
					rownum as rn
				FROM
					page_data b
			)

			SELECT
				*
			FROM RownumData r
        ]]></Content>
    </Func>

    <Func ID="MONITOR10205_total" MODULE_NAME="个人回访质量报表|总数" TYPE="QUERY" DESC="个人回访质量报表|总数">
        <InParam>
            <Param Code="qcr_finish_datetime_start" Name="质检完成时间开始"/>
            <Param Code="qcr_finish_datetime_end" Name="质检完成时间结束"/>
            <Param Code="last_operator_name" Name="被检人姓名"/>
            <Param Code="last_operator_no" Name="被检人工号"/>
            <Param Code="quality_task_source" Name="抽检类型"/>
        </InParam>
        <Content><![CDATA[
        SELECT
            COUNT(DISTINCT last_operator_no) AS total
        FROM
            CRH_FUS.QCRTASK
        WHERE
            QCR_FINISH_DATETIME is not null

            <if condition="@qcr_finish_datetime_start != null && @qcr_finish_datetime_start.length() > 0">
			    AND QCR_FINISH_DATETIME >= TO_DATE('@qcr_finish_datetime_start', 'YYYY-MM-DD HH24:MI:SS')
                AND QCR_FINISH_DATETIME <= TO_DATE('@qcr_finish_datetime_end', 'YYYY-MM-DD HH24:MI:SS')
			</if>

			<if condition="@last_operator_no != null && @last_operator_no.length() > 0">
			    AND LAST_OPERATOR_NO = '@last_operator_no'
			</if>

			<if condition="@last_operator_name != null && @last_operator_name.length() > 0">
			    AND LAST_OPERATOR_NAME = '@last_operator_name'
			</if>

			<if condition="@quality_task_source != null && @quality_task_source.length() > 0">
			    AND QUALITY_TASK_SOURCE = '@quality_task_source'
			</if>
        ]]></Content>
    </Func>

</Package>