<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cairh.cpe.monitor.backend.mapper.AdsReportMapper">

    <select id="getAcOperatorInfoList" resultType="com.cairh.cpe.common.entity.AcOperatorInfo">
        select a.staff_no,
               a.branch_no,
               b.up_branch_no
        from crh_user.operatorinfo a,
             crh_user.allbranch b
        where instr(',' || a.en_roles || ',', ',1,') > 0
          and a.branch_no = b.branch_no
    </select>

    <select id="getUnDealTaskCount" resultType="java.lang.Integer">
        select count(1)
        from crh_ads.dispatchtask
        where dispatch_status in ('0', '1', '6')
          and subsys_id = '24'
    </select>

    <select id="getAcOperatorTaskCount" resultType="com.cairh.cpe.common.entity.AcOperatorTaskCount">
        select operator_no as staff_no,
               count(1)    as num
        from crh_ads.dispatchtask
        where dispatch_status = '3'
          and subsys_id = '24'
          and finish_datetime > (sysdate - ${unitTime} / 1440)
        group by operator_no
        order by count(1) desc
    </select>

    <select id="getTaskCountPerSecond" resultType="java.lang.Integer">
        select count(1)
        from crh_ads.dispatchtask
        where dispatch_status = '3'
          and subsys_id = '24'
          and finish_datetime > (sysdate - ${unitTime} / 1440)
    </select>

    <select id="getAcOperatorTodayTaskCount" resultType="com.cairh.cpe.common.entity.AcOperatorTodayTaskCount">
        select operator_no                       as staff_no,
               operator_name,
               count(1)                          as total_num,
               sum(decode(task_type, '5', 1, 0)) as audit_num,
               sum(decode(task_type, '5', 0, 1)) as review_num
        from crh_ads.dispatchtask
        where dispatch_status = '3'
          and subsys_id = '24'
          and trunc(finish_datetime) >= trunc(sysdate)
        group by operator_no, operator_name
        order by count(1) desc
    </select>

    <select id="getLevelDispatchTask" resultType="com.cairh.cpe.monitor.backend.entity.LevelDispatchTask">
        select a.task_id,
               a.id_no,
               a.mobile_tel,
               a.queue_level,
               a.task_type,
               a.top_pos,
               b.serial_id
        from crh_ads.dispatchtask a,
             (select *
              from crh_user.whitelist b
              where b.whitelist_type = '2'
                and b.valid_end_date >= to_char(sysdate, 'yyyymmdd')
                and b.status = '8') b
        where a.dispatch_status = '6'
          and a.subsys_id = '24'
          and a.id_no = b.id_no(+)
          and a.id_kind = b.id_kind(+)
          and a.mobile_tel = b.mobile_tel(+)
    </select>

    <select id="getFinshDispatchTaskWithFailCount"
            resultType="com.cairh.cpe.monitor.backend.entity.FinshDispatchTaskWithFailCount">
        select count(dispatch_fail_count) as dispatch_fail_count_task_count,
               max(dispatch_fail_count)   as dispatch_fail_count
        from crh_ads.dispatchtask
        where dispatch_status = '3'
          and subsys_id = '24'
          and finish_datetime > trunc(sysdate)
        group by dispatch_fail_count
    </select>

    <select id="getDispatchTaskOverview" resultType="com.cairh.cpe.monitor.backend.entity.DispatchTaskOverview">
        select nvl(sum(decode(dispatch_status, '0', 1, 0)), 0) as noqueue_sum,
               nvl(sum(decode(dispatch_status, '6', 1, 0)), 0) as queue_sum,
               nvl(sum(decode(dispatch_status, '1', 1, 0)), 0) as nodeal_sum,
               nvl(sum(decode(dispatch_status, '2', 1, 0)), 0) as dealing_sum
        from crh_ads.dispatchtask
        where dispatch_status in ('0', '1', '2', '6')
          and subsys_id = '24'
    </select>

    <select id="getHfDispatchTaskOverview" resultType="java.util.Map">
        select nvl(sum(decode(dispatch_status, '0', 1, 0)), 0) as noqueueSum,
               nvl(sum(decode(dispatch_status, '6', 1, 0)), 0) as queueSum,
               nvl(sum(decode(dispatch_status, '1', 1, 0)), 0) as nodealSum,
               nvl(sum(decode(dispatch_status, '2', 1, 0)), 0) as dealingSum
        from crh_ads_fus.dispatchtask
        where dispatch_status in ('0', '1', '2', '6')
          and subsys_id = '27'
    </select>

    <select id="getAllOperatorNum" resultType="java.lang.Integer">
        SELECT count(*)
        FROM CRH_USER.ALLCHANNELOPERATORINFO
        where status = '8';
    </select>

    <select id="getTodayOnlineOperatorNum" resultType="java.lang.Integer">
        SELECT count(DISTINCT OPERATOR_NO)
        from CRH_FUS.FOLLOWUPONLINERECORD
        where CREATE_DATETIME >=trunc( SYSDATE )
          AND STATUS = 1;
    </select>

    <select id="getTodayOnlineOperatorNoList" resultType="java.lang.String">
        SELECT DISTINCT OPERATOR_NO
        from CRH_FUS.FOLLOWUPONLINERECORD
        where CREATE_DATETIME >= trunc(SYSDATE)
          AND STATUS = 1;
    </select>

    <select id="getTodayOnlineOperatorTaskHandleNum"
            resultType="com.cairh.cpe.monitor.backend.entity.OperatorTaskHandleNum">
        WITH operators AS (SELECT DISTINCT OPERATOR_NO
                           FROM CRH_FUS.FOLLOWUPONLINERECORD
                           WHERE CREATE_DATETIME >= trunc(SYSDATE)
                             AND STATUS = 1)

        SELECT f.ACTUAL_OPERATOR_NO AS operator_no,
               count(*) as task_handle_num
        FROM CRH_FUS.FOLLOWUPTASK f
                 JOIN operators o
                      ON f.ACTUAL_OPERATOR_NO = o.OPERATOR_NO
                          AND f.TASK_STATUS = '50'
                          AND f.MERGE_TYPE != '2'
                          AND CREATE_DATETIME >= trunc(SYSDATE)
        GROUP BY f.ACTUAL_OPERATOR_NO
    </select>

    <select id="getTodayOnlineOperatorList"
            resultType="com.cairh.cpe.monitor.backend.form.resp.TodayOperatorExecuteStatusResp">
        SELECT DISTINCT OPERATOR_NO AS operatorNo, OPERATOR_NAME AS operatorName
        from CRH_FUS.FOLLOWUPONLINERECORD
        where CREATE_DATETIME >= trunc(SYSDATE)
          AND STATUS = 1;
    </select>
</mapper>
