<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cairh.cpe.monitor.backend.mapper.ChannelDefineMapper">

    <resultMap id="ChannelDefine" type="com.cairh.cpe.monitor.backend.entity.ChannelDefine">
        <result column="CHANNEL_NAME" property="channel_name" />
        <result column="CHANNEL_CODE" property="channel_code" />
    </resultMap>

    <select id="getChannelDefineList" resultMap="ChannelDefine">
        select * from crh_wskh.ChannelDefine where status = '8'
    </select>
</mapper>
