<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cairh.cpe.monitor.backend.mapper.FlowTaskRecordDetailsMapper">

    <select id="selectFlowTaskRecordDetailsByOperator"
            resultType="com.cairh.cpe.monitor.backend.entity.FlowTaskRecordDetails">
        select
        d.operator_no,
        count(1) as task_deal_count
        from crh_ac.flowtaskrecorddetails d
        where d.task_status in ('3','4')
        and d.invalid_flag = '0'
        and d.finish_datetime >= to_date(to_char(sysdate, 'yyyymm') || '01', 'yyyymmdd')
        and d.operator_no in
        <foreach collection="operatorNoList" item="operatorNo" open="(" separator="," close=")">
            #{operatorNo}
        </foreach>
        group by d.operator_no
    </select>
</mapper>
