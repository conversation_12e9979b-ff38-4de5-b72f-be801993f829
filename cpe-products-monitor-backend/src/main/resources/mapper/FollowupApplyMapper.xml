<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cairh.cpe.monitor.backend.mapper.FollowupApplyMapper">

    <select id="dailyApply" resultType="com.cairh.cpe.monitor.backend.form.resp.FollowUpDailyApplyResp">
        SELECT
            count( * ) AS apply_count,
            count(
                    CASE

                        WHEN STATUS = 7 THEN
                            1
                        WHEN STATUS = 8 THEN
                            1
                        WHEN STATUS = 9 THEN
                            1
                        WHEN STATUS = 10 THEN
                            1
                        WHEN STATUS = 11 THEN
                            1
                        END
            ) AS complete_count,
            TO_CHAR(
                    CASE WHEN count(*) = 0 THEN 0
                         ELSE count(
                                      CASE

                                          WHEN STATUS = 7 THEN
                                              1
                                          WHEN STATUS = 8 THEN
                                              1
                                          WHEN STATUS = 9 THEN
                                              1
                                          WHEN STATUS = 10 THEN
                                              1
                                          WHEN STATUS = 11 THEN
                                              1
                                          END
                              ) / count( * ) * 100
                        END
                    ,
                    'FM99999990.00'
            ) AS complete_rate,
            count( CASE WHEN STATUS = 7 THEN 1 END ) AS pass_count,
            TO_CHAR(
                    CASE WHEN count(*) = 0 THEN 0
                         ELSE  count( CASE WHEN STATUS = 7 THEN 1 END ) / count( * ) * 100
                        END, 'FM99999990.00' ) AS pass_rate,
            count( CASE WHEN STATUS = 8 THEN 1 END ) AS invalid_count,
            TO_CHAR(
                    CASE WHEN count(*) = 0 THEN 0
                         ELSE count( CASE WHEN STATUS = 8 THEN 1 END ) / count( * ) * 100
                        END, 'FM99999990.00' ) AS invalid_rate,
            count( CASE WHEN STATUS = 9 THEN 1 END ) AS reject_count,
            TO_CHAR(
                    CASE WHEN count(*) = 0 THEN 0
                         ELSE count( CASE WHEN STATUS = 9 THEN 1 END ) / count( * ) * 100
                        END, 'FM99999990.00' ) AS reject_rate,
            count( CASE WHEN STATUS = 11 THEN 1 END ) AS strategy_complete_count,
            TO_CHAR(
                    CASE WHEN count(*) = 0 THEN 0
                         ELSE count( CASE WHEN STATUS = 11 THEN 1 END ) / count( * ) * 100
                        END, 'FM99999990.00' ) AS strategy_complete_rate,
            count( CASE WHEN STATUS = 10 THEN 1 END ) AS manual_end_count,
            TO_CHAR(
                    CASE WHEN count(*) = 0 THEN 0
                         ELSE count( CASE WHEN STATUS = 10 THEN 1 END ) / count( * ) * 100
                        END, 'FM99999990.00' ) AS manual_end_rate,
            count( CASE WHEN LAST_QNAIRE_COMPLETION = 1 THEN 1 END ) AS survey_complete_count,
            TO_CHAR(
                    CASE WHEN count(*) = 0 THEN 0
                         ELSE  count( CASE WHEN LAST_QNAIRE_COMPLETION = 1 THEN 1 END ) / count( * ) * 100
                        END, 'FM99999990.00' ) AS survey_complete_rate,
            (
                sum(
                        CASE

                            WHEN ( FIRST_DEAL_TIME - CREATE_DATETIME ) * 24 * 60 * 60 >= 0 THEN
                                ( FIRST_DEAL_TIME - CREATE_DATETIME ) * 24 * 60 * 60
                            END
                )
                ) / count( CASE WHEN ( FIRST_DEAL_TIME - CREATE_DATETIME ) * 24 * 60 * 60 >= 0 THEN 1 END ) avg_first_response_time
        FROM
            CRH_FUS.FOLLOWUPAPPLY f
        WHERE CREATE_DATETIME >= TO_DATE(#{param.apply_start_time}, 'YYYY-MM-DD HH24:MI:SS')
        AND CREATE_DATETIME <![CDATA[ <= ]]> TO_DATE(#{param.apply_end_time}, 'YYYY-MM-DD HH24:MI:SS')
    </select>
</mapper>
