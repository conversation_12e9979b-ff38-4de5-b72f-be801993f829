<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cairh.cpe.monitor.backend.mapper.FollowupOperatorStatisticsMapper">
    <select id="operatorWork" resultType="com.cairh.cpe.monitor.backend.form.resp.FollowUpOperatorWorkResp">
        SELECT
            OPERATOR_NO AS operator_no,
            OPERATOR_NAME AS operator_name,
            EARLIEST_LOGIN_TIME AS first_login_time,
            LATEST_LOGOUT_TIME AS last_logout_time,
            TO_CHAR( TOTAL_ONLINE_SECONDS / ( 1 * 60 * 60 ), 'FM99999990.00' ) AS online_duration_hours,
            FIRST_PROCESS_TIME AS first_process_time,
            LAST_PROCESS_TIME AS last_process_time,
            TO_CHAR( TOTAL_PROCESS_DUR_SECONDS / ( 1 * 60 * 60 ), 'FM99999990.00' ) AS total_process_duration,
            TO_CHAR( TOTAL_PROCESS_DUR_SECONDS / TOTAL_ONLINE_SECONDS, 'FM99999990.00' ) AS work_efficiency,
            TO_CHAR( TOTAL_CALL_NUM / ( TOTAL_ONLINE_SECONDS / ( 1 * 60 * 60 ) ), 'FM99999990.00' ) AS call_efficiency
        FROM
            CRH_MONITOR.FOLLOWUPOPERATORSTATISTICS f
        WHERE STAT_DATE >= TO_DATE(#{param.task_create_start_time}, 'YYYY-MM-DD HH24:MI:SS')
          AND STAT_DATE <![CDATA[ <= ]]> TO_DATE(#{param.task_create_end_time}, 'YYYY-MM-DD HH24:MI:SS')
        order by STAT_DATE desc, OPERATOR_NO asc
    </select>


    <select id="operatorWorkTotal" resultType="com.cairh.cpe.monitor.backend.form.resp.FollowUpOperatorWorkResp">
        SELECT
            '合计' AS operator_no,
            '-' AS operator_name,
            min( EARLIEST_LOGIN_TIME ) AS first_login_time,
            max( LATEST_LOGOUT_TIME ) AS last_logout_time,
            TO_CHAR( sum( TOTAL_ONLINE_SECONDS ) / ( 1 * 60 * 60 ), 'FM99999990.00' ) AS online_duration_hours,
            min( FIRST_PROCESS_TIME ) AS first_process_time,
            max( LAST_PROCESS_TIME ) AS last_process_time,
            TO_CHAR( sum( TOTAL_PROCESS_DUR_SECONDS ) / ( 1 * 60 * 60 ), 'FM99999990.00' ) AS total_process_duration,
            TO_CHAR( sum( TOTAL_PROCESS_DUR_SECONDS ) / sum( TOTAL_ONLINE_SECONDS ), 'FM99999990.00' ) AS work_efficiency,
            TO_CHAR( sum( TOTAL_CALL_NUM ) / ( sum( TOTAL_ONLINE_SECONDS ) / ( 1 * 60 * 60 ) ), 'FM99999990.00' ) AS call_efficiency
        FROM
            FOLLOWUPOPERATORSTATISTICS f
        WHERE STAT_DATE >= TO_DATE(#{param.task_create_start_time}, 'YYYY-MM-DD HH24:MI:SS')
          AND STAT_DATE <![CDATA[ <= ]]> TO_DATE(#{param.task_create_end_time}, 'YYYY-MM-DD HH24:MI:SS')
    </select>

    <select id="getOnlineOperatorNum" resultType="java.lang.Integer">
        SELECT
            count( CASE WHEN TOTAL_ONLINE_SECONDS >= 7 * 60 * 60 THEN 1 END )
        FROM
            FOLLOWUPOPERATORSTATISTICS ss
        WHERE STAT_DATE >= TO_DATE(#{param.task_create_start_time}, 'YYYY-MM-DD HH24:MI:SS')
          AND STAT_DATE <![CDATA[ <= ]]> TO_DATE(#{param.task_create_end_time}, 'YYYY-MM-DD HH24:MI:SS')
    </select>
</mapper>
