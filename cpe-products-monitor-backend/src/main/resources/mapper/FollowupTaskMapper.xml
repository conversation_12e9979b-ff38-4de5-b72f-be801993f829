<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cairh.cpe.monitor.backend.mapper.FollowupTaskMapper">


    <select id="operatorBusiness" resultType="com.cairh.cpe.monitor.backend.form.resp.FollowUpOperatorBusinessResp">
        SELECT
        ACTUAL_OPERATOR_NO AS operator_no,
        MAX(ACTUAL_OPERATOR_NAME) AS operator_name,
        count(*) AS task_complete_count,
        nvl(sum(CALL_NUM),0) AS call_count,
        count(
        CASE
        WHEN ANSWER_STATUS=2 THEN 1
        END
        ) AS call_connect_count ,
        TO_CHAR(
        CASE
        WHEN sum(CALL_NUM) = 0 THEN 0
        ELSE count(
        CASE
        WHEN ANSWER_STATUS=2 THEN 1
        END
        ) / sum(CALL_NUM) * 100
        END
        , '**********.00') AS call_connect_rate,
        TO_CHAR(
        CASE
        WHEN count(
        CASE
        WHEN (DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60>=0 THEN 1
        END) = 0 THEN 0
        ELSE sum(
        CASE
        WHEN (DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60>=0 THEN (DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60
        END
        )
        /count(
        CASE
        WHEN (DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60>=0 THEN 1
        END)
        END
        , '**********.00') AS avg_response_duration,
        round(
        CASE WHEN count(
        CASE
        WHEN (FINISH_DATETIME - DEAL_DATETIME) * 24 * 60 * 60>=0 THEN 1
        END) = 0 THEN 0
        ELSE sum(
        CASE
        WHEN (FINISH_DATETIME - DEAL_DATETIME) * 24 * 60 * 60>=0 THEN (FINISH_DATETIME - DEAL_DATETIME) * 24 * 60 * 60
        END
        )
        /count(
        CASE
        WHEN (FINISH_DATETIME - DEAL_DATETIME) * 24 * 60 * 60>=0 THEN 1
        END)
        END
        ,2
        )AS avg_handling_duration,
        TO_CHAR(
        CASE
        WHEN count(*) = 0 THEN 0
        ELSE count(
        CASE
        WHEN HANDLE_STATUS=1 THEN 1
        END
        )
        /count(*) * 100
        END
        , '**********.00') AS visit_pass_rate,
        TO_CHAR(
        CASE
        WHEN count(*) = 0 THEN 0
        ELSE count(
        CASE
        WHEN HANDLE_STATUS=5 THEN 1
        END
        )
        /count(*) * 100
        END
        , '**********.00') AS invalid_rate,
        TO_CHAR(
        CASE
        WHEN count(*) = 0 THEN 0
        ELSE count(
        CASE
        WHEN HANDLE_STATUS=6 THEN 1
        END
        )
        /count(*) * 100
        END
        , '**********.00') AS reject_rate,
        TO_CHAR(
        CASE
        WHEN count(*) = 0 THEN 0
        ELSE count(
        CASE
        WHEN QNAIRE_COMPLETION=1 THEN 1
        END
        )
        /count(*) * 100
        END
        , '**********.00') AS survey_completion_rate,
        TO_CHAR(
        CASE
        WHEN count(*) = 0 THEN 0
        ELSE count(
        CASE
        WHEN (DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60>=0 AND(DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60<![CDATA[ < ]]>
        5*60 THEN
        1
        END
        )
        /count(*) * 100
        END
        , '**********.00') AS response_0_5m_rate,
        TO_CHAR(
        CASE
        WHEN count(*) = 0 THEN 0
        ELSE count(
        CASE
        WHEN (DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60>=5*60 AND(DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60<![CDATA[ < ]]>
        10*60
        THEN 1
        END
        )
        /count(*) * 100
        END
        , '**********.00') AS response_5_10m_rate,
        TO_CHAR(
        CASE
        WHEN count(*) = 0 THEN 0
        ELSE count(
        CASE
        WHEN (DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60>=10*60 AND(DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60
        <![CDATA[ < ]]>15*60
        THEN 1
        END
        )
        /count(*) * 100
        END
        , '**********.00') AS response_10_15m_rate,
        TO_CHAR(
        CASE
        WHEN count(*) = 0 THEN 0
        ELSE count(
        CASE
        WHEN (DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60>=15*60 THEN 1
        END
        )
        /count(*) * 100
        END
        , '**********.00') AS response_over_15m_rate,
        TO_CHAR(
        CASE
        WHEN count(*) = 0 THEN 0
        ELSE count(
        CASE
        WHEN (DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60>=30*60 THEN 1
        END
        )
        /count(*) * 100
        END
        , '**********.00') AS response_over_30m_rate,
        TO_CHAR(
        CASE
        WHEN count(*) = 0 THEN 0
        ELSE count(
        CASE
        WHEN (DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60>=45*60 THEN 1
        END
        )
        /count(*) * 100
        END
        , '**********.00') AS response_over_45m_rate
        FROM CRH_FUS.FOLLOWUPTASK f
        WHERE TASK_STATUS= 50
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(param.task_create_time)">
            AND CREATE_DATETIME >= TO_DATE(#{param.task_create_start_time}, 'YYYY-MM-DD HH24:MI:SS')
            AND CREATE_DATETIME <![CDATA[ <= ]]> TO_DATE(#{param.task_create_end_time}, 'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="param.first_task != null">
            AND IS_FIRST_TASK = #{param.first_task}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(param.actual_operator_no)">
            AND actual_operator_no = #{param.actual_operator_no}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(param.actual_operator_name)">
            AND actual_operator_name LIKE CONCAT('%', #{param.actual_operator_name}, '%')
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(param.push_time)">
            AND PUSH_DATETIME >= TO_DATE(#{param.push_start_time}, 'YYYY-MM-DD HH24:MI:SS')
            AND PUSH_DATETIME <![CDATA[ <= ]]> TO_DATE(#{param.push_end_time}, 'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(param.deal_time)">
            AND DEAL_DATETIME >= TO_DATE(#{param.deal_start_time}, 'YYYY-MM-DD HH24:MI:SS')
            AND DEAL_DATETIME <![CDATA[ <= ]]> TO_DATE(#{param.deal_end_time}, 'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(param.finish_time)">
            AND FINISH_DATETIME >= TO_DATE(#{param.finish_start_time}, 'YYYY-MM-DD HH24:MI:SS')
            AND FINISH_DATETIME <![CDATA[ <= ]]> TO_DATE(#{param.finish_end_time}, 'YYYY-MM-DD HH24:MI:SS')
        </if>
        GROUP BY ACTUAL_OPERATOR_NO
    </select>

    <select id="operatorBusinessTotal"
            resultType="com.cairh.cpe.monitor.backend.form.resp.FollowUpOperatorBusinessResp">
        SELECT
        '合计' AS operator_no,
        '-' AS operator_name,
        count(*) AS task_complete_count,
        nvl(sum(CALL_NUM),0) AS call_count,
        count(
        CASE
        WHEN ANSWER_STATUS=2 THEN 1
        END
        ) AS call_connect_count ,
        TO_CHAR(
        CASE
        WHEN sum(CALL_NUM) = 0 THEN 0
        ELSE count(
        CASE
        WHEN ANSWER_STATUS=2 THEN 1
        END
        ) / sum(CALL_NUM) * 100
        END
        , '**********.00') AS call_connect_rate,
        TO_CHAR(
        CASE
        WHEN count(
        CASE
        WHEN (DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60>=0 THEN 1
        END) = 0 THEN 0
        ELSE sum(
        CASE
        WHEN (DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60>=0 THEN (DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60
        END
        )
        /count(
        CASE
        WHEN (DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60>=0 THEN 1
        END)
        END
        , '**********.00') AS avg_response_duration,
        round(
        sum(
        CASE
        WHEN (FINISH_DATETIME - DEAL_DATETIME) * 24 * 60 * 60>=0 THEN (FINISH_DATETIME - DEAL_DATETIME) * 24 * 60 * 60
        END
        )
        /count(
        CASE
        WHEN (FINISH_DATETIME - DEAL_DATETIME) * 24 * 60 * 60>=0 THEN 1
        END),2
        )AS avg_handling_duration,
        TO_CHAR(
        CASE
        WHEN count(*) = 0 THEN 0
        ELSE count(
        CASE
        WHEN HANDLE_STATUS=1 THEN 1
        END
        )
        /count(*) * 100
        END
        , '**********.00') AS visit_pass_rate,
        TO_CHAR(
        CASE
        WHEN count(*) = 0 THEN 0
        ELSE count(
        CASE
        WHEN HANDLE_STATUS=5 THEN 1
        END
        )
        /count(*) * 100
        END
        , '**********.00') AS invalid_rate,
        TO_CHAR(
        CASE
        WHEN count(*) = 0 THEN 0
        ELSE count(
        CASE
        WHEN HANDLE_STATUS=6 THEN 1
        END
        )
        /count(*) * 100
        END
        , '**********.00') AS reject_rate,
        TO_CHAR(
        CASE
        WHEN count(*) = 0 THEN 0
        ELSE count(
        CASE
        WHEN QNAIRE_COMPLETION=1 THEN 1
        END
        )
        /count(*) * 100
        END
        , '**********.00') AS survey_completion_rate,
        TO_CHAR(
        CASE
        WHEN count(*) = 0 THEN 0
        ELSE count(
        CASE
        WHEN (DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60>=0 AND(DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60<![CDATA[ < ]]>
        5*60 THEN
        1
        END
        )
        /count(*) * 100
        END
        , '**********.00') AS response_0_5m_rate,
        TO_CHAR(
        CASE
        WHEN count(*) = 0 THEN 0
        ELSE count(
        CASE
        WHEN (DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60>=5*60 AND(DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60<![CDATA[ < ]]>
        10*60
        THEN 1
        END
        )
        /count(*) * 100
        END
        , '**********.00') AS response_5_10m_rate,
        TO_CHAR(
        CASE
        WHEN count(*) = 0 THEN 0
        ELSE count(
        CASE
        WHEN (DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60>=10*60 AND(DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60
        <![CDATA[ < ]]>15*60
        THEN 1
        END
        )
        /count(*) * 100
        END
        , '**********.00') AS response_10_15m_rate,
        TO_CHAR(
        CASE
        WHEN count(*) = 0 THEN 0
        ELSE count(
        CASE
        WHEN (DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60>=15*60 THEN 1
        END
        )
        /count(*) * 100
        END
        , '**********.00') AS response_over_15m_rate,
        TO_CHAR(
        CASE
        WHEN count(*) = 0 THEN 0
        ELSE count(
        CASE
        WHEN (DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60>=30*60 THEN 1
        END
        )
        /count(*) * 100
        END
        , '**********.00') AS response_over_30m_rate,
        TO_CHAR(
        CASE
        WHEN count(*) = 0 THEN 0
        ELSE count(
        CASE
        WHEN (DEAL_DATETIME - CREATE_DATETIME) * 24 * 60 * 60>=45*60 THEN 1
        END
        )
        /count(*) * 100
        END
        , '**********.00') AS response_over_45m_rate
        FROM CRH_FUS.FOLLOWUPTASK f
        WHERE TASK_STATUS= 50
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(param.task_create_time)">
            AND CREATE_DATETIME >= TO_DATE(#{param.task_create_start_time}, 'YYYY-MM-DD HH24:MI:SS')
            AND CREATE_DATETIME <![CDATA[ <= ]]> TO_DATE(#{param.task_create_end_time}, 'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="param.first_task != null">
            AND IS_FIRST_TASK = #{param.first_task}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(param.actual_operator_no)">
            AND actual_operator_no = #{param.actual_operator_no}
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(param.actual_operator_name)">
            AND actual_operator_name LIKE CONCAT('%', #{param.actual_operator_name}, '%')
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(param.push_time)">
            AND PUSH_DATETIME >= TO_DATE(#{param.push_start_time}, 'YYYY-MM-DD HH24:MI:SS')
            AND PUSH_DATETIME <![CDATA[ <= ]]> TO_DATE(#{param.push_end_time}, 'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(param.deal_time)">
            AND DEAL_DATETIME >= TO_DATE(#{param.deal_start_time}, 'YYYY-MM-DD HH24:MI:SS')
            AND DEAL_DATETIME <![CDATA[ <= ]]> TO_DATE(#{param.deal_end_time}, 'YYYY-MM-DD HH24:MI:SS')
        </if>
        <if test="@cn.hutool.core.text.CharSequenceUtil@isNotBlank(param.finish_time)">
            AND FINISH_DATETIME >= TO_DATE(#{param.finish_start_time}, 'YYYY-MM-DD HH24:MI:SS')
            AND FINISH_DATETIME <![CDATA[ <= ]]> TO_DATE(#{param.finish_end_time}, 'YYYY-MM-DD HH24:MI:SS')
        </if>


    </select>

    <select id="dailyTask" resultType="com.cairh.cpe.monitor.backend.form.resp.FollowUpDailyTaskResp">
        SELECT
            count( * ) AS task_create_count,
            count( PUSH_DATETIME ) AS task_push_count,
            count( CASE WHEN TASK_STATUS = 50 THEN 1 END ) AS task_complete_count,
            TO_CHAR(
                    CASE WHEN count(*) = 0 THEN 0
                         ELSE count( CASE WHEN TASK_STATUS = 50 THEN 1 END ) / count( * ) * 100
                        END, '**********.00' ) AS task_complete_rate,
            nvl(sum( CALL_TOTAL_NUM ),0) AS call_total_count,
            COUNT( CASE WHEN TRIM( ANSWER_STATUS ) = '2' THEN 1
                END ) AS connect_total_count,
            round(CASE WHEN count(*) = 0 THEN 0
                       ELSE ( sum( ( DEAL_DATETIME - CREATE_DATETIME ) * 24 * 60 * 60 ) ) / count( * )
                      END, 2 ) AS avg_response_time
        FROM
            CRH_FUS.FOLLOWUPTASK f
        WHERE CREATE_DATETIME >= TO_DATE(#{param.task_create_start_time}, 'YYYY-MM-DD HH24:MI:SS')
          AND CREATE_DATETIME <![CDATA[ <= ]]> TO_DATE(#{param.task_create_end_time}, 'YYYY-MM-DD HH24:MI:SS')
    </select>

    <select id="groupAndCountTodayByStatus" resultType="com.cairh.cpe.monitor.backend.entity.OperatorGroupAndCount">
        SELECT
        actual_operator_no as operatorNo ,MAX(actual_operator_name) as operatorName ,count( * ) as countNum
        FROM
        CRH_FUS.FOLLOWUPTASK f
        WHERE
        f.PUSH_DATETIME >= trunc( SYSDATE )
        AND TASK_STATUS IN
        <foreach collection="param" item="status" open="(" separator="," close=")">
            #{status}
        </foreach>
        AND MERGE_TYPE != '2'
        AND actual_operator_no!= ' '
        GROUP BY actual_operator_no
    </select>



    <select id="groupAndCountTodayByStatusOperatorNo"
            resultType="com.cairh.cpe.monitor.backend.entity.OperatorGroupAndCount">
        SELECT
        OPERATOR_NO as operatorNo ,MAX(OPERATOR_NAME) as operatorName ,count( * ) as countNum
        FROM
        CRH_FUS.FOLLOWUPTASK f
        WHERE
        f.PUSH_DATETIME >= trunc( SYSDATE )
        AND TASK_STATUS IN
        <foreach collection="param" item="status" open="(" separator="," close=")">
            #{status}
        </foreach>
        AND MERGE_TYPE != '2'
        AND OPERATOR_NO!= ' '
        GROUP BY OPERATOR_NO
    </select>

    <select id="groupAndCountTodayOverTime" resultType="com.cairh.cpe.monitor.backend.entity.OperatorGroupAndCount">
        SELECT
        actual_operator_no as operatorNo ,MAX(actual_operator_name) as operatorName ,count( * ) as countNum
        FROM
        CRH_FUS.FOLLOWUPTASK f
        WHERE
        f.PUSH_DATETIME >= trunc( SYSDATE )
        AND IS_OVERTIME='1'
        AND MERGE_TYPE != '2'
        AND actual_operator_no!= ' '
        GROUP BY actual_operator_no
    </select>

    <select id="groupAndCountTodayNoHandleAndOverTime"
            resultType="com.cairh.cpe.monitor.backend.entity.OperatorGroupAndCount">
        SELECT
            OPERATOR_NO as operatorNo ,MAX(OPERATOR_NAME) as operatorName ,count( * ) as countNum
        FROM
            CRH_FUS.FOLLOWUPTASK f
        WHERE
            f.PUSH_DATETIME >= trunc( SYSDATE )
          AND IS_OVERTIME='1'
          AND MERGE_TYPE != '2'
          AND TASK_STATUS in ('15','20')
        GROUP BY OPERATOR_NO

    </select>
</mapper>
