<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cairh.cpe.monitor.backend.mapper.OperatorInfoMapper">

    <select id="selectOperatorInfoByStaffNo"
            resultType="com.cairh.cpe.monitor.backend.entity.OperatorInfo">
        select o.staff_no,
               o.branch_no,
               o.en_branch_nos
        from crh_user.operatorinfo o
        where o.staff_no = #{staff_no};
    </select>
</mapper>
