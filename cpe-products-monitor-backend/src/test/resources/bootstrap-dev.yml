# 使用 172.16.1.104 环境的，不需要更改，如果用自己本地服务，请用 profile 为 local，并更改配置，不要提交自己的配置
spring:
  profiles:
    include: nacos-discovery, nacos-config
  #    include: consul-discovery, apollo-config
  cloud:
    nacos:
      discovery:
        namespace: zhubiao
        group: DEFAULT_GROUP
      config:
        namespace: zhubiao
        file-extension: yml
      server-addr: 111.229.161.129:8848
      username: nacos
      password: nacos

