spring:
  cloud:
    nacos:
      config:
        enabled: false
      discovery:
        enabled: false
    consul:
      enabled: false
      discovery:
        enabled: false
      config:
        enabled: false

apollo:
  bootstrap:
    enabled: false

---
spring:
  profiles: nacos-discovery
  cloud:
    nacos:
      discovery:
        enabled: true
dubbo:
  registry:
    address: nacos://${spring.cloud.nacos.discovery.server-addr:${spring.cloud.nacos.server-addr}}
    group: DUBBO_SERVICE_GROUP
    parameters:
      namespace: ${spring.cloud.nacos.discovery.namespace}

---
spring:
  profiles: nacos-config
  cloud:
    nacos:
      config:
        enabled: true
        shared-configs:
          - dataId: application.yml
            group: COMMON_GROUP

---
spring:
  profiles: consul-discovery
  cloud:
    consul:
      enabled: true
      discovery:
        preferIpAddress: true
        healthCheckCriticalTimeout: 10s
        instanceId: ${spring.application.name}:${spring.cloud.client.ip-address}-${random.value}
        enabled: true
        heartbeat:
          enabled: true
dubbo:
  registry:
    address: consul://${spring.cloud.consul.host}:${spring.cloud.consul.port}
    simplified: true
    parameters:
      consul-check-pass-interval: 120000
      consul-deregister-critical-service-after: 30s

---
spring:
  profiles: consul-config
  cloud:
    consul:
      config:
        prefix: config
        format: yaml
        default-context: application
        enabled: true

---
spring:
  profiles: apollo-config

apollo:
  bootstrap:
    enabled: true
    eagerLoad:
      enabled: true
    namespaces: application.yml, ${spring.application.name}.yml