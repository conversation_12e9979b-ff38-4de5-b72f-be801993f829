package com.cairh.cpe.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class MonitorThreadPoolConfig {

    public MonitorThreadPoolConfig() {
    }

    @Bean({"monitorExecutor"})
    public ThreadPoolTaskExecutor monitorExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(5);
        executor.setKeepAliveSeconds(60);
        executor.setQueueCapacity(50);
        executor.setThreadNamePrefix("monitor-executor-");
        executor.initialize();
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setAllowCoreThreadTimeOut(true);
        return executor;
    }
}