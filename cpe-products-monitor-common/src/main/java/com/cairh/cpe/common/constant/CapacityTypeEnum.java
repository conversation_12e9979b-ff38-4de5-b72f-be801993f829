package com.cairh.cpe.common.constant;

/**
 * 产能类型
 */
public enum CapacityTypeEnum {

    CAPACITY_LEVEL_6("6", "500笔以上"),
    CAPACITY_LEVEL_5("5", "400-500笔"),
    CAPACITY_LEVEL_4("4", "300-400笔"),
    CAPACITY_LEVEL_3("3", "200-300笔"),
    CAPACITY_LEVEL_2("2", "100-200笔"),
    CAPACITY_LEVEL_1("1", "0-100笔");

    String code;
    String value;

    CapacityTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public static String getValue(String code) {
        for (CapacityTypeEnum h : CapacityTypeEnum.values()) {
            if (h.getCode().equals(code)) {
                return h.value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
