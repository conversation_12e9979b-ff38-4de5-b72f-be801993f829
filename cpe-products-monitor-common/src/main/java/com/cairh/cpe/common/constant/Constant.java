package com.cairh.cpe.common.constant;

/**
 * 常量
 *
 * <AUTHOR>
 * @since 2025/6/26 17:06
 */
public class Constant {


    public static final String SUCCESS = "成功";

    //通用删除状态
    public static final String COMMON_DELETE_STATUS = "9";
    public static final String COMMON_VALID_STATUS = "8";

    public static final String[] CHAR_SET = {"@", "&"};

    public static final String EMPTY_STRING = "";
    public static final String BLANK_SPACE_STRING = " ";
    public static final String ZERO_STR = "0";
    public static final String ONE_STR = "1";
    public static final String THREE_STR = "3";
    public static final String FIVE_STR = "5";
    public static final String NINE_STR = "9";
    public static final String TEN_STR = "10";
    public static final String NINETEEN_STR = "19";
    public static final String ONE_HUNDRED_STR = "100";
    public static final String ONE_HUNDRED_STR_SUFFIX = "%";

    public static final String DEFAULT = "default";
    public static final String SOURCE_INFO_SYS = "sys";

    public static final String COMMA = ",";
    public static final String COLON = ":";
    public static final String UNDERLINE = "_";

    public static final int ZERO = 0;
    public static final int NEGATIVE_ONE = -1;
    public static final int ONE = 1;
    public static final int TWO = 2;
    public static final int THREE = 3;
    public static final int FOUR = 4;
    public static final int FIVE = 5;
    public static final int SIX = 6;
    public static final int EIGHT = 8;
    public static final int NINE = 9;
    public static final int TEN = 10;
    public static final int TWELVE = 12;
    public static final int FOURTEEN = 14;
    public static final int FIFTEEN = 15;
    public static final int SIXTEEN = 16;
    public static final int EIGHTEEN = 18;
    public static final int SIXTY = 60;
    public static final int ONE_HUNDRED = 100;
    public static final int THREE_HUNDRED = 300;
    public static final int ONE_THOUSAND = 1000;
    public static final int THREE_THOUSAND = 3000;
    public static final int FOUR_THOUSAND = 4000;
    public static final int TEN_THOUSAND = 10000;

    /**
     * 见证系统报表统计单位时间
     */
    public static final String MONITOR_AC_STATISTICS_UNIT_TIME = "monitor.ac.statistics.unit.time";

    /**
     * 导出接口的最大条数
     */
    public static final String UPLOAD_MAX_SIZE = "cpe.db.config.max-rows";

    /**
     * 坐席人员分组配置
     */
    public static final String MONITOR_AC_OPERATOR_GROUP = "monitor.ac.operator.group";

    /**
     * 工作时段（交易日）待处理任务（不含挂起）
     */
    public static final String MONITOR_ALARM_TASK_WORK_PENDING_NUM = "monitor.alarm.task.work.pending.num";

    /**
     * 非工作时段待处理任务（不含挂起）
     */
    public static final String MONITOR_ALARM_TASK_NON_WORK_PENDING_NUM = "monitor.alarm.task.non-work.pending.num";

    /**
     * 工作时段平均响应时长
     */
    public static final String MONITOR_ALARM_TASK_AVG_RESPONSE_TIME = "monitor.alarm.task.avg.response.time";

    /**
     * 非工作时段平均响应时长
     */
    public static final String MONITOR_ALARM_NON_WORK_TASK_AVG_RESPONSE_TIME = "monitor.alarm.non-work.task.avg.response.time";

    /**
     * 工作时段单笔任务等待时长
     */
    public static final String MONITOR_ALARM_TASK_WAIT_TIME = "monitor.alarm.task.wait.time";

    /**
     * 非工作时段单笔任务等待时长
     */
    public static final String MONITOR_ALARM_NON_WORK_TASK_WAIT_TIME = "monitor.alarm.non-work.task.wait.time";

    /**
     * 工作时段单笔任务分配失败次数
     */
    public static final String MONITOR_ALARM_TASK_ASSIGNMENT_FAIL_NUM = "monitor.alarm.task.assignment.fail.num";

    /**
     * 非工作时段单笔任务分配失败次数
     */
    public static final String MONITOR_ALARM_NON_WORK_TASK_ASSIGNMENT_FAIL_NUM = "monitor.alarm.non-work.task.assignment.fail.num";

    /**
     * 系统工作时段未进任务时长
     */
    public static final String MONITOR_ALARM_TASK_NEW_TIME = "monitor.alarm.task.new.time";

    /**
     * 系统非工作时段未进任务时长
     */
    public static final String MONITOR_ALARM_NON_WORK_TASK_NEW_TIME = "monitor.alarm.non-work.task.new.time";

    /**
     * 单坐席未有操作记录时长
     */
    public static final String MONITOR_ALARM_OPERATOR_IDLE_TIME = "monitor.alarm.operator.idle.time";

    /**
     * 非工作时段单坐席未有操作记录时长
     */
    public static final String MONITOR_ALARM_NON_WORK_OPERATOR_IDLE_TIME = "monitor.alarm.non-work.operator.idle.time";

    /**
     * 监控告警通知手机号
     */
    public static final String MONITOR_ALARM_SMS_NOTICE_MOBILE = "monitor.alarm.sms.notice.mobile";

    /**
     * 监控告警通知间隔时间
     */
    public static final String MONITOR_ALARM_SMS_INTERVAL_TIME = "monitor.alarm.sms.interval.time";

    /**
     * 监控告警通知开关
     */
    public static final String MONITOR_ALARM_SMS_NOTICE_SWITCH = "monitor.alarm.sms.notice.switch";

    /**
     * 待处理任务量告警短信模板
     */
    public static final String MONITOR_ALARM_PENDINGTASK_SMS_TEMPLATE = "monitor.alarm.pendingTask.sms.template";

    /**
     * 平均响应时长告警短信模板
     */
    public static final String MONITOR_ALARM_AVGRESPONSE_SMS_TEMPLATE = "monitor.alarm.avgResponse.sms.template";

    /**
     * 新进任务告警短信模板
     */
    public static final String MONITOR_ALARM_NEWTASK_SMS_TEMPLATE = "monitor.alarm.newTask.sms.template";

    /**
     * 单笔任务等待时长告警短信模板
     */
    public static final String MONITOR_ALARM_WAITTASK_SMS_TEMPLATE = "monitor.alarm.waitTask.sms.template";

    /**
     * 单笔任务分配失败告警短信模板
     */
    public static final String MONITOR_ALARM_FAILEDTASK_SMS_TEMPLATE = "monitor.alarm.failedTask.sms.template";

    /**
     * 操作频次告警短信模板
     */
    public static final String MONITOR_ALARM_OPERATOR_SMS_TEMPLATE = "monitor.alarm.operator.sms.template";

    /**
     * 新进任务展示数量
     */
    public static final String MONITOR_ALARM_TASK_NEW_SHOW_NUM = "monitor.alarm.task.new.show.num";

    /**
     * 营运中心
     */
    public static final String SPECIAL_BRANCH = "3";

    /**
     * redis缓存过期时间: 20
     */
    public static final long CACHE_EXPIRE_TIME = 20;

    /**
     * 操作员状态 1
     */
    public static final String USER_STATUS_1 = "1";

    /**
     * 操作员状态 2
     */
    public static final String USER_STATUS_2 = "2";

    /**
     * 见证人工作时间
     */
    public static final String WORK_TIME_AUDIT_WORKING = "h";

    /**
     * 见证告警通知时间
     */
    public static final String WORK_TIME_AUDIT_ALARM = "j";

    /**
     * 发送短信服务名称
     */
    public static final String KAFKA_SERVICE_VENDER = "gtjaKafkaSmsMessageService";

    /**
     * 通知方式 0-短信
     */
    public static final String SEND_TYPE = "0";

    /**
     * 通知渠道类型
     */
    public static final String CHANNEL_TYPE = "GTJA";

    /**
     * 工作时间属性
     */
    public static final String TIME_PROPERTY_1 = "1";

    /**
     * 非工作时间属性
     */
    public static final String TIME_PROPERTY_2 = "2";

    /**
     * 默认新进任务展示数量
     */
    public static final String DEFAULT_TASK_NEW_SHOW_NUM = "5";
}
