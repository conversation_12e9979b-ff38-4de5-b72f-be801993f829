package com.cairh.cpe.common.constant;

public class MonitorErrorCode {

    /*
        #BM-312 【通用修改】为规范各个系统错误号输出，现定义运营监控平台系统错误号（280000-289999），请安排处理
     */

    public static final String OK_SUCCESS = "0";

    /**
     * 没有对应转换格式类型
     */
    public static final String PARSE_TYPE_NO_FOUND = "280000";

    /**
     * 属性没有对应字段类型
     */
    public static final String ATTR_TYPE_NO_FOUND = "280001";

    /**
     * 传入字段值为空
     */
    public static final String PARAM_VALUE_IS_NULL = "280002";

    /**
     * json转换异常
     */
    public static final String JSON_CONVERT_IS_ERROR = "280003";

    /**
     * 缓存配置不存在
     */
    public static final String CACHE_CONFIG_NO_EXIST = "280004";

    /**
     * sql执行错误
     */
    public static final String SQL_EXECUTE_IS_ERROR = "280005";

    /**
     * 不符合参数校验规则
     */
    public static final String VALID_PARAM_IS_ERROR = "280006";

    /**
     * XML文件解析异常
     */
    public static final String XML_PARSE_ERROR = "280007";

    /**
     * 请求重复执行异常
     */
    public static final String REPEATED_EXECUTION_ERROR = "28008";
}
