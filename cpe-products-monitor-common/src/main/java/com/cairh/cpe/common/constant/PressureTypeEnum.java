package com.cairh.cpe.common.constant;

/**
 * 承压类型
 */
public enum PressureTypeEnum {

    PRESS("1", "承压"),
    BUSY("2", "忙碌"),
    NORMAL("3", "正常"),
    IDLE("4", "空闲"),
    ;

    String code;
    String value;

    PressureTypeEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public static String getValue(String code) {
        for (PressureTypeEnum h : PressureTypeEnum.values()) {
            if (h.getCode().equals(code)) {
                return h.value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
