package com.cairh.cpe.common.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 告警监控大屏
 *
 * <AUTHOR>
 * @since 2025/6/24 14:37
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class AuditAlarmDetailResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 待处理任务详情
     */
    private PendingTaskDetail pendingTaskDetail;

    /**
     * 平均响应详情
     */
    private AvgResponseDetail avgResponseDetail;

    /**
     * 单笔任务等待时长
     */
    private List<AuditAlarmTaskDetail> taskWaitDetailList;

    /**
     * 单笔任务分配失败次数
     */
    private List<AuditAlarmTaskDetail> failedTaskAssignmentList;

    /**
     * 新进任务监控
     */
    private List<AuditAlarmTaskDetail> newTaskList;

    /**
     * 操作频次监控
     */
    private List<AuditAlarmOperatorDetail> operatorDetailList;

    @Data
    public static class PendingTaskDetail implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 待处理任务量
         */
        private Integer pending_task_num = 0;

        /**
         * 数据类型
         * 0-正常 1-告警
         */
        private String type = "0";

    }

    @Data
    public static class AvgResponseDetail implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 平均响应时长
         */
        private Integer avg_response_time = 0;

        /**
         * 数据类型
         * 0-正常 1-告警
         */
        private String type = "0";

    }
}
