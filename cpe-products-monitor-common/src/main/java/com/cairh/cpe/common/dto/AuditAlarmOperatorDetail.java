package com.cairh.cpe.common.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 操作员操作详情
 *
 * <AUTHOR>
 * @since 2025/6/25 13:49
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class AuditAlarmOperatorDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 操作员工号
     */
    private String operator_no;

    /**
     * 操作员姓名
     */
    private String operator_name;

    /**
     * 处理时间
     */
    private Date deal_datetime;

    /**
     * 未操作时间（分钟）
     */
    private Integer idle_time = 0;

    /**
     * 数据类型
     * 0-正常 1-告警
     */
    private String type = "0";
}
