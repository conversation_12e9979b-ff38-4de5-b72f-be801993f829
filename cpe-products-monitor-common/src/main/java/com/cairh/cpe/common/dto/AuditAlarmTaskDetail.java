package com.cairh.cpe.common.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025/6/25 13:50
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class AuditAlarmTaskDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户名称
     */
    private String client_name;

    /**
     * 客户证件号码
     */
    private String id_no;

    /**
     * 等待时长（分钟）
     */
    private Integer wait_time = 0;

    /**
     * 分配失败次数（次）
     */
    private Integer fail_assignment_num = 0;

    /**
     * 任务请求时间
     */
    private Date request_datetime;

    /**
     * 最近新进任务时间
     */
    private String new_task_datetime;

    /**
     * 间隔时间（分钟）
     */
    private Integer interval_time = 0;

    /**
     * 数据类型
     * 0-正常 1-告警
     */
    private String type = "0";
}
