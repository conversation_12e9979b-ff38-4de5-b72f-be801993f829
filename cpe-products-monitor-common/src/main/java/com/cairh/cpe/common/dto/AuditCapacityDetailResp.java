package com.cairh.cpe.common.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 见证人产能概况
 *
 * <AUTHOR>
 * @since 2025/6/4 16:40
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class AuditCapacityDetailResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 区域名称
     */
    private String area_name;

    /**
     * 任务数量500笔以上（当天）
     */
    private Integer task_count500_num;

    /**
     * 任务数量400-500笔（当天）
     */
    private Integer task_count400_num;

    /**
     * 任务数量300-400笔（当天）
     */
    private Integer task_count300_num;

    /**
     * 任务数量200-300笔（当天）
     */
    private Integer task_count200_num;

    /**
     * 任务数量100-200笔（当天）
     */
    private Integer task_count100_num;

    /**
     * 任务数量0-100笔（当天）
     */
    private Integer task_count0_num;

    /**
     * 用户产能详情
     */
    private List<CapacityUserDetail> capacityUserDetailList;

    @Data
    public static class CapacityUserDetail implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 操作员编号
         */
        private String operator_no;

        /**
         * 操作员名称
         */
        private String operator_name;

        /**
         * 所属机构编号
         */
        private String branch_no;

        /**
         * 所属机构名称
         */
        private String branch_name;

        /**
         * 产能类型
         * 1-0-100笔 2-100-200笔 3-200-300笔 4-300-400笔 5-400-500笔 6-500笔以上
         */
        private String capacity_type;

        /**
         * 笔数（当天）
         */
        private Integer num;
    }
}
