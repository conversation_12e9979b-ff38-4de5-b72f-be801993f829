package com.cairh.cpe.common.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 见证人在线分组详情
 *
 * <AUTHOR>
 * @since 2025/6/4 10:27
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class AuditGroupDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 小组名称
     */
    private String group_name;

    /**
     * 小组所在地区
     * 上海、黑龙江、兰州
     */
    private String group_area;

    /**
     * 人均笔数
     */
    private List<AuditUserInfo> auditUserInfoList;

    @Data
    public static class AuditUserInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 操作员编号
         */
        private String operator_no;

        /**
         * 操作员姓名
         */
        private String operator_name;

        /**
         * 状态 0-离线 1-在线，非派单状态 2-在线，派单中
         */
        private String status = "0";
    }

}
