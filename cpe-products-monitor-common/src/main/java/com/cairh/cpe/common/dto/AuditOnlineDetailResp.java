package com.cairh.cpe.common.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 见证人在线概况
 *
 * <AUTHOR>
 * @since 2025/6/4 10:23
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class AuditOnlineDetailResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 上线率（上线人数/总人数）
     */
    private String online_rate;

    /**
     * 上线人数
     */
    private Integer online_num;

    /**
     * 离线率（离线人数/总人数）
     */
    private String offline_rate;

    /**
     * 离线人数
     */
    private Integer offline_num;

    /**
     * 在线派单率（派单人数/上线人数）
     */
    private String dispatch_rate;

    /**
     * 在线派单人数
     */
    private Integer dispatch_num;

    /**
     * 小组详情
     */
    private List<AuditGroupDetail> auditGroupDetailList;
}
