package com.cairh.cpe.common.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 见证人承压概况
 *
 * <AUTHOR>
 * @since 2025/6/4 15:23
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class AuditPressureDetailResp implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 地区名称
     */
    private String area_name;

    /**
     * 承压人数
     */
    private Integer press_num;

    /**
     * 忙碌人数
     */
    private Integer busy_num;

    /**
     * 正常人数
     */
    private Integer normal_num;

    /**
     * 空闲人数
     */
    private Integer idle_num;

    /**
     * 承压用户详情
     */
    private List<PressureUserDetail> pressureUserDetailList;

    @Data
    public static class PressureUserDetail implements Serializable {

        /**
         * 操作员编号
         */
        private String operator_no;

        /**
         * 操作员名称
         */
        private String operator_name;

        /**
         * 所属机构编号
         */
        private String branch_no;

        /**
         * 所属机构名称
         */
        private String branch_name;

        /**
         * 承压类型
         * 1-承压 2-忙碌 3-正常 4-空闲
         */
        private String pressure_type;

        /**
         * 承压数（5分钟）
         */
        private Integer num;
    }
}
