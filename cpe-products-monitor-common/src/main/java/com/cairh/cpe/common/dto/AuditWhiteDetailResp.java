package com.cairh.cpe.common.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 绿通客户服务概况
 *
 * <AUTHOR>
 * @since 2025/6/5 19:10
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class AuditWhiteDetailResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 服务绿通客户数量
     */
    private Integer white_num;

    /**
     * 平均响应时长
     */
    private Integer avg_response_time;

    /**
     * 分支绿通详情
     */
    private List<BranchWhiteDetail> branchWhiteDetailList;

    @Data
    public static class BranchWhiteDetail implements Serializable {

        /**
         * 分支编号
         */
        private String branch_no;

        /**
         * 分支名称
         */
        private String branch_name;

        /**
         * 分支绿通客户申请数量
         */
        private Integer branch_white_num;

        /**
         * 分支已处理数量
         */
        private Integer branch_handle_num;

        /**
         * 响应时效
         * 开始处理时间-设置绿通时间
         */
        private Integer branch_avg_response_time;
    }
}
