package com.cairh.cpe.common.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 分支绿通任务统计
 *
 * <AUTHOR>
 * @since 2025/6/5 10:55
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AcBranchWhiteTaskCount implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分支编号
     */
    private String branch_no;

    /**
     * 分支名称
     */
    private String branch_name;

    /**
     * 分支绿通客户申请数量
     */
    private Integer branch_white_num;

    /**
     * 分支已处理数量
     */
    private Integer branch_handle_num;

    /**
     * 响应时效
     * 开始处理时间-设置绿通时间
     */
    private Integer branch_avg_response_time;
}
