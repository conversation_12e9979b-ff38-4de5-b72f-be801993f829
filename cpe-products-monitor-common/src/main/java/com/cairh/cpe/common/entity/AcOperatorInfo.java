package com.cairh.cpe.common.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 渠道定义表
 *
 * <AUTHOR>
 * @since 2022-02-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AcOperatorInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 员工编号
     */
    private String staff_no;

    /**
     * 员工名称
     */
    private String user_name;

    /**
     * 营业部编号
     */
    private String branch_no;

    /**
     * 营业部名称
     */
    private String branch_name;

    /**
     * 上级营业部编号
     */
    private String up_branch_no;
}
