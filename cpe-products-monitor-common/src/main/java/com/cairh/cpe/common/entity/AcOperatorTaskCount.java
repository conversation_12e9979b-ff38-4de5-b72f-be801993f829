package com.cairh.cpe.common.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 渠道定义表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AcOperatorTaskCount implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 员工编号
     */
    private String staff_no;

    /**
     * 员工名称
     */
    private String operator_name;

    /**
     * 任务量
     */
    private Integer num;
}
