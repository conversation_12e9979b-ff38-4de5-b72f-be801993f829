package com.cairh.cpe.common.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 * 渠道定义表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AcOperatorTodayTaskCount implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 员工编号
     */
    private String staff_no;

    /**
     * 员工姓名
     */
    private String operator_name;

    /**
     * 任务总量
     */
    private Integer total_num;

    /**
     * 见证总量
     */
    private Integer audit_num;

    /**
     * 审核总量
     */
    private Integer review_num;
}
