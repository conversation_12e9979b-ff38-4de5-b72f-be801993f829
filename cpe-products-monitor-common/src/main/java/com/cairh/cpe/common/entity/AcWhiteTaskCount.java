package com.cairh.cpe.common.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 绿通任务统计
 *
 * <AUTHOR>
 * @since 2025/6/5 10:51
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AcWhiteTaskCount implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 服务绿通客户数量
     */
    private Integer white_num;

    /**
     * 平均响应时长
     */
    private Integer avg_response_time;
}
