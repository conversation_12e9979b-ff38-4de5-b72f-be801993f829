package com.cairh.cpe.common.service;

import com.cairh.cpe.common.dto.AuditAlarmDetailResp;
import com.cairh.cpe.common.dto.AuditAlarmOperatorDetail;
import com.cairh.cpe.common.dto.AuditAlarmTaskDetail;

import java.util.List;

/**
 * 见证监控告警服务
 *
 * <AUTHOR>
 * @since 2025/7/1 09:55
 */
public interface IAuditAlarmService {

    /**
     * 待处理任务详情
     */
    AuditAlarmDetailResp.PendingTaskDetail getPendingTaskDetail();

    /**
     * 平均响应时长详情
     */
    AuditAlarmDetailResp.AvgResponseDetail getAvgResponseDetail();

    /**
     * 单笔任务等待时长详情
     */
    List<AuditAlarmTaskDetail> getTaskWaitDetailList();

    /**
     * 单笔任务分配失败详情
     */
    List<AuditAlarmTaskDetail> getFailedTaskAssignmentList();

    /**
     * 新进任务详情
     */
    List<AuditAlarmTaskDetail> getNewTaskList();

    /**
     * 用户操作详情
     */
    List<AuditAlarmOperatorDetail> getOperatorDetailList();

    /**
     * 自动驳回任务详情
     */
    AuditAlarmDetailResp.AutoRejectTaskDetail getAutoRejectTaskDetail();
}
