package com.cairh.cpe.common.service.impl;

import cn.hutool.core.date.DateUnit;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.cairh.cpe.common.constant.Constant;
import com.cairh.cpe.common.dto.AuditAlarmDetailResp;
import com.cairh.cpe.common.dto.AuditAlarmOperatorDetail;
import com.cairh.cpe.common.dto.AuditAlarmTaskDetail;
import com.cairh.cpe.common.dto.OperatorGroupVo;
import com.cairh.cpe.common.mapper.AuditReportMapper;
import com.cairh.cpe.common.service.IAuditAlarmService;
import com.cairh.cpe.common.util.ComponentWorkTimeService;
import com.cairh.cpe.common.util.DataUtil;
import com.cairh.cpe.common.util.DateUtil;
import com.cairh.cpe.common.util.RedisService;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import com.cairh.cpe.esb.base.rpc.IVBaseOnlineUserDubboService;
import com.cairh.cpe.esb.base.rpc.dto.resp.OnlineUserResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.common.utils.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/7/1 09:59
 */
@Slf4j
@Service
public class AuditAlarmServiceImpl implements IAuditAlarmService {

    @Resource
    protected CompositePropertySources compositePropertySources;
    @Resource
    private AuditReportMapper auditReportMapper;
    @Resource
    private ComponentWorkTimeService componentWorkTimeService;
    @Autowired
    private RedisService redisService;
    @DubboReference(check = false, lazy = true)
    private IVBaseOnlineUserDubboService baseOnlineUserDubboService;

    /**
     * 获取待处理任务详情
     */
    @Override
    public AuditAlarmDetailResp.PendingTaskDetail getPendingTaskDetail() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("getPendingTaskDetail start");
        log.debug("开始获取待处理任务详情");
        // 工作时段（交易日）待处理任务（不含挂起）阈值
        int workNumConfig = Integer.parseInt(compositePropertySources.getProperty(Constant.MONITOR_ALARM_TASK_WORK_PENDING_NUM));
        // 非工作时段待处理任务（不含挂起）阈值
        int nonWorkNumConfig = Integer.parseInt(compositePropertySources.getProperty(Constant.MONITOR_ALARM_TASK_NON_WORK_PENDING_NUM));
        Integer pendingTaskCount = auditReportMapper.getPendingTaskCount();
        AuditAlarmDetailResp.PendingTaskDetail pendingTaskDetail = new AuditAlarmDetailResp.PendingTaskDetail();
        if (Objects.nonNull(pendingTaskCount)) {
            pendingTaskDetail.setPending_task_num(pendingTaskCount);
            setAlarmType(pendingTaskCount, workNumConfig, nonWorkNumConfig, pendingTaskDetail::setType);
        }
        log.debug("成功获取待处理任务详情，任务数量: {}", pendingTaskCount);
        stopWatch.stop();
        log.info("getPendingTaskDetail end, cost: {}ms", stopWatch.getLastTaskTimeMillis());
        return pendingTaskDetail;
    }

    /**
     * 获取平均响应详情
     */
    @Override
    public AuditAlarmDetailResp.AvgResponseDetail getAvgResponseDetail() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("getAvgResponseDetail start");
        log.debug("开始获取平均响应详情");
        // 工作时段平均响应时长（秒）
        int avgResponseTimeWorkConfig = Integer.parseInt(compositePropertySources.getProperty(Constant.MONITOR_ALARM_TASK_AVG_RESPONSE_TIME));
        // 非工作时段平均响应时长（秒）
        int avgResponseTimeNonWorkConfig = Integer.parseInt(compositePropertySources.getProperty(Constant.MONITOR_ALARM_NON_WORK_TASK_AVG_RESPONSE_TIME));
        String time_property = componentWorkTimeService.isWorkTime(Constant.WORK_TIME_AUDIT_WORKING) ? Constant.TIME_PROPERTY_1 : Constant.TIME_PROPERTY_2;
        // 获取平均响应时长
        Integer avgResponseTimeCount = auditReportMapper.getAvgResponseTime(time_property);
        AuditAlarmDetailResp.AvgResponseDetail avgResponseDetail = new AuditAlarmDetailResp.AvgResponseDetail();
        if (Objects.nonNull(avgResponseTimeCount)) {
            avgResponseDetail.setAvg_response_time(avgResponseTimeCount);
            setAlarmType(avgResponseTimeCount, avgResponseTimeWorkConfig, avgResponseTimeNonWorkConfig, avgResponseDetail::setType);
        }
        log.debug("成功获取平均响应详情，信息: {}", JSON.toJSONString(avgResponseDetail));
        stopWatch.stop();
        log.info("getAvgResponseDetail end, cost: {}", stopWatch.getLastTaskTimeMillis());
        return avgResponseDetail;
    }

    /**
     * 获取单笔任务等待时长详情
     */
    @Override
    public List<AuditAlarmTaskDetail> getTaskWaitDetailList() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("getTaskWaitDetailList start");
        log.debug("开始获取单笔任务等待时长详情");
        // 工作时段单笔任务等待时长（分钟）
        int taskWaitTimeWorkConfig = Integer.parseInt(compositePropertySources.getProperty(Constant.MONITOR_ALARM_TASK_WAIT_TIME));
        // 非工作时段单笔任务等待时长（分钟）
        int taskWaitTimeNonWorkConfig = Integer.parseInt(compositePropertySources.getProperty(Constant.MONITOR_ALARM_NON_WORK_TASK_WAIT_TIME));
        // 获取单笔任务等待时长最长的30条客户信息
        List<AuditAlarmTaskDetail> acWaitTaskTop30List = auditReportMapper.getWaitTaskTop30List();
        List<AuditAlarmTaskDetail> result = acWaitTaskTop30List.stream().peek(taskDetail -> {
            // 计算与当前时间差异，单位：分钟
            int diffDateTime = diffDateTime(taskDetail.getRequest_datetime(), new Date());
            taskDetail.setWait_time(diffDateTime);
            setAlarmType(diffDateTime, taskWaitTimeWorkConfig, taskWaitTimeNonWorkConfig, taskDetail::setType);
            taskDetail.setId_no(DataUtil.desensitized(taskDetail.getId_no()));
        }).collect(Collectors.toList());
        log.debug("成功获取单笔任务等待时长详情，数量: {}", result.size());
        stopWatch.stop();
        log.info("getTaskWaitDetailList end, cost: {}", stopWatch.getLastTaskTimeMillis());
        return result;
    }

    /**
     * 获取单笔任务分配失败详情
     */
    @Override
    public List<AuditAlarmTaskDetail> getFailedTaskAssignmentList() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("getFailedTaskAssignmentList start");
        log.debug("开始获取单笔任务分配失败详情");
        // 工作时段单笔任务分配失败次数
        int failedTaskAssignmentWorkConfig = Integer.parseInt(compositePropertySources.getProperty(Constant.MONITOR_ALARM_TASK_ASSIGNMENT_FAIL_NUM));
        // 非工作时段单笔任务分配失败次数
        int failedTaskAssignmentNonWorkConfig = Integer.parseInt(compositePropertySources.getProperty(Constant.MONITOR_ALARM_NON_WORK_TASK_ASSIGNMENT_FAIL_NUM));
        // 获取单笔任务分配失败次数最长的30条客户信息
        List<AuditAlarmTaskDetail> failedAssignmentTaskTop30List = auditReportMapper.getFailedAssignmentTaskTop30List();
        List<AuditAlarmTaskDetail> result = failedAssignmentTaskTop30List.stream().peek(taskDetail -> {
            setAlarmType(taskDetail.getFail_assignment_num(), failedTaskAssignmentWorkConfig, failedTaskAssignmentNonWorkConfig, taskDetail::setType);
            taskDetail.setId_no(DataUtil.desensitized(taskDetail.getId_no()));
        }).collect(Collectors.toList());

        log.debug("成功获取单笔任务分配失败详情，数量: {}", result.size());
        stopWatch.stop();
        log.info("getFailedTaskAssignmentList end, cost: {}ms", stopWatch.getLastTaskTimeMillis());
        return result;
    }

    /**
     * 获取新进任务详情
     */
    @Override
    public List<AuditAlarmTaskDetail> getNewTaskList() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("getNewTaskList start");
        log.debug("开始获取新进任务详情");
        // 系统工作时段未进任务时长（分钟）
        int newTaskWorkConfig = Integer.parseInt(compositePropertySources.getProperty(Constant.MONITOR_ALARM_TASK_NEW_TIME));
        // 系统工作时段未进任务时长（分钟）
        int newTaskNonWorkConfig = Integer.parseInt(compositePropertySources.getProperty(Constant.MONITOR_ALARM_NON_WORK_TASK_NEW_TIME));
        // 新进任务展示数量，默认：5
        int showNum = Integer.parseInt(compositePropertySources.getProperty(Constant.MONITOR_ALARM_TASK_NEW_SHOW_NUM, Constant.DEFAULT_TASK_NEW_SHOW_NUM));
        // 获取最新申请的x笔客户信息
        List<AuditAlarmTaskDetail> newTaskTopList = auditReportMapper.getNewTaskTopList(showNum);
        List<AuditAlarmTaskDetail> result = newTaskTopList.stream().peek(taskDetail -> {
            // 计算与当前时间差异，单位：分钟
            int diffDateTime = diffDateTime(taskDetail.getRequest_datetime(), new Date());
            taskDetail.setInterval_time(diffDateTime);
            // 格式化时间为字符串：yyyy-MM-dd HH:mm:ss
            taskDetail.setNew_task_datetime(DateUtil.formatDateTime(taskDetail.getRequest_datetime(), "yyyy-MM-dd HH:mm:ss"));
            setAlarmType(diffDateTime, newTaskWorkConfig, newTaskNonWorkConfig, taskDetail::setType);
        }).collect(Collectors.toList());
        log.debug("成功获取新进任务详情，数量: {}", result.size());
        stopWatch.stop();
        log.info("getNewTaskList end, cost: {}", stopWatch.getLastTaskTimeMillis());
        return result;

    }

    /**
     * 获取用户操作详情
     */
    @Override
    public List<AuditAlarmOperatorDetail> getOperatorDetailList() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("getOperatorDetailList start");
        log.debug("开始获取用户操作详情");
        // 获取系统在线的操作员
        List<String> onlineUserList = baseOnlineUserDubboService.onlineUserInfo().stream().map(OnlineUserResponse::getStaff_no).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(onlineUserList)) {
            log.info("[getOperatorDetailList] 获取系统在线的操作员为空!");
            return new ArrayList<>();
        }
        // 获取系统参数配置
        String groupConfig = compositePropertySources.getProperty(Constant.MONITOR_AC_OPERATOR_GROUP);
        if (StringUtils.isBlank(groupConfig)) {
            log.warn("[getOperatorDetailList] 获取系统参数配置={}为空", Constant.MONITOR_AC_OPERATOR_GROUP);
            return new ArrayList<>();
        }
        List<OperatorGroupVo> groupList = JSONArray.parseArray(groupConfig).toJavaList(OperatorGroupVo.class);
        // 所有操作员，去重
        List<String> operatorNos = groupList.stream()
                .map(group -> Arrays.asList(group.getGroup_operators().split(Constant.COMMA)))
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
        log.info("[getOperatorDetailList] 获取所有操作员={}", JSON.toJSONString(operatorNos));
        // 配置组的操作员并且在派单中的操作员
        List<String> workingOperatorNos = operatorNos.stream().filter(onlineUserList::contains).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workingOperatorNos)) {
            log.info("[getOperatorDetailList] 配置组的操作员并且在派单中的操作员为空!");
            return new ArrayList<>();
        }
        // 单坐席未有操作记录时长（分钟）
        int operatorIdleWorkConfig = Integer.parseInt(compositePropertySources.getProperty(Constant.MONITOR_ALARM_OPERATOR_IDLE_TIME));
        // 非工作时段单坐席未有操作记录时长（分钟）
        int operatorIdleNonWorkConfig = Integer.parseInt(compositePropertySources.getProperty(Constant.MONITOR_ALARM_NON_WORK_OPERATOR_IDLE_TIME));
        // 获取空闲时间最长的30条坐席信息-见证任务
        List<AuditAlarmOperatorDetail> auditOperatorIdleTop30List = auditReportMapper.getAuditOperatorIdleTop30List(workingOperatorNos);
        // 获取空闲时间最长的30条坐席信息-质检任务
        List<AuditAlarmOperatorDetail> qcOperatorIdleTop30List = auditReportMapper.getQCOperatorIdleTop30List(workingOperatorNos);
        // 合并两个接口，并且去重，保留处理时间最新的数据
        List<AuditAlarmOperatorDetail> alarmOperatorDetailList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(auditOperatorIdleTop30List)) {
            alarmOperatorDetailList.addAll(auditOperatorIdleTop30List);
        }
        if (CollectionUtils.isNotEmpty(qcOperatorIdleTop30List)) {
            alarmOperatorDetailList.addAll(qcOperatorIdleTop30List);
        }
        List<AuditAlarmOperatorDetail> result = alarmOperatorDetailList.stream()
                .sorted(Comparator.comparing(AuditAlarmOperatorDetail::getDeal_datetime).reversed())
                .peek(operatorDetail -> {
                    // 计算与当前时间差异，单位：分钟
                    int diffDateTime = diffDateTime(operatorDetail.getDeal_datetime(), new Date());
                    operatorDetail.setIdle_time(diffDateTime);
                    setAlarmType(diffDateTime, operatorIdleWorkConfig, operatorIdleNonWorkConfig, operatorDetail::setType);
                })
                .collect(Collectors.toList());
        log.debug("成功获取用户操作详情，数量: {}", result.size());
        stopWatch.stop();
        log.info("getOperatorDetailList end, cost: {}", stopWatch.getLastTaskTimeMillis());
        return result;
    }

    /**
     * 计算时间差（分钟）
     *
     * @param startDate
     * @param endDate
     * @return
     */
    private int diffDateTime(Date startDate, Date endDate) {
        return (int) cn.hutool.core.date.DateUtil.between(startDate, endDate, DateUnit.MINUTE);
    }

    /**
     * 设置告警类型
     */
    private void setAlarmType(int actualValue, int workTimeThreshold, int nonWorkTimeThreshold,
                              java.util.function.Consumer<String> typeSetter) {
        int threshold = componentWorkTimeService.isWorkTime(Constant.WORK_TIME_AUDIT_WORKING) ? workTimeThreshold : nonWorkTimeThreshold;
        if (actualValue >= threshold) {
            typeSetter.accept(Constant.ONE_STR);
        }
    }
}
