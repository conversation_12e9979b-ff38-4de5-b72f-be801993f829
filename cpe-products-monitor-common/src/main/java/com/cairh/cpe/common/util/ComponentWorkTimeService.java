package com.cairh.cpe.common.util;

import com.cairh.cpe.esb.base.rpc.IVBaseWorkTimeDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseWorkTimeIsRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 工作时间
 */
@Slf4j
@Service
public class ComponentWorkTimeService {

    @DubboReference(check = false)
    private IVBaseWorkTimeDubboService baseWorkTimeDubboService;


    /**
     * 工作时间检查
     *
     * @param time_kind
     * @return
     */
    public boolean isWorkTime(String time_kind) {
        VBaseWorkTimeIsRequest req = new VBaseWorkTimeIsRequest();
        req.setTime_kind(time_kind);
        return baseWorkTimeDubboService.baseDataWorkTimeIs(req).getIs_work_time();
    }
}
