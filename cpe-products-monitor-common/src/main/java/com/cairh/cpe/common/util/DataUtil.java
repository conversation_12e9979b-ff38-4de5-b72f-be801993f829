package com.cairh.cpe.common.util;

import org.apache.commons.lang3.StringUtils;

/**
 * 数据处理
 *
 * <AUTHOR>
 * @since 2025/7/1 15:08
 */
public class DataUtil {

    /**
     * 身份证号码脱敏
     * <p>
     * 1. 单字符显示原字符
     * 2. 两字符显示第一个字符,第二个字符用*代替
     * 3. 三字符及以上显示第一个和最后一个字符,中间用*代替
     */
    public static String desensitized(String idNo) {
        // 空值校验
        if (StringUtils.isEmpty(idNo)) {
            return idNo;
        }

        int length = idNo.length();
        if (length <= 6) {
            switch (length) {
                case 1:
                    return idNo;
                case 2:
                    return idNo.charAt(0) + "*";
                case 3:
                    return idNo.charAt(0) + "*" + idNo.charAt(2);
                case 4:
                    return idNo.charAt(0) + "**" + idNo.substring(3);
                case 5:
                    return idNo.charAt(0) + "***" + idNo.substring(4);
                case 6:
                    return idNo.charAt(0) + "****" + idNo.substring(5);
                default:
                    return idNo;
            }
        }

        // 处理长度大于6的情况
        return idNo.substring(0, 6) +
                StringUtils.repeat("*", length - 6) +
                idNo.substring(length - 1);
    }
}
