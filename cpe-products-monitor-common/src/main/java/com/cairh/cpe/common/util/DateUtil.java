package com.cairh.cpe.common.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

public class DateUtil extends DateUtils {

    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    private static Logger logger = LoggerFactory.getLogger(DateUtil.class);

    /**
     * 获取两个日期之间间隔的秒数
     *
     * @param startDate
     * @param endDate
     * @return
     */
    public static float getIntervalSeconds(Date startDate, Date endDate) {
        float seconds = 0.0f;
        if (null != startDate && null != endDate) {
            long end = endDate.getTime();
            long start = startDate.getTime();
            Double betweenHour = (end - start) / 1000.0;
            seconds = betweenHour.floatValue();
        }
        return seconds;
    }

    /**
     * 计算下一个自然日
     *
     * @param curr_date
     * @return
     */
    public static String getNextDate(String curr_date) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(sdf.parse(curr_date));
            calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) + 1);// 日期加days
            return sdf.format(calendar.getTime());
        } catch (ParseException e) {
            logger.debug("传入的日期[%s]格式不正确,无法计算下一个自然日,直接返回空", e);
            return "";
        }
    }

    /**
     * 判断当前日期最近一个交易日是否在esb最新历史数据的交易日之前
     *
     * @param recentDate
     * @param billDate
     * @return
     */
    public static boolean isDayBefore(String recentDate, String billDate) {
        LocalDate recent = LocalDate.parse(recentDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
        LocalDate bill = LocalDate.parse(billDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
        return recent.isBefore(bill);
    }

    /**
     * 判断当前日期最近一个交易日是否在esb最新历史数据的交易日之后
     *
     * @param recentDate
     * @param billDate
     * @return
     */
    public static boolean isDayAfter(String recentDate, String billDate) {
        LocalDate recent = LocalDate.parse(recentDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
        LocalDate bill = LocalDate.parse(billDate, DateTimeFormatter.ofPattern("yyyyMMdd"));
        return recent.isAfter(bill);
    }

    /**
     * 获取某天的开始时间
     *
     * @param today     日期
     * @param formatter 日期格式 默认 yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static String getStartTime(LocalDate today, DateTimeFormatter formatter) {
        // 定义日期时间格式化器
        if (formatter == null) {
            formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        }

        return today.atStartOfDay().format(formatter);
    }

    /**
     * 获取某天的结束时间
     *
     * @param today     日期
     * @param formatter 日期格式 默认 yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static String getEndTime(LocalDate today, DateTimeFormatter formatter) {
        // 定义日期时间格式化器
        if (formatter == null) {
            formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        }

        return today.atTime(LocalTime.MAX).format(formatter);
    }

    /**
     * 格式化日期为字符串（考虑时区）
     *
     * @param date 日期对象
     * @return 格式化后的字符串 yyyy-MM-dd HH:mm:ss（24小时制），如果日期为null则返回空字符串
     */
    public static String formatDateTime(Date date, String pattern) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        // 设置时区为中国标准时间
        sdf.setTimeZone(java.util.TimeZone.getTimeZone("Asia/Shanghai"));
        return sdf.format(date);
    }

    /**
     * 将util.Date对象转换为LocalDate对象
     *
     * @param utilDate
     * @return
     */
    public static LocalDate convertUtilDateToLocalDate(Date utilDate) {
        if (utilDate == null) {
            return null;
        }
        // 将util.Date对象转换为Instant对象
        Instant instant = utilDate.toInstant();
        // 将Instant对象转换为LocalDate对象，使用系统默认时区
        ZoneId systemZone = ZoneId.systemDefault();
        return instant.atZone(systemZone).toLocalDate();
    }

    /**
     * 将字符串转换成Date类型
     *
     * @param dateString
     * @param dateFormat
     * @return
     */
    public static Date formatStrDateTime(String dateString, String dateFormat) throws ParseException {
        if (StringUtils.isEmpty(dateString)) {
            return null;
        }
        if (StringUtils.isBlank(dateFormat)) {
            dateFormat = DATE_TIME_FORMAT;
        }
        return DateUtils.parseDate(dateString, dateFormat);
    }

    /**
     * 得到当前日期字符串 格式（yyyy-MM-dd）
     */
    public static String getDate() {
        return getDate("yyyy-MM-dd");
    }

    /**
     * 得到当前日期字符串 格式（yyyy-MM-dd） pattern可以为："yyyy-MM-dd" "HH:mm:ss" "E"
     */
    public static String getDate(String pattern) {
        return FastDateFormat.getInstance(pattern).format(new Date());
    }


    /**
     * 获取昨天的日期，格式为 yyyy-MM-dd
     *
     * @return 昨天的日期字符串
     */
    public static String getYesterdayDate() {
        // 获取当前日期
        Date now = new Date();
        // 获取昨天的日期
        Date yesterday = cn.hutool.core.date.DateUtil.offsetDay(now, -1);
        // 格式化日期为 yyyy-MM-dd 格式
        return cn.hutool.core.date.DateUtil.format(yesterday, "yyyy-MM-dd");
    }

}