package com.cairh.cpe.common.util;

import com.cairh.cpe.common.constant.Constant;
import com.cairh.cpe.common.util.function.LockFunction;
import com.cairh.cpe.context.BizException;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisServerCommands;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import static com.cairh.cpe.common.constant.MonitorErrorCode.REPEATED_EXECUTION_ERROR;


/**
 * redis服务类
 *
 * <AUTHOR>
 * @since 2023/10/7 15:48
 */
@Service
public class RedisService {
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private RedissonClient redissonClient;

    /**
     * 设置秒级的锁
     *
     * @param key
     * @param timeout
     * @return Boolean
     * <AUTHOR>
     * @since 2023/11/4 15:24
     */
    public Boolean setIfAbsent(String key, long timeout) {
        return stringRedisTemplate.opsForValue().setIfAbsent(key, Constant.ONE_STR, timeout, TimeUnit.SECONDS);
    }

    /**
     * 获取锁并执行
     *
     * @param key
     * @param lockFunction
     * @param consumer
     * <AUTHOR>
     * @since 2023/10/8 09:04
     */
    public void doLockFunction(String key, LockFunction lockFunction, Consumer<Exception> consumer) {
        RLock lock = redissonClient.getLock(key);
        try {
            if ((!lock.tryLock(0, 180, TimeUnit.SECONDS))) {
                return;
            }
            lockFunction.execute();
        } catch (Exception e) {
            consumer.accept(e);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取锁校验并执行
     *
     * @param key
     * @param lockFunction
     * @param consumer
     * <AUTHOR>
     * @since 2023/10/8 09:09
     */
    public void checkAndDoLockFunction(String key, LockFunction lockFunction, Consumer<Exception> consumer) {
        RLock lock = redissonClient.getLock(key);
        if (!lock.tryLock()) {
            throw new BizException(REPEATED_EXECUTION_ERROR, "操作频繁,请勿重复点击");
        }
        try {
            lockFunction.execute();
        } catch (Exception e) {
            consumer.accept(e);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 删除redis指定key
     *
     * @param key
     * @return Boolean
     * <AUTHOR>
     * @since 2023/10/11 21:53
     */
    public Boolean delete(String key) {
        return stringRedisTemplate.delete(key);
    }

    /**
     * 获取当前时间
     *
     * @return long
     * <AUTHOR>
     * @since 2023/10/11 13:21
     */
    public long getMillisecond() {
        try {
            Long execute = stringRedisTemplate.execute((RedisCallback<Long>) RedisServerCommands::time);
            if (Objects.isNull(execute)) {
                return Constant.ZERO;
            }
            return execute;
        } catch (Exception e) {
            return Constant.ZERO;
        }
    }

    /**
     * 消息推送
     *
     * @param channel
     * @param message
     * <AUTHOR>
     * @since 2023/10/6 16:47
     */
    public void publish(String channel, String message) {
        stringRedisTemplate.convertAndSend(channel, message);
    }

    /**
     * zSet结构添加数据
     *
     * @param key
     * @param value
     * @param score
     * @return Boolean
     * <AUTHOR>
     * @since 2023/9/19 15:32
     */
    public Boolean zSetAdd(String key, String value, double score) {
        return stringRedisTemplate.opsForZSet().add(key, value, score);
    }

    /**
     * zSet结构判断是否为其元素
     *
     * @param key
     * @param value
     * @return boolean
     * <AUTHOR>
     * @since 2023/10/8 09:54
     */
    public boolean zSetIsMember(String key, String value) {
        return Objects.nonNull(stringRedisTemplate.opsForZSet().rank(key, value));
    }

    /**
     * 从zSet结构中移除数据
     *
     * @param key
     * @param value
     * @return Long
     * <AUTHOR>
     * @since 2023/9/25 15:54
     */
    public Long zSetRemove(String key, String value) {
        return stringRedisTemplate.opsForZSet().remove(key, value);
    }

    /**
     * 批量删除
     *
     * @param key
     * @param values
     * @return Long
     * <AUTHOR>
     * @since 2023/11/2 17:04
     */
    public Long zSetRemoveList(String key, Object[] values) {
        return stringRedisTemplate.opsForZSet().remove(key, values);
    }

    /**
     * 获取zSet中值的分数
     *
     * @param key
     * @param value
     * @return Double
     * <AUTHOR>
     * @since 2023/10/20 15:36
     */
    public Double zSetGetScore(String key, String value) {
        return stringRedisTemplate.opsForZSet().score(key, value);
    }

    /**
     * 获取zSet中元素的个数
     *
     * @param key
     * @return Long
     * <AUTHOR>
     * @since 2023/11/2 09:28
     */
    public Long zSetGetSize(String key) {
        return stringRedisTemplate.opsForZSet().zCard(key);
    }

    /**
     * 从zSet类型的数据中取出指定索引的数据
     *
     * @param key
     * @param start
     * @param end
     * @return List<java.lang.String>
     * <AUTHOR>
     * @since 2023/9/19 19:14
     */
    public List<String> zSetGetValueWithIndex(String key, int start, int end) {
        if (end < Constant.ZERO) {
            return new ArrayList<>();
        }
        return Optional.ofNullable(stringRedisTemplate.opsForZSet().range(key, start, end)).map(ArrayList::new).orElse(new ArrayList<>());
    }

    /**
     * 从zSet类型的数据中获取所有的数据值
     *
     * @param key
     * @return List<java.lang.String>
     * <AUTHOR>
     * @since 2023/10/20 15:30
     */
    public List<String> zSetGetAllValue(String key) {
        return Optional.ofNullable(stringRedisTemplate.opsForZSet().range(key, Constant.ZERO, Constant.NEGATIVE_ONE)).map(ArrayList::new).orElse(new ArrayList<>());
    }

    /**
     * 从zSet类型的数据中获取所有的数据(包含分数)
     *
     * @param key
     * @return Set<org.springframework.data.redis.core.ZSetOperations.TypedTuple < java.lang.String>>
     * <AUTHOR>
     * @since 2023/11/8 10:05
     */
    public Set<ZSetOperations.TypedTuple<String>> zSetGetByScoreWithAll(String key) {
        return stringRedisTemplate.opsForZSet().rangeWithScores(key, Constant.ZERO, Constant.NEGATIVE_ONE);
    }

    /**
     * 将集合填写进list类型的数据中
     *
     * @param key
     * @param valueList
     * @return Long
     * <AUTHOR>
     * @since 2023/9/19 20:06
     */
    public Long listAddList(String key, List<String> valueList) {
        return stringRedisTemplate.opsForList().rightPushAll(key, valueList);
    }

    /**
     * 往list中添加数量
     *
     * @param key
     * @param value
     * @return Long
     * <AUTHOR>
     * @since 2023/11/2 10:31
     */
    public Long listAdd(String key, String value) {
        return stringRedisTemplate.opsForList().rightPush(key, value);
    }

    /**
     * 获取list中元素的数量
     *
     * @param key
     * @return Long
     * <AUTHOR>
     * @since 2023/11/2 10:31
     */
    public Long listGetSize(String key) {
        return stringRedisTemplate.opsForList().size(key);
    }

    /**
     * 获取list中所有的元素
     *
     * @param key
     * @return List<java.lang.String>
     * <AUTHOR>
     * @since 2023/11/8 13:50
     */
    public List<String> listGetAll(String key) {
        return stringRedisTemplate.opsForList().range(key, Constant.ZERO, Constant.NEGATIVE_ONE);
    }

    /**
     * 判断list是否存在该元素
     *
     * @param key
     * @param value
     * @return boolean
     * <AUTHOR>
     * @since 2023/10/16 22:34
     */
    public boolean listIsMember(String key, String value) {
        Long index = stringRedisTemplate.opsForList().indexOf(key, value);
        return Optional.ofNullable(index).map(tmp -> tmp > Constant.NEGATIVE_ONE).orElse(Boolean.FALSE);
    }

    /**
     * 从list类型中取左边第一个数据
     *
     * @param key
     * @return String
     * <AUTHOR>
     * @since 2023/9/20 09:34
     */
    public String listPopFirst(String key) {
        return stringRedisTemplate.opsForList().leftPop(key);
    }

    /**
     * 往set里面添加数据
     *
     * @param key
     * @param value
     * @return Long
     * <AUTHOR>
     * @since 2023/11/8 13:34
     */
    public Long setAdd(String key, String value) {
        return stringRedisTemplate.opsForSet().add(key, value);
    }

    /**
     * 添加元素并增加过期时间
     *
     * @param key
     * @param value
     * @param date
     * <AUTHOR>
     * @since 2023/11/3 14:09
     */
    public void setAdd(String key, String value, Date date) {
        setAdd(key, value);
        Long expire = stringRedisTemplate.getExpire(key, TimeUnit.SECONDS);
        if (Objects.isNull(expire) || expire <= Constant.ZERO) {
            stringRedisTemplate.expireAt(key, date);
        }
    }

    /**
     * 从set中移除元素
     *
     * @param key
     * @param value
     * @return Long
     * <AUTHOR>
     * @since 2023/11/8 13:36
     */
    public Long setRemove(String key, String value) {
        return stringRedisTemplate.opsForSet().remove(key, value);
    }

    /**
     * 判断是否为set中的元素
     *
     * @param key
     * @param value
     * @return Boolean
     * <AUTHOR>
     * @since 2023/11/8 13:36
     */
    public Boolean setIsMember(String key, String value) {
        return stringRedisTemplate.opsForSet().isMember(key, value);
    }

    /**
     * 从set类型的数据中获取所有的数据
     *
     * @param key
     * @return Set<java.lang.String>
     * <AUTHOR>
     * @since 2023/9/25 13:47
     */
    public Set<String> setGetAll(String key) {
        return stringRedisTemplate.opsForSet().members(key);
    }

    /**
     * hash中是否有该值
     *
     * @param key
     * @param hashKey
     * @return Boolean
     * <AUTHOR>
     * @since 2023/11/9 19:54
     */
    public Boolean hashHasKey(String key, String hashKey) {
        return stringRedisTemplate.opsForHash().hasKey(key, hashKey);
    }

    /**
     * 获取hash结构中对应key的hash值
     *
     * @param key
     * @param hashKey
     * @return Object
     * <AUTHOR>
     * @since 2023/9/25 14:47
     */
    public Object hashGet(String key, String hashKey) {
        return stringRedisTemplate.opsForHash().get(key, hashKey);
    }

    /**
     * 获取hash结构中的对象值
     *
     * @param key
     * @return Map<java.lang.Object, java.lang.Object>
     * <AUTHOR>
     * @since 2023/9/25 14:46
     */
    public Map<Object, Object> hashGetAll(String key) {
        return stringRedisTemplate.opsForHash().entries(key);
    }

    /**
     * hash结构中hashKey对应的值新增1
     *
     * @param key
     * @param hashKey
     * @param delta
     * @return long
     * <AUTHOR>
     * @since 2023/10/6 13:35
     */
    public long hashIncrement(String key, String hashKey, long delta) {
        return stringRedisTemplate.opsForHash().increment(key, hashKey, delta);
    }

    /**
     * 修改hash结构中对应hashKey的值
     *
     * @param key
     * @param hashKey
     * @param hashValue
     * <AUTHOR>
     * @since 2023/9/25 15:22
     */
    public void hashPut(String key, String hashKey, Object hashValue) {
        stringRedisTemplate.opsForHash().put(key, hashKey, hashValue);
    }

    /**
     * 修改hash结构中的值
     *
     * @param key
     * @param map
     * <AUTHOR>
     * @since 2023/9/25 15:21
     */
    public void hashPutAll(String key, Map<?, ?> map) {
        stringRedisTemplate.opsForHash().putAll(key, map);
    }

    /**
     * 获取hash结构中值个数
     *
     * @param key
     * @return Long
     * <AUTHOR>
     * @since 2023/11/9 19:41
     */
    public Long hashGetSize(String key) {
        return stringRedisTemplate.opsForHash().size(key);
    }

    /**
     * 删除hash结构中的值
     *
     * @param key
     * @param hashKey
     * @return Long
     * <AUTHOR>
     * @since 2023/10/6 14:14
     */
    public Long hashDelete(String key, String hashKey) {
        return stringRedisTemplate.opsForHash().delete(key, hashKey);
    }

    public boolean exists(String redisKey) {
        return stringRedisTemplate.hasKey(redisKey);
    }
}
