package com.cairh.cpe.common.util;

import com.cairh.cpe.common.constant.Constant;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 各对接系统业务参数
 *
 * <AUTHOR>
 * @since 2023/9/19 10:16
 */
@Service
public class SysParamService {

    @Autowired
    protected CompositePropertySources compositePropertySources;

    /**
     * 获取见证系统承压统计单位时间
     *
     * @return List<java.lang.String>
     * <AUTHOR>
     * @since 2023/10/5 10:32
     */
    public String getMonitorAcStatisticsUnitTime() {
        return compositePropertySources.getProperty(Constant.MONITOR_AC_STATISTICS_UNIT_TIME, Constant.FIVE_STR);
    }


    /**
     * 获取见证系统分钟产能单位时间
     */
    public String getMonitorAcMinuteStatisticsUnitTime() {
        return compositePropertySources.getProperty(Constant.MONITOR_AC_MINUTE_STATISTICS_UNIT_TIME, Constant.ONE_STR);
    }

    /**
     * 获取导出的最大条数
     *
     * @return
     */
    public String getUploadMaxSize() {
        return compositePropertySources.getProperty(Constant.UPLOAD_MAX_SIZE, Constant.TEN_THOUSAND + "");
    }
}
