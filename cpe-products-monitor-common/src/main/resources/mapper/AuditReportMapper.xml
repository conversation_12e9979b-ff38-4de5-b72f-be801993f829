<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cairh.cpe.common.mapper.AuditReportMapper">

    <select id="getAcHeadOperatorInfoList" resultType="com.cairh.cpe.common.entity.AcOperatorInfo">
        select a.staff_no,
               a.user_name,
               a.branch_no,
               b.branch_name,
               b.up_branch_no
        from crh_user.operatorinfo a,
             crh_user.allbranch b
        where instr(',' || a.en_roles || ',', ',1,') > 0
          and a.branch_no = b.branch_no
          and b.branch_no = '3'
    </select>

    <select id="getAcOperatorInfoList" resultType="com.cairh.cpe.common.entity.AcOperatorInfo">
        select a.staff_no,
        a.user_name,
        a.branch_no,
        b.branch_name,
        b.up_branch_no
        from crh_user.operatorinfo a,
        crh_user.allbranch b
        where a.branch_no = b.branch_no
        and staff_no in
        <foreach item="item" collection="operatorNos" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getAcOperatorTaskCount" resultType="com.cairh.cpe.common.entity.AcOperatorTaskCount">
        select operator_no as staff_no,
        operator_name,
        count(1) as num
        from crh_ads.dispatchtask
        where dispatch_status = '3'
        and subsys_id = '24'
        and finish_datetime > (sysdate - ${unitTime} / 1440)
        and operator_no in
        <foreach item="item" collection="operatorNos" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by operator_no, operator_name
    </select>

    <select id="getAcOperatorTodayTaskCount" resultType="com.cairh.cpe.common.entity.AcOperatorTodayTaskCount">
        select
        operator_no as staff_no,
        operator_name,
        count(1) as total_num
        from crh_ads.dispatchtask
        where dispatch_status = '3'
        and subsys_id = '24'
        and finish_datetime >= trunc(sysdate)
        and finish_datetime &lt; trunc(sysdate + 1)
        and operator_no in
        <foreach item="item" collection="operatorNos" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by operator_no, operator_name
    </select>

    <select id="getAcWhiteTaskCount" resultType="com.cairh.cpe.common.entity.AcWhiteTaskCount">
        select nvl(decode(task_num, 0, 0, round(response_time / task_num)), 0) as avg_response_time,
               white_num
        from (select nvl(sum(decode(a.task_status, '3', 1, '4', 1, '8', 1, 0)), 0) as task_num,
                     count(distinct request_no)                                    as white_num,
                     round(nvl(sum(decode(a.task_status, '3', (a.deal_datetime - a.white_datetime), '4',
                                          (a.deal_datetime - a.white_datetime), '8',
                                          (a.deal_datetime - a.white_datetime),
                                          0)), 0) * 24 * 60 * 60)                  as response_time
              from crh_ac.flowtaskrecorddetails a
              where a.white_flag = '1'
                and a.white_datetime >= trunc(sysdate)
                and a.white_datetime &lt; trunc(sysdate + 1)
                and a.invalid_flag = '0')
    </select>

    <select id="getAcBranchWhiteTaskCount" resultType="com.cairh.cpe.common.entity.AcBranchWhiteTaskCount">
        select branch_white_num,
               branch_handle_num,
               branch_all_num,
               branch_no,
               branch_name,
               nvl(decode(branch_handle_num, 0, 0, round(branch_response_time / branch_all_num)),
                   0) as branch_avg_response_time
        from (select count(distinct a.request_no)                                  as branch_white_num,
                     count(distinct case
                                        when a.task_status in ('3', '4', '8')
                                            then a.request_no end)                 as branch_handle_num,
                     nvl(sum(decode(a.task_status, '3', 1, '4', 1, '8', 1, 0)), 0) as branch_all_num,
                     a.branch_no,
                     a.branch_name,
                     round(nvl(sum(decode(a.task_status, '3', (a.deal_datetime - a.white_datetime), '4',
                                          (a.deal_datetime - a.white_datetime), '8',
                                          (a.deal_datetime - a.white_datetime),
                                          0)), 0) * 24 * 60 *
                           60)                                                     as branch_response_time
              from crh_ac.flowtaskrecorddetails a
              where a.white_flag = '1'
                and a.white_datetime >= trunc(sysdate)
                and a.white_datetime &lt; trunc(sysdate + 1)
                and a.invalid_flag = '0'
                and a.branch_no != ' '
              group by a.branch_no, a.branch_name)
    </select>

    <select id="getPendingTaskCount" resultType="java.lang.Integer">
        select count(*)
        from crh_ac.businflowtask b
        where b.task_status = '1'
          and b.push_flag in ('7', '8', '9')
    </select>

    <select id="getAvgResponseTime" resultType="java.lang.Integer">
        select nvl(decode(task_num, 0, 0, round(response_time / task_num)), 0) as avg_response_time
        from (select count(*) as task_num,
        round(sum(b.first_deal_datetime - b.request_datetime) * 24 * 60 * 60) as response_time
        from crh_ac.businprocessrequestaudittrail b
        where b.request_datetime >= trunc(sysdate)
        and b.request_datetime &lt; trunc(sysdate + 1)
        and b.user_up_branch_no != '3'
        and b.user_up_branch_no != ' '
        and b.pause_flag = '0'
        <if test="time_property != null and time_property!=''">
            and b.time_property = #{time_property}
        </if>
        and b.audit_finish_datetime is not null)
    </select>

    <select id="getWaitTaskTop30List" resultType="com.cairh.cpe.common.dto.AuditAlarmTaskDetail">
        select *
        from (select d.client_name,
                     d.id_no,
                     d.dist_datetime as request_datetime
              from crh_ads.dispatchtask d
              where d.dispatch_status in ('0', '1', '6')
              order by d.dist_datetime)
        where rownum &lt;= 30
    </select>

    <select id="getFailedAssignmentTaskTop30List" resultType="com.cairh.cpe.common.dto.AuditAlarmTaskDetail">
        select *
        from (select d.client_name,
                     d.id_no,
                     d.dispatch_fail_count as fail_assignment_num
              from crh_ads.dispatchtask d
              where d.dispatch_status in ('0', '6')
              order by d.dispatch_fail_count desc)
        where rownum &lt;= 30
    </select>

    <select id="getNewTaskTopList" resultType="com.cairh.cpe.common.dto.AuditAlarmTaskDetail">
        select *
        from (select b.user_name as client_name,
                     b.id_no,
                     b.request_datetime
              from crh_ac.businprocessrequestaudittrail b
              where b.request_datetime >= trunc(sysdate)
                and b.request_datetime &lt; trunc(sysdate + 1)
              order by b.request_datetime desc)
        where rownum &lt;= #{showNum}
    </select>

    <select id="getAuditOperatorIdleTop30List" resultType="com.cairh.cpe.common.dto.AuditAlarmOperatorDetail">
        select *
        from (
        select f.operator_no,
        max(f.operator_name) as operator_name,
        max(f.deal_datetime) as deal_datetime
        from crh_ac.flowtaskrecorddetails f
        where f.task_status in ('3', '4', '8')
        and f.deal_datetime >= trunc(sysdate)
        and f.deal_datetime &lt; trunc(sysdate + 1)
        and f.operator_no in
        <foreach item="item" collection="operatorNos" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by f.operator_no
        order by max(f.deal_datetime)
        )
        where rownum &lt;= 30
    </select>

    <select id="getQCOperatorIdleTop30List" resultType="com.cairh.cpe.common.dto.AuditAlarmOperatorDetail">
        select *
        from (
        select a.operator_no,
        max(a.operator_name) as operator_name,
        max(a.deal_datetime) as deal_datetime
        from (
        select q.actual_quality_operator_no as operator_no,
        max(q.actual_quality_operator_name) as operator_name,
        max(q.quality_operator_finish_datetime) as deal_datetime
        from crh_qc.qctask q
        where q.quality_operator_finish_datetime >= trunc(sysdate)
        and q.quality_operator_finish_datetime &lt; trunc(sysdate + 1)
        and q.actual_quality_operator_no in
        <foreach item="item" collection="operatorNos" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by q.actual_quality_operator_no
        union all
        select t.actual_quality_operator_no as operator_no,
        max(t.actual_quality_operator_name) as operator_name,
        max(t.review_quality_finish_datetime) as deal_datetime
        from crh_qc.qctask t
        where t.review_quality_finish_datetime >= trunc(sysdate)
        and t.review_quality_finish_datetime &lt; trunc(sysdate + 1)
        and t.actual_quality_operator_no in
        <foreach item="item" collection="operatorNos" separator="," open="(" close=")">
            #{item}
        </foreach>
        group by t.actual_quality_operator_no
        ) a
        group by a.operator_no
        order by max(a.deal_datetime)
        )
        where rownum &lt;= 30
    </select>

</mapper>
