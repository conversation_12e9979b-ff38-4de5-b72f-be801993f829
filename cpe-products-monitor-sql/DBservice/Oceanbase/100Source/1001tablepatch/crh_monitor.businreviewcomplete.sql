create table CRH_MONITOR.BUSINREVIEWCOMPLETE
(
    SERIAL_ID          VARCHAR2(32)  default ' '     not null,
    CURR_DATE          NUMBER(10)    default 0       not null,
    COMPLETE_NUM       NUMBER(10)    default 0       not null,
    BRANCH_NO          VARCHAR2(20)  default ' '     not null,
    BRANCH_NAME        VARCHAR2(255) default ' '     not null,
    UP_BRANCH_NO       VARCHAR2(20)  default ' '     not null,
    UP_BRANCH_NAME     VARCHAR2(255) default ' '     not null,
    BUSIN_TYPE         VARCHAR2(10)  default ' '     not null,
    CREATE_DATETIME    DATE          default sysdate not null,
    MONTH_COMPLETE_NUM NUMBER(10)    default 0       not null,
    YEAR_COMPLETE_NUM  NUMBER(10)    default 0       not null
);

CREATE UNIQUE INDEX CRH_MONITOR.IDX_BUSINREVIEWCOMPLETE ON CRH_MONITOR.BUSINREVIEWCOMPLETE (SERIAL_ID);
CREATE UNIQUE INDEX CRH_MONITOR.IDX_BRC_CDATE_BTYPE_BNO ON CRH_MONITOR.BUSINREVIEWCOMPLETE (CURR_DATE, <PERSON><PERSON><PERSON>_NO, BUSIN_TYPE);
CREATE INDEX CRH_MONITOR.IDX_BRC_CDATE_BTYPE ON CRH_MONITOR.BUSINREVIEWCOMPLETE (CURR_DATE, BUSIN_TYPE);
CREATE INDEX CRH_MONITOR.IDX_BRC_CDATE ON CRH_MONITOR.BUSINREVIEWCOMPLETE (CURR_DATE);
