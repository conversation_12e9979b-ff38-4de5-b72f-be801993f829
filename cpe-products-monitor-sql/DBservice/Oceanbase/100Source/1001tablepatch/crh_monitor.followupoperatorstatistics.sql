create table CRH_MONITOR.FOLLOWUPOPERATORSTATISTICS
(
    SERIAL_ID                 VARCHAR2(32) default ' '     not null,
    OPERATOR_NO               VARCHAR2(20) default ' '     not null,
    OPERATOR_NAME             VARCHAR2(64) default ' '     not null,
    EARLIEST_LOGIN_TIME       DATE,
    LATEST_LOGOUT_TIME        DATE,
    TOTAL_ONLINE_SECONDS      NUMBER       default 0       not null,
    FIRST_PROCESS_TIME        DATE,
    LAST_PROCESS_TIME         DATE,
    TOTAL_PROCESS_DUR_SECONDS NUMBER       default 0       not null,
    TOTAL_CALL_NUM            NUMBER       default 0       not null,
    STAT_DATE                 DATE,
    CREATE_DATETIME           DATE         default sysdate not null,
    UPDATE_DATETIME           DATE         default sysdate not null
);

CREATE UNIQUE INDEX CRH_MONITOR.PK_FU_OPERATOR_NO ON CRH_MONITOR.FOLLOWUPOPERATORSTATISTICS (SERIAL_ID);
CREATE INDEX CRH_MONITOR.IDX_FU_STAT_DATE ON CRH_MONITOR.FOLLOWUPOPERATORSTATISTICS (STAT_DATE);
CREATE INDEX CRH_MONITOR.IDX_FU_OPERATOR_NO ON CRH_MONITOR.FOLLOWUPOPERATORSTATISTICS (OPERATOR_NO);

COMMENT ON COLUMN CRH_MONITOR.FOLLOWUPOPERATORSTATISTICS.SERIAL_ID IS '主键';
COMMENT ON COLUMN CRH_MONITOR.FOLLOWUPOPERATORSTATISTICS.OPERATOR_NO IS '执行人工号';
COMMENT ON COLUMN CRH_MONITOR.FOLLOWUPOPERATORSTATISTICS.OPERATOR_NAME IS '执行人姓名';
COMMENT ON COLUMN CRH_MONITOR.FOLLOWUPOPERATORSTATISTICS.EARLIEST_LOGIN_TIME IS '当天最早上线时间';
COMMENT ON COLUMN CRH_MONITOR.FOLLOWUPOPERATORSTATISTICS.LATEST_LOGOUT_TIME IS '当天最晚下线时间';
COMMENT ON COLUMN CRH_MONITOR.FOLLOWUPOPERATORSTATISTICS.TOTAL_ONLINE_SECONDS IS '全天在线时长';
COMMENT ON COLUMN CRH_MONITOR.FOLLOWUPOPERATORSTATISTICS.FIRST_PROCESS_TIME IS '当天第一笔处理时间';
COMMENT ON COLUMN CRH_MONITOR.FOLLOWUPOPERATORSTATISTICS.LAST_PROCESS_TIME IS '当天最后一笔处理时间';
COMMENT ON COLUMN CRH_MONITOR.FOLLOWUPOPERATORSTATISTICS.TOTAL_PROCESS_DUR_SECONDS IS '全天处理总时长';
COMMENT ON COLUMN CRH_MONITOR.FOLLOWUPOPERATORSTATISTICS.TOTAL_CALL_NUM IS '拨打总次数';
COMMENT ON COLUMN CRH_MONITOR.FOLLOWUPOPERATORSTATISTICS.STAT_DATE IS '统计时间';
COMMENT ON COLUMN CRH_MONITOR.FOLLOWUPOPERATORSTATISTICS.CREATE_DATETIME IS '创建时间';
COMMENT ON COLUMN CRH_MONITOR.FOLLOWUPOPERATORSTATISTICS.UPDATE_DATETIME IS '更新时间';


