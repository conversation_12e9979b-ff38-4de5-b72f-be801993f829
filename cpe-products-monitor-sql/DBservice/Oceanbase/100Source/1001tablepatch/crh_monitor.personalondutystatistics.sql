create table CRH_MONITOR.PERSONALONDUTYSTATISTICS
(
    OPERATOR_NO       VARCHAR2(18)  default ' '     not null,
    OPERATOR_NAME     VARCHAR2(60)  default ' '     not null,
    BRANCH_NO         VARCHAR2(20)  default ' '     not null,
    BRANCH_NAME       VARCHAR2(255) default ' '     not null,
    REPORT_DATE       DATE          default sysdate not null,
    BEGIN_DEAL_TIME   DATE,
    END_DEAL_TIME     DATE,
    SUM_DEAL_TIME     NUMBER(10)    default 0       not null,
    BEGIN_ONLINE_TIME DATE,
    END_ONLINE_TIME   DATE,
    ONLINE_TIME_SUM   NUMBER(10)    default 0       not null,
    PAUSE_SUM         NUMBER(10)    default 0       not null,
    RECYCLE_SUM       NUMBER(10)    default 0       not null,
    DELIVER_SUM       NUMBER(10)    default 0       not null,
    CREATE_DATETIME   DATE          default sysdate not null,
    UP_BRANCH_NAME    VARCHAR2(255) default ' '     not null,
    UP_BRANCH_NO      VARCHAR2(20)  default ' '     not null
);

CREATE UNIQUE INDEX CRH_MONITOR.IDX_PERSONALONDUTYSTATISTICS ON CRH_MONITOR.PERSONALONDUTYSTATISTICS (OPERATOR_NO, REPORT_DATE);
CREATE INDEX CRH_MONITOR.IDX_PODSTATISTICS_OPERATORNO ON CRH_MONITOR.PERSONALONDUTYSTATISTICS (OPERATOR_NO);
CREATE INDEX CRH_MONITOR.IDX_PODSTATISTICS_BRANCHNO ON CRH_MONITOR.PERSONALONDUTYSTATISTICS (BRANCH_NO);
CREATE INDEX CRH_MONITOR.IDX_PODSTATISTICS_REPORTDATE ON CRH_MONITOR.PERSONALONDUTYSTATISTICS (REPORT_DATE);

COMMENT ON COLUMN CRH_MONITOR.PERSONALONDUTYSTATISTICS.OPERATOR_NO IS '操作员编号';
COMMENT ON COLUMN CRH_MONITOR.PERSONALONDUTYSTATISTICS.OPERATOR_NAME IS '操作员姓名';
COMMENT ON COLUMN CRH_MONITOR.PERSONALONDUTYSTATISTICS.BRANCH_NO IS '用户营业部编号';
COMMENT ON COLUMN CRH_MONITOR.PERSONALONDUTYSTATISTICS.BRANCH_NAME IS '用户所属分公司';
COMMENT ON COLUMN CRH_MONITOR.PERSONALONDUTYSTATISTICS.REPORT_DATE IS '统计日期';
COMMENT ON COLUMN CRH_MONITOR.PERSONALONDUTYSTATISTICS.BEGIN_DEAL_TIME IS '当天第一笔处理时间';
COMMENT ON COLUMN CRH_MONITOR.PERSONALONDUTYSTATISTICS.END_DEAL_TIME IS '当天最后一笔处理时间';
COMMENT ON COLUMN CRH_MONITOR.PERSONALONDUTYSTATISTICS.SUM_DEAL_TIME IS '全天审核总时长，单位：s';
COMMENT ON COLUMN CRH_MONITOR.PERSONALONDUTYSTATISTICS.BEGIN_ONLINE_TIME IS '当天最早上线时间';
COMMENT ON COLUMN CRH_MONITOR.PERSONALONDUTYSTATISTICS.END_ONLINE_TIME IS '当天最晚下线时间';
COMMENT ON COLUMN CRH_MONITOR.PERSONALONDUTYSTATISTICS.ONLINE_TIME_SUM IS '全天在线时长，单位：s';
COMMENT ON COLUMN CRH_MONITOR.PERSONALONDUTYSTATISTICS.PAUSE_SUM IS '挂起数量';
COMMENT ON COLUMN CRH_MONITOR.PERSONALONDUTYSTATISTICS.RECYCLE_SUM IS '回收数量';
COMMENT ON COLUMN CRH_MONITOR.PERSONALONDUTYSTATISTICS.DELIVER_SUM IS '转交数量';
COMMENT ON COLUMN CRH_MONITOR.PERSONALONDUTYSTATISTICS.CREATE_DATETIME IS '创建时间';
COMMENT ON COLUMN CRH_MONITOR.PERSONALONDUTYSTATISTICS.UP_BRANCH_NAME IS '上级用户所属分公司';
COMMENT ON COLUMN CRH_MONITOR.PERSONALONDUTYSTATISTICS.UP_BRANCH_NO IS '上级用户营业部编号';


