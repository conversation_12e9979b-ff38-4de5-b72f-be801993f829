insert into crh_user."CRHRESOURCE"("RESOURCE_ID", "RESOURCE_TYPE", "PARENT_RESOURCE_ID", "RESOURCE_NAME", "RESOURCE_URL", "IS_CONTROL", "CREATE_DATETIME")
values('28150101', '1', '281501', '见证业务数据总览', ' ', '1', CURRENT_TIMESTAMP);
insert into crh_user."CRHRESOURCE"("RESOURCE_ID", "RESOURCE_TYPE", "PARENT_RESOURCE_ID", "RESOURCE_NAME", "RESOURCE_URL", "IS_CONTROL", "CREATE_DATETIME")
values('28150102', '1', '281501', '见证智能调度总览', ' ', '1', CURRENT_TIMESTAMP);
insert into crh_user."CRHRESOURCE"("RESOURCE_ID", "RESOURCE_TYPE", "PARENT_RESOURCE_ID", "RESOURCE_NAME", "RESOURCE_URL", "IS_CONTROL", "CREATE_DATETIME")
values('28150103', '1', '281501', '见证申请实况', ' ', '1', CURRENT_TIMESTAMP);
insert into crh_user."CRHRESOURCE"("RESOURCE_ID", "RESOURCE_TYPE", "PARENT_RESOURCE_ID", "RESOURCE_NAME", "RESOURCE_URL", "IS_CONTROL", "CREATE_DATETIME")
values('28150104', '1', '281501', '见证人在线概况', ' ', '1', CURRENT_TIMESTAMP);
insert into crh_user."CRHRESOURCE"("RESOURCE_ID", "RESOURCE_TYPE", "PARENT_RESOURCE_ID", "RESOURCE_NAME", "RESOURCE_URL", "IS_CONTROL", "CREATE_DATETIME")
values('28150105', '1', '281501', '见证人承压概况', ' ', '1', CURRENT_TIMESTAMP);
insert into crh_user."CRHRESOURCE"("RESOURCE_ID", "RESOURCE_TYPE", "PARENT_RESOURCE_ID", "RESOURCE_NAME", "RESOURCE_URL", "IS_CONTROL", "CREATE_DATETIME")
values('28150106', '1', '281501', '见证人产能分布', ' ', '1', CURRENT_TIMESTAMP);
insert into crh_user."CRHRESOURCE"("RESOURCE_ID", "RESOURCE_TYPE", "PARENT_RESOURCE_ID", "RESOURCE_NAME", "RESOURCE_URL", "IS_CONTROL", "CREATE_DATETIME")
values('28150107', '1', '281501', '绿通客户服务概况', ' ', '1', CURRENT_TIMESTAMP);
insert into crh_user."CRHRESOURCE"("RESOURCE_ID", "RESOURCE_TYPE", "PARENT_RESOURCE_ID", "RESOURCE_NAME", "RESOURCE_URL", "IS_CONTROL", "CREATE_DATETIME")
values('28150108', '1', '281501', '异常指标警示', ' ', '1', CURRENT_TIMESTAMP);