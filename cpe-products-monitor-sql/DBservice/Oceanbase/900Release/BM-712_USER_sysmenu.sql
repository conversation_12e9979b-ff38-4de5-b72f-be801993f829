--见证人岗位报表菜单
declare
    v_count integer;
begin
    select count(1) into v_count from crh_user.sysmenu where crhresource_id='281609';
    if v_count = 0 then
        insert into crh_user.sysmenu(
            crhresource_id,
            menu_site,
            menu_caption,
            menu_name,
            menu_class_style,
            action_url,
            menu_hint,
            subsys_no,
            show_flag,
            leaf_flag,
            role_rights,
            use_new_sysmenu,
            new_action_url
        )
        values (
                   '281609',
                   'OGJ',
                   '见证人岗位报表',
                   '见证人岗位报表',
                   ' ',
                   'backend20/bm/#/account/position-statistics',
                   '  ',
                   '28',
                   '1',
                   '3',
                   ' ',
                   '0',
                   ' '
               );
    end if;
    commit;
end;
/

declare
    v_count integer;
begin
    select count(1) into v_count from crh_user.crhresource where resource_id='281609';
    if v_count = 0 then
        insert into crh_user.crhresource(
            resource_id,
            resource_type,
            parent_resource_id,
            resource_name,
            resource_url,
            is_control,
            create_datetime
        )
        values (
                   '281609',
                   '0',
                   ' ',
                   '见证人岗位报表',
                   ' ',
                   '1',
                   CURRENT_TIMESTAMP
               );
    end if;
    commit;
end;
/