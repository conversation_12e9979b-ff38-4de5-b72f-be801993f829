declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.sysparamconfig where config_id='monitor_alarm';
if v_count = 0 then
			insert into crh_monitor.sysparamconfig(
				config_id,
				label_sys,
				description,
				order_no,
				parent_config_id,
				show_flag
			)
			values (
				'monitor_alarm',
				'见证告警配置',
				' ',
				2,
				'monitor',
				'1'
			);
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.alarm.task.work.pending.num';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_alarm',
				'工作时段（交易日）待处理任务（不含挂起）',
				'笔',
				1,
				'1',
				'monitor.alarm.task.work.pending.num',
				'100',
				' ',
				' '
			);
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.alarm.task.non-work.pending.num';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_alarm',
				'非工作时段待处理任务（不含挂起）',
				'笔',
				2,
				'1',
				'monitor.alarm.task.non-work.pending.num',
				'100',
				' ',
				' '
			);
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.alarm.task.avg.response.time';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_alarm',
				'工作时段平均响应时长',
				'秒',
				3,
				'1',
				'monitor.alarm.task.avg.response.time',
				'10',
				' ',
				' '
			);
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.alarm.task.wait.time';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_alarm',
				'工作时段单笔任务等待时长',
				'分钟',
				4,
				'1',
				'monitor.alarm.task.wait.time',
				'10',
				' ',
				' '
			);
end if;
commit;
end;
/


declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.alarm.task.assignment.fail.num';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_alarm',
				'工作时段单笔任务分配失败次数',
				'次',
				5,
				'1',
				'monitor.alarm.task.assignment.fail.num',
				'10',
				' ',
				' '
			);
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.alarm.task.new.time';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_alarm',
				'系统工作时段未进任务时长',
				'分钟',
				6,
				'1',
				'monitor.alarm.task.new.time',
				'10',
				' ',
				' '
			);
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.alarm.operator.idle.time';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_alarm',
				'单坐席未有操作记录时长',
				'分钟',
				7,
				'1',
				'monitor.alarm.operator.idle.time',
				'10',
				' ',
				' '
			);
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.alarm.sms.notice.switch';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_alarm',
				'监控告警短信通知开关',
				' ',
				8,
				'1',
				'monitor.alarm.sms.notice.switch',
				'0',
				'1',
				'[{\"label\":\"是\",\"value\":\"1\"},{\"label\":\"否\",\"value\":\"0\"}]'
			);
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.alarm.sms.notice.mobile';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_alarm',
				'监控告警短信通知手机号',
				' ',
				9,
				'1',
				'monitor.alarm.sms.notice.mobile',
				'1363644574',
				' ',
				' '
			);
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.alarm.sms.notice.interval.time';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_alarm',
				'监控告警短信通知间隔时间',
				'分钟',
				10,
				'1',
				'monitor.alarm.sms.interval.time',
				'60',
				' ',
				' '
			);
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_user.basedictionary where dict_code='time_kind' and sub_code='j';
if v_count = 0 then
        insert into crh_user.basedictionary(
            subsys_no,
            dict_code,
            dict_name,
            sub_code,
            sub_name,
            order_no,
            remark,
            status,
            create_by,
            modify_by
        )
        values (
                   '24',
                   'time_kind',
                   '时间类型',
                   'j',
                   '见证告警时间',
                   24,
                   ' ',
                   '8',
                   ' ',
                   ' '
               );
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_user.sysworktime where time_kind='j';
if v_count = 0 then
        insert into crh_user.sysworktime(
            serial_id,
            time_name,
            status,
            work_time,
            is_work_day,
            create_by,
            create_datetime,
            modify_by,
            modify_datetime,
            time_kind
        )
        values (
                   '202507031439130010000',
                   '见证告警时间',
                   '8',
                   '00-00 00-00 08-22 * * * *;01-59 00-59 22-23 * * * *',
                   '1',
                   ' ',
                   null,
                   ' ',
                   null,
                   'j'
               );
end if;
commit;
end;
/
