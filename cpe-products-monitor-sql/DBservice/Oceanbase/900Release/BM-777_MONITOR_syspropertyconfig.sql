declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.alarm.non-work.task.avg.response.time';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_alarm',
				'非工作时段平均响应时长',
				'秒',
				3,
				'1',
				'monitor.alarm.non-work.task.avg.response.time',
				'10',
				' ',
				' '
			);
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.alarm.non-work.task.wait.time';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_alarm',
				'非工作时段单笔任务等待时长',
				'分钟',
				4,
				'1',
				'monitor.alarm.non-work.task.wait.time',
				'10',
				' ',
				' '
			);
end if;
commit;
end;
/


declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.alarm.non-work.task.assignment.fail.num';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_alarm',
				'非工作时段单笔任务分配失败次数',
				'次',
				5,
				'1',
				'monitor.alarm.non-work.task.assignment.fail.num',
				'10',
				' ',
				' '
			);
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.alarm.non-work.task.new.time';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_alarm',
				'系统非工作时段未进任务时长',
				'分钟',
				6,
				'1',
				'monitor.alarm.non-work.task.new.time',
				'10',
				' ',
				' '
			);
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.alarm.non-work.operator.idle.time';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_alarm',
				'非工作时段单坐席未有操作记录时长',
				'分钟',
				7,
				'1',
				'monitor.alarm.non-work.operator.idle.time',
				'10',
				' ',
				' '
			);
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.alarm.pendingTask.sms.template';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_alarm',
				'待处理任务量告警短信模板',
				' ',
				11,
				'1',
				'monitor.alarm.pendingTask.sms.template',
				'见证系统待处理任务超%s笔，请尽快处理！',
				' ',
				' '
			);
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.alarm.avgResponse.sms.template';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_alarm',
				'平均响应时长告警短信模板',
				' ',
				12,
				'1',
				'monitor.alarm.avgResponse.sms.template',
				'见证响应时长已超%s秒，请及时关注！',
				' ',
				' '
			);
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.alarm.newTask.sms.template';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_alarm',
				'新进任务告警短信模板',
				' ',
				13,
				'1',
				'monitor.alarm.newTask.sms.template',
				'见证系统已超%s分钟未有新进任务，请及时关注！',
				' ',
				' '
			);
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.alarm.waitTask.sms.template';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_alarm',
				'单笔任务等待时长告警短信模板',
				' ',
				14,
				'1',
				'monitor.alarm.waitTask.sms.template',
				'单笔任务待处理时长已超%s分钟，请及时关注！',
				' ',
				' '
			);
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.alarm.failedTask.sms.template';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_alarm',
				'单笔任务分配失败告警短信模板',
				' ',
				15,
				'1',
				'monitor.alarm.failedTask.sms.template',
				'单笔任务分配失败次数已超%s次，请及时关注！',
				' ',
				' '
			);
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.alarm.operator.sms.template';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_alarm',
				'操作频次告警短信模板',
				' ',
				16,
				'1',
				'monitor.alarm.operator.sms.template',
				'在线见证人已超%s分钟未审核，请及时关注！',
				' ',
				' '
			);
end if;
commit;
end;
/

declare
begin
update crh_monitor.syspropertyconfig set label_sys='工作时段单坐席未有操作记录时长' where property_key = 'monitor.alarm.operator.idle.time';
commit;
end;
/

declare
begin
update crh_monitor.syspropertyconfig set label_sys='工作时段待处理任务', description='笔，注：不含挂起' where property_key = 'monitor.alarm.task.work.pending.num';
commit;
end;
/

declare
begin
update crh_monitor.syspropertyconfig set label_sys='非工作时段待处理任务', description='笔，注：不含挂起' where property_key = 'monitor.alarm.task.non-work.pending.num';
commit;
end;
/

