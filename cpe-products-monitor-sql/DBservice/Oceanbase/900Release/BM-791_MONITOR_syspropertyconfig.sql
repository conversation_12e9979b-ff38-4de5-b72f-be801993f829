declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.alarm.task.new.show.num';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_alarm',
				'新进任务展示数量',
				'笔',
				13,
				'1',
				'monitor.alarm.task.new.show.num',
				'5',
				' ',
				' '
			);
end if;
commit;
end;
/


update crh_monitor.syspropertyconfig set order_no = 4 where property_key = 'monitor.alarm.non-work.task.avg.response.time';
update crh_monitor.syspropertyconfig set order_no = 5 where property_key = 'monitor.alarm.task.wait.time';
update crh_monitor.syspropertyconfig set order_no = 6 where property_key = 'monitor.alarm.non-work.task.wait.time';
update crh_monitor.syspropertyconfig set order_no = 7 where property_key = 'monitor.alarm.task.assignment.fail.num';
update crh_monitor.syspropertyconfig set order_no = 8 where property_key = 'monitor.alarm.non-work.task.assignment.fail.num';
update crh_monitor.syspropertyconfig set order_no = 9 where property_key = 'monitor.alarm.task.new.time';
update crh_monitor.syspropertyconfig set order_no = 10 where property_key = 'monitor.alarm.non-work.task.new.time';
update crh_monitor.syspropertyconfig set order_no = 11 where property_key = 'monitor.alarm.operator.idle.time';
update crh_monitor.syspropertyconfig set order_no = 12 where property_key = 'monitor.alarm.non-work.operator.idle.time';
update crh_monitor.syspropertyconfig set order_no = 14 where property_key = 'monitor.alarm.sms.notice.switch';
update crh_monitor.syspropertyconfig set order_no = 15 where property_key = 'monitor.alarm.sms.notice.mobile';
update crh_monitor.syspropertyconfig set order_no = 16 where property_key = 'monitor.alarm.sms.interval.time';
update crh_monitor.syspropertyconfig set order_no = 17 where property_key = 'monitor.alarm.pendingTask.sms.template';
update crh_monitor.syspropertyconfig set order_no = 18 where property_key = 'monitor.alarm.avgResponse.sms.template';
update crh_monitor.syspropertyconfig set order_no = 19 where property_key = 'monitor.alarm.newTask.sms.template';
update crh_monitor.syspropertyconfig set order_no = 20 where property_key = 'monitor.alarm.waitTask.sms.template';
update crh_monitor.syspropertyconfig set order_no = 21 where property_key = 'monitor.alarm.failedTask.sms.template';
update crh_monitor.syspropertyconfig set order_no = 22 where property_key = 'monitor.alarm.operator.sms.template';
