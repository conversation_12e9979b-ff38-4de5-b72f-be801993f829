declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.sysparamconfig where config_id='monitor_screen';
if v_count = 0 then
			insert into crh_monitor.sysparamconfig(
				config_id,
				label_sys,
				description,
				order_no,
				parent_config_id,
				show_flag
			)
			values (
				'monitor_screen',
				'大屏配置',
				' ',
				3,
				'monitor',
				'1'
			);
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.screen.general.metric.refresh.time';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_screen',
				'常规指标刷新频率',
				'秒',
				1,
				'1',
				'monitor.screen.general.metric.refresh.time',
				'3',
				' ',
				' '
			);
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.screen.yoy.metric.refresh.time';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_screen',
				'同比指标刷新频率',
				'秒',
				2,
				'1',
				'monitor.screen.yoy.metric.refresh.time',
				'3',
				' ',
				' '
			);
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.screen.mom.metric.refresh.time';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_screen',
				'环比指标刷新频率',
				'秒',
				3,
				'1',
				'monitor.screen.mom.metric.refresh.time',
				'3',
				' ',
				' '
			);
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.screen.tab.switch.time';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_screen',
				'tab页切换频率',
				'秒',
				4,
				'1',
				'monitor.screen.tab.switch.time',
				'13',
				' ',
				' '
			);
end if;
commit;
end;
/
