declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.alarm.task.autoreject.num';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_alarm',
				'工作时段自动驳回任务',
				'笔',
				12,
				'1',
				'monitor.alarm.task.autoreject.num',
				'10',
				' ',
				' '
			);
end if;
commit;
end;
/

declare
v_count integer;
begin
select count(1) into v_count from crh_monitor.syspropertyconfig where property_key='monitor.alarm.non-work.task.autoreject.num';
if v_count = 0 then
			insert into crh_monitor.syspropertyconfig(
				config_id,
				label_sys,
				description,
				order_no,
				show_flag,
				property_key,
				property_value,
				validator_type,
				validator_content
			)
			values (
				'monitor_alarm',
				'非工作时段自动驳回任务',
				'笔',
				12,
				'1',
				'monitor.alarm.non-work.task.autoreject.num',
				'10',
				' ',
				' '
			);
end if;
commit;
end;
/