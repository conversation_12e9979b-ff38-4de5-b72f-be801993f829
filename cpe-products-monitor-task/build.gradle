plugins {
    id 'war'
    id 'org.springframework.boot'
}

version ''

archivesBaseName = 'cpe-products-monitor-task'

dependencies {
    api(project(":cpe-products-monitor-common"))
    api('com.cairh:cpe-esb-basedata-server-api')
    api('com.cairh:cpe-esb-component-server-api')
    api("com.cairh:cpe-trace")
    api('com.cairh:cpe-db')
    api('com.cairh:cpe-mem')
    api('org.springframework.boot:spring-boot-starter-web')
    api('org.springframework.boot:spring-boot-starter-actuator')
    api('org.apache.dubbo:dubbo')
    api('org.apache.dubbo:dubbo-spring-boot-starter')
    api('org.springframework.boot:spring-boot-starter-data-redis')
    api('com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery')
    api('com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config')
    api('org.springframework.cloud:spring-cloud-starter-consul-discovery')
    api('org.springframework.cloud:spring-cloud-starter-consul-config')
    api('com.ctrip.framework.apollo:apollo-client')
    api('mysql:mysql-connector-java')
    api('com.oracle:ojdbc6')
    api('cn.com.kingbase:kingbase8:8.6.0')
    api('com.oceanbase:oceanbase-client:2.4.0')
    api('com.alibaba.spring:spring-context-support')
    api('com.cairh:cpe-job-core')

    //tongweb
    api('com.gtja:tongweb-embed')
    api('com.gtja:tongweb-spring-boot-starter')

    implementation 'org.redisson:redisson-spring-boot-starter:3.17.1'
}
