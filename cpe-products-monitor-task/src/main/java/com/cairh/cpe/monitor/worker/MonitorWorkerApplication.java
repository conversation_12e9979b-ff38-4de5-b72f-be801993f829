package com.cairh.cpe.monitor.worker;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.gson.GsonAutoConfiguration;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

@EnableCaching
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@MapperScan("com.cairh.cpe.**.mapper")
@SpringBootApplication(scanBasePackages = "com.cairh.cpe",exclude = {GsonAutoConfiguration.class})
public class MonitorWorkerApplication {

    public static void main(String[] args) {
        SpringApplication.run(MonitorWorkerApplication.class, args);
    }
}
