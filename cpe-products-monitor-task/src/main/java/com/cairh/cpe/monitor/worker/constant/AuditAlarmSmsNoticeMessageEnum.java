package com.cairh.cpe.monitor.worker.constant;

/**
 * 见证告警短信通知消息
 */
public enum AuditAlarmSmsNoticeMessageEnum {

    PENDINGTASK("pendingTask", "见证系统待处理任务超%s笔，请尽快处理！"),
    AVGRESPONSE("avgResponse", "见证工作时段响应时长已超%s秒，请及时关注！"),
    NEWTASK("newTask", "见证系统已超%s分钟未有新进任务，请及时关注！"),
    WAITTASK("waitTask", "单笔任务待处理时长已超%s分钟，请及时关注！"),
    FAILEDTASK("failedTask", "单笔任务分配失败次数已超%s次，请及时关注！"),
    OPERATORIDLE("operatorIdle", "在线见证人已超%s分钟未审核，请及时关注！"),
    AUTOREJECTTASK("autoRejectTask", "见证系统自动驳回任务超%s笔，请及时关注！"),
    ;

    String code;
    String value;

    AuditAlarmSmsNoticeMessageEnum(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public static String getValue(String code) {
        for (AuditAlarmSmsNoticeMessageEnum h : AuditAlarmSmsNoticeMessageEnum.values()) {
            if (h.getCode().equals(code)) {
                return h.value;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
