package com.cairh.cpe.monitor.worker.constant;

/**
 * 常量
 *
 * <AUTHOR>
 */
public class Constant {

    public static final String SUCCESS = "成功";

    //通用删除状态
    public static final String COMMON_DELETE_STATUS = "9";
    public static final String COMMON_VALID_STATUS = "8";

    public static final String[] CHAR_SET = {"@", "&"};

    public static final String EMPTY_STRING = "";
    public static final String BLANK_SPACE_STRING = " ";
    public static final String ZERO_STR = "0";
    public static final String ONE_STR = "1";
    public static final String THREE_STR = "3";
    public static final String FIVE_STR = "5";
    public static final String NINE_STR = "9";
    public static final String TEN_STR = "10";
    public static final String NINETEEN_STR = "19";
    public static final String ONE_HUNDRED_STR = "100";

    public static final String DEFAULT = "default";
    public static final String SOURCE_INFO_SYS = "sys";

    public static final String COMMA = ",";
    public static final String COLON = ":";
    public static final String UNDERLINE = "_";

    public static final int ZERO = 0;
    public static final int NEGATIVE_ONE = -1;
    public static final int ONE = 1;
    public static final int TWO = 2;
    public static final int THREE = 3;
    public static final int FOUR = 4;
    public static final int FIVE = 5;
    public static final int SIX = 6;
    public static final int EIGHT = 8;
    public static final int NINE = 9;
    public static final int TEN = 10;
    public static final int TWELVE = 12;
    public static final int FOURTEEN = 14;
    public static final int FIFTEEN = 15;
    public static final int SIXTEEN = 16;
    public static final int EIGHTEEN = 18;
    public static final int SIXTY = 60;
    public static final int ONE_HUNDRED = 100;
    public static final int THREE_HUNDRED = 300;
    public static final int ONE_THOUSAND = 1000;
    public static final int THREE_THOUSAND = 3000;
    public static final int FOUR_THOUSAND = 4000;
    public static final int TEN_THOUSAND = 10000;

    /**
     * 见证系统报表统计单位时间
     */
    public static final String MONITOR_AC_STATISTICS_UNIT_TIME = "monitor.ac.statistics.unit.time";
    // 审核
    public final static String AUDIT = "audit";
    // 复核
    public final static String REVIEW = "review";
    // 二次复核
    public final static String SECONDARY_REVIEW = "secondary_review";

    // 登出
    public static final String LOGOUT_SYS_JOUR_CODE = "O";
    // 登录
    public static final String LOGIN_SYS_JOUR_CODE = "S";

    // 签入
    public static final String ONLINE_RECORD_LOGIN = "1";
    // 签出
    public static final String ONLINE_RECORD_LOGOUT = "2";

    // 总部
    public static final String LEVEL_HEADQUARTERS = "0";
    // 子公司
    public static final String LEVEL_SUBSIDIARY_COMPANY = "1";
    // 分公司
    public static final String LEVEL_BRANCH_OFFICE = "2";
    /**
     * 营运中心
     */
    public static final String SPECIAL_BRANCH = "3";

    /**
     * 总部
     */
    public static final String DEFAULT_KH_BRANCH_CODE = "18";

    /**
     * 用户状态-冻结
     */
    public static final String OPERATOR_STATUS_7 = "7";

    /**
     * 用户状态-正常
     */
    public static final String OPERATOR_STATUS_8 = "8";

    /**
     * 用户状态-注销
     */
    public static final String OPERATOR_STATUS_9 = "9";

}
