package com.cairh.cpe.monitor.worker.data.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 营业部信息
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class BranchInfo {

    // 营业部编号
    private String branch_no;

    // 营业部名称
    private String branch_name;

    // 上级营业部编号
    private String up_branch_no;

    // 营业部类型
    private String branch_type;

}
