package com.cairh.cpe.monitor.worker.data.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 业务流程任务表
 * </p>
 *
 * <AUTHOR>
 * @since
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class BusinFlowTask implements Serializable {

    /**
     * 受理编号
     */
    private String request_no;

    /**
     * 流水号
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 任务状态
     */
    private String task_status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date create_datetime;

    /**
     * 处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date deal_datetime;

    /**
     * 完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date finish_datetime;

    /**
     * 任务类型
     * <p>
     *  审核 audit 复核 review
     */
    private String task_type;

    /**
     * 操作员编号
     */
    private String operator_no;

    /**
     * 操作员姓名
     */
    private String operator_name;

    /**
     * 操作处理内容
     */
    private String op_content;

    // reqeust表中的branch_no
    private String branch_no;
}
