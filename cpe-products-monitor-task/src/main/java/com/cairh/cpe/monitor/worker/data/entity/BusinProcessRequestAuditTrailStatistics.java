package com.cairh.cpe.monitor.worker.data.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 业务流程请求审核跟踪表-统计数据
 *
 * <AUTHOR>
 * @since 2025/2/17 10:13
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class BusinProcessRequestAuditTrailStatistics {

    /**
     * 营业部统计数量
     */
    private int branch_num;

    /**
     * 用户营业部编号
     */
    private String user_branch_no;

    /**
     * 用户所属分公司
     */
    private String user_branch_name;

    /**
     * 上级营业部编号
     */
    private String user_up_branch_no;

    /**
     * 上级营业部的名称
     */
    private String user_up_branch_name;

    /**
     * 业务类型
     */
    private String busin_type;

}
