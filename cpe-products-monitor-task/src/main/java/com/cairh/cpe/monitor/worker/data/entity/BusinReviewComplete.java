package com.cairh.cpe.monitor.worker.data.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 审核通过业务量报表
 *
 * <AUTHOR>
 * @since 2025/2/11 11:13
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("BusinReviewComplete")
public class BusinReviewComplete {

    /**
     * 主键
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 业务日期
     */
    @TableField("curr_date")
    private Integer curr_date;

    /**
     * 审核通过业务量-当日
     */
    @TableField("complete_num")
    private Integer complete_num;

    /**
     * 审核通过业务量-当月
     */
    @TableField("month_complete_num")
    private Integer month_complete_num;

    /**
     * 审核通过业务量-当年
     */
    @TableField("year_complete_num")
    private Integer year_complete_num;

    /**
     * 分支机构代码
     */
    @TableField("branch_no")
    private String branch_no;

    /**
     * 分支机构名称
     */
    @TableField("branch_name")
    private String branch_name;

    /**
     * 上级分支机构代码
     */
    @TableField("up_branch_no")
    private String up_branch_no;

    /**
     * 上级分支机构名称
     */
    @TableField("up_branch_name")
    private String up_branch_name;

    /**
     * 业务类型
     */
    @TableField("busin_type")
    private String busin_type;

    /**
     * 创建时间
     */
    @TableField("create_datetime")
    private Date create_datetime;


}
