package com.cairh.cpe.monitor.worker.data.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 分公司在岗坐席统计表
 *
 * <AUTHOR>
 * @since 2025/6/17 14:31
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("CompanyOperatorStatistics")
public class CompanyOperatorStatistics {

    /**
     * 分公司编号
     */
    @TableField("branch_no")
    private String branch_no;

    /**
     * 分公司名称
     */
    @TableField("branch_name")
    private String branch_name;

    /**
     * 坐席数量-冻结
     */
    @TableField("operator_frozen_num")
    private Integer operator_frozen_num;

    /**
     * 坐席数量-正常
     */
    @TableField("operator_normal_num")
    private Integer operator_normal_num;

    /**
     * 坐席数量-注销
     */
    @TableField("operator_inactive_num")
    private Integer operator_inactive_num;

    /**
     * 见证坐席数量
     */
    @TableField("audit_operator_num")
    private Integer audit_operator_num;

    /**
     * 创建时间
     */
    @TableField("create_datetime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date create_datetime;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String create_by;
}
