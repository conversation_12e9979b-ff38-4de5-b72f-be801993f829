package com.cairh.cpe.monitor.worker.data.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> 回访预约表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName(value = "FOLLOWUPONLINERECORD", schema = "CRH_FUS")
public class FollowupOnlineRecord implements Serializable {

    /**
     * 主键ID
     */
    @TableId("serial_id")
    private String serial_id;



    /**
     * 坐席编号
     */
    @TableField("operator_no")
    private String operator_no;

    /**
     * 坐席名称
     */
    @TableField("operator_name")
    private String operator_name;


    /**
     * 状态 1-签入 2-签出 3-置忙
     */
    @TableField("status")
    private String status;

    /**
     * 创建时间
     */
    @TableField("create_datetime")
    private Date create_datetime;

}
