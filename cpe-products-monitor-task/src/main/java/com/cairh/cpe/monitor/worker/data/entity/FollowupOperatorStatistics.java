package com.cairh.cpe.monitor.worker.data.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 坐席在岗统计;
 */
@Data
@TableName(value = "FOLLOWUPOPERATORSTATISTICS")
public class FollowupOperatorStatistics {
    /**
     * 主键
     */
    @TableId("serial_id")
    private String serial_id;

    /**
     * 执行人工号
     */
    @TableField(value = "operator_no")
    private String operator_no;

    /**
     * 执行人姓名
     */
    @TableField(value = "operator_name")
    private String operator_name;

    /**
     * TODO 当天最早上线时间（格式：HH24:MI）
     */
    @TableField(value = "earliest_login_time")
    private Date earliest_login_time;

    /**
     * TODO 当天最晚下线时间（格式：HH24:MI）
     */
    @TableField(value = "latest_logout_time")
    private Date latest_logout_time;

    /**
     * TODO 全天在线时长（秒钟）
     */
    @TableField(value = "total_online_seconds")
    private Integer total_online_seconds;

    /**
     * 当天第一笔处理时间
     */
    @TableField(value = "first_process_time")
    private Date first_process_time;

    /**
     * 当天最后一笔处理时间
     */
    @TableField(value = "last_process_time")
    private Date last_process_time;

    /**
     * 全天处理总时长（秒钟）
     */
    @TableField(value = "total_process_dur_seconds")
    private Integer total_process_dur_seconds;

    /**
     * 拨打总次数
     */
    @TableField(value = "total_call_num")
    private Integer total_call_num;

    /**
     * 统计时间
     */
    @TableField(value = "stat_date")
    private Date stat_date;

    /**
     * 创建时间
     */
    @TableField(value = "create_datetime")
    private Date create_datetime;

    /**
     * 更新时间
     */
    @TableField(value = "update_datetime")
    private Date update_datetime;
}
