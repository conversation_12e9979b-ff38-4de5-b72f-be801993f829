package com.cairh.cpe.monitor.worker.data.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * Description：用户信息
 * Author： slx
 * Date： 2024/4/24 下午3:59
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class OperatorInfo {

    /**
     * 用户编号
     */
    private String staff_no;

    /**
     * 用户所属营业部编号
     */
    private String branch_no;

    /**
     * 用户角色
     */
    private String en_roles;

    /**
     * 用户状态
     * 7-冻结 8-正常 9-注销
     */
    private String status;

}
