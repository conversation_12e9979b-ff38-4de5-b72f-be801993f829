package com.cairh.cpe.monitor.worker.data.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 见证系统操作员每日在线时长实体类
 *
 * <AUTHOR>
 * @since 2024/1/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("operatoronlinetime")
public class OperatorOnlineTime extends Model<OperatorOnlineTime> {

    private static final long serialVersionUID = 0xe7d9483e3ff3b2dfL;

    /**
     * 操作员编号
     */
    @TableField("operator_no")
    private String operator_no;

    /**
     * 报表统计日期
     */
    @TableField("report_date")
    private Date report_date;

    /**
     * 当天最早上线时间
     */
    @TableField("begin_online_time")
    private Date begin_online_time;

    /**
     * 当天最晚下线时间
     */
    @TableField("end_online_time")
    private Date end_online_time;

    /**
     * 当日在线时长
     */
    @TableField("online_time_sum")
    private Float online_time_sum;
}