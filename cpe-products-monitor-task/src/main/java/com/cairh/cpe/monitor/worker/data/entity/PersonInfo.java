package com.cairh.cpe.monitor.worker.data.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * Description：用户信息
 * Author： slx
 * Date： 2024/4/18 下午5:45
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class PersonInfo {

    /**
     * 操作员编号
     */
    private String operator_no;

    /**
     * 操作员名称
     */
    private String operator_name;

    /**
     * 用户营业部编号
     */
    private String branch_no;

    /**
     * 用户所属分公司
     */
    private String branch_name;

    /**
     * 上级用户营业部编号
     */
    private String up_branch_no;

    /**
     * 上级用户所属分公司
     */
    private String up_branch_name;

}
