package com.cairh.cpe.monitor.worker.data.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDate;
import java.util.Date;

/**
 * Description：个人在岗统计表
 * Author： slx
 * Date： 2024/4/18 下午4:44
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("PersonalOnDutyStatistics")
public class PersonalOnDutyStatistics {

    /**
     * 操作员编号
     */
    private String operator_no;

    /**
     * 操作员姓名
     */
    private String operator_name;

    /**
     * 用户营业部编号
     */
    private String branch_no;

    /**
     * 用户所属分公司
     */
    private String branch_name;

    /**
     * 上级用户营业部编号
     */
    private String up_branch_no;

    /**
     * 上级用户所属分公司
     */
    private String up_branch_name;

    /**
     * 统计日期
     */
    private LocalDate report_date;

    /**
     * 当天第一笔处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date begin_deal_time;

    /**
     * 当天最后一笔处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date end_deal_time;

    /**
     * 全天审核总时长，单位：s
     */
    private int sum_deal_time;

    /**
     * 当天最早上线时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date begin_online_time;

    /**
     * 当天最晚下线时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date end_online_time;

    /**
     * 全天在线时长，单位：s
     */
    private int online_time_sum;

    /**
     * 挂起数量
     */
    private int pause_sum;

    /**
     * 回收数量
     */
    private int recycle_sum;

    /**
     * 转交数量
     */
    private int deliver_sum;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date create_datetime;

}
