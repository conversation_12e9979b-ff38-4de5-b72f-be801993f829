package com.cairh.cpe.monitor.worker.data.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 见证系统操作员每日在线时长实体类
 *
 * <AUTHOR>
 * @since 2024/1/22
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("queuelevelspeed")
public class QueueLevelSpeed extends Model<QueueLevelSpeed> {

    private static final long serialVersionUID = 0xe7d9483e3ff3b2dfL;

    /**
     * 报表统计日期
     */
    @TableField("create_datetime")
    private Date create_datetime;

    /**
     * 单位时间内，leve0队列派发成功数量
     */
    @TableField("queue_level0_num")
    private String queue_level0_num;

    /**
     * 单位时间内，leve1队列派发成功数量
     */
    @TableField("queue_level1_num")
    private String queue_level1_num;

    /**
     * 单位时间内，leve2队列派发成功数量
     */
    @TableField("queue_level2_num")
    private String queue_level2_num;

    /**
     * 单位时间内，leve3队列派发成功数量
     */
    @TableField("queue_level3_num")
    private String queue_level3_num;

    /**
     * 单位时间内，leve4队列派发成功数量
     */
    @TableField("queue_level4_num")
    private String queue_level4_num;
}