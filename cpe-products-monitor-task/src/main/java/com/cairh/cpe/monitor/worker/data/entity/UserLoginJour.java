package com.cairh.cpe.monitor.worker.data.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * Description：
 * Author： slx
 * Date： 2024/4/19 下午3:43
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class UserLoginJour {

    private Date report_date;

    private Date create_datetime;

    private String staff_no;

    private String login_type;
}
