package com.cairh.cpe.monitor.worker.data.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * Description：
 * Author： slx
 * Date： 2024/4/19 下午3:43
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class UserLoginJourStatistics {

    private String staff_no;

    private Date begin_online_time;

    private Date end_online_time;

    private int online_time_sum = 0;
}
