package com.cairh.cpe.monitor.worker.data.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cairh.cpe.monitor.worker.data.entity.BusinProcessRequestAuditTrailStatistics;

import java.util.List;

/**
 * 业务流程请求审核跟踪表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025/2/11 13:14
 */
public interface BusinProcessRequestAuditTrailMapper extends BaseMapper<BusinProcessRequestAuditTrailStatistics> {

    List<BusinProcessRequestAuditTrailStatistics> countDayByDateTime(String dateTime);

    List<BusinProcessRequestAuditTrailStatistics> countMonthByDateTime(String dateTime);

    List<BusinProcessRequestAuditTrailStatistics> countYearByDateTime(String dateTime);
}
