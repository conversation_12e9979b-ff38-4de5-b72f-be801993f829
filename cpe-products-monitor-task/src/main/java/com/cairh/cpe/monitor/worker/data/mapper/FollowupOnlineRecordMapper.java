package com.cairh.cpe.monitor.worker.data.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cairh.cpe.monitor.worker.data.entity.FollowupOnlineRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface FollowupOnlineRecordMapper extends BaseMapper<FollowupOnlineRecord> {
    List<FollowupOnlineRecord> selectByOperatorNoAndCreateDatetime(@Param("operatorNo") String operatorNos, @Param("startTime") String startTime, @Param("endTime") String endTime);
}
