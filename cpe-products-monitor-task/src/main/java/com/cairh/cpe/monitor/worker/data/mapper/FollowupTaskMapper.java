package com.cairh.cpe.monitor.worker.data.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cairh.cpe.monitor.worker.data.entity.FollowupOperatorStatistics;
import com.cairh.cpe.monitor.worker.data.entity.FollowupTask;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface FollowupTaskMapper extends BaseMapper<FollowupTask> {
    List<FollowupOperatorStatistics> statisticsOperatorTask(@Param("startTime") String startTime, @Param("endTime") String endTime);
}
