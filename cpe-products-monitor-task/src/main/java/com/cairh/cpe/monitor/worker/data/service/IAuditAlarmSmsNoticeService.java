package com.cairh.cpe.monitor.worker.data.service;

/**
 * 见证告警短信通知服务
 *
 * <AUTHOR>
 * @since 2025/7/1 09:40
 */
public interface IAuditAlarmSmsNoticeService {


    /**
     * 待处理任务告警通知
     */
    void pendingTaskAlarmNotice();

    /**
     * 平均响应时长告警通知
     */
    void avgResponseAlarmNotice();

    /**
     * 长时间未有新任务告警通知
     */
    void newTaskAlarmNotice();

    /**
     * 单笔任务等待时长告警通知
     */
    void waitTaskAlarmNotice();

    /**
     * 单笔任务分配失败次数告警通知
     */
    void failedTaskAlarmNotice();

    /**
     * 操作频次告警通知
     */
    void operatorIdleAlarmNotice();

    /**
     * 自动驳回任务告警通知
     */
    void autoRejectTaskAlarmNotice();
}
