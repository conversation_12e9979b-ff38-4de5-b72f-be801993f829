package com.cairh.cpe.monitor.worker.data.service;


import com.cairh.cpe.monitor.worker.data.entity.BusinFlowTask;

import java.util.Date;
import java.util.List;

/**
 * Description：任务操作流水表服务
 * Author： slx
 * Date： 2024/4/15 下午3:46
 */
public interface IBusinFlowTaskService {

    /**
     * 根据日期查询操作员
     *
     * @param date 日期
     * @return businFlowTask表中指定日期的操作员信息
     */
    List<BusinFlowTask> selectUserLoginJourByDateAndOperatorNoList(Date date);
}
