package com.cairh.cpe.monitor.worker.data.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cairh.cpe.monitor.worker.data.entity.BusinProcessRequestAuditTrailStatistics;

import java.util.Date;
import java.util.List;

/**
 * 业务流程请求审核跟踪表服务
 *
 * <AUTHOR>
 * @since 2025/2/11 13:15
 */
public interface IBusinProcessRequestAuditTrailService extends IService<BusinProcessRequestAuditTrailStatistics> {


    /**
     * 查询当天列表
     *
     * @param date
     * @return
     */
    List<BusinProcessRequestAuditTrailStatistics> queryDayList(Date date);

    /**
     * 查询当月列表
     *
     * @param date
     * @return
     */
    List<BusinProcessRequestAuditTrailStatistics> queryMonthList(Date date);

    /**
     * 查询当年列表
     *
     * @param date
     * @return
     */
    List<BusinProcessRequestAuditTrailStatistics> queryYearList(Date date);


}
