package com.cairh.cpe.monitor.worker.data.service;


import com.cairh.cpe.monitor.worker.data.entity.UserLoginJourStatistics;

import java.util.Date;
import java.util.List;

/**
 * Description：任务操作流水表服务
 * Author： slx
 * Date： 2024/4/15 下午3:46
 */
public interface IUserLoginJourService {

    /**
     * 根据操作人和日期查询登录信息
     *
     * @param operatorNoList 操作员编号集合
     * @param date           日期
     * @return 登录信息
     */
    List<UserLoginJourStatistics> selectUserLoginJourByDateAndOperatorNoList(List<String> operatorNoList, Date date);
}
