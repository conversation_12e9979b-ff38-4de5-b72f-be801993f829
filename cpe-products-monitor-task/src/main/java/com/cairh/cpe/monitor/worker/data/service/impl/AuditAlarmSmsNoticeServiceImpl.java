package com.cairh.cpe.monitor.worker.data.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.cairh.cpe.common.constant.CacheKeyConfig;
import com.cairh.cpe.common.constant.Constant;
import com.cairh.cpe.common.dto.AuditAlarmDetailResp;
import com.cairh.cpe.common.dto.AuditAlarmOperatorDetail;
import com.cairh.cpe.common.dto.AuditAlarmTaskDetail;
import com.cairh.cpe.common.service.IAuditAlarmService;
import com.cairh.cpe.common.util.ComponentWorkTimeService;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import com.cairh.cpe.esb.component.notice.IEsbComponentSmsDubboService;
import com.cairh.cpe.esb.component.notice.dto.req.NoticeSendSmsRequest;
import com.cairh.cpe.monitor.worker.constant.AuditAlarmSmsNoticeMessageEnum;
import com.cairh.cpe.monitor.worker.data.service.IAuditAlarmSmsNoticeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2025/7/1 09:50
 */
@Slf4j
@Service
public class AuditAlarmSmsNoticeServiceImpl implements IAuditAlarmSmsNoticeService {

    @Autowired
    protected CompositePropertySources compositePropertySources;
    @Autowired
    private IAuditAlarmService auditAlarmService;
    @DubboReference(check = false, lazy = true)
    private IEsbComponentSmsDubboService smsDubboService;
    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private ComponentWorkTimeService componentWorkTimeService;
    @Autowired
    @Qualifier("monitorExecutor")
    private ThreadPoolTaskExecutor monitorExecutor;

    @Override
    public void pendingTaskAlarmNotice() {
        processAlarmNotice(
                "待处理任务告警",
                () -> {
                    AuditAlarmDetailResp.PendingTaskDetail detail = auditAlarmService.getPendingTaskDetail();
                    return detail != null ? java.util.Collections.singletonList(detail) : java.util.Collections.emptyList();
                },
                AuditAlarmSmsNoticeMessageEnum.PENDINGTASK,
                Constant.MONITOR_ALARM_PENDINGTASK_SMS_TEMPLATE,
                AuditAlarmDetailResp.PendingTaskDetail::getPending_task_num
        );
    }

    @Override
    public void avgResponseAlarmNotice() {
        processAlarmNotice(
                "平均响应时长告警",
                () -> {
                    AuditAlarmDetailResp.AvgResponseDetail detail = auditAlarmService.getAvgResponseDetail();
                    return detail != null ? java.util.Collections.singletonList(detail) : java.util.Collections.emptyList();
                },
                AuditAlarmSmsNoticeMessageEnum.AVGRESPONSE,
                Constant.MONITOR_ALARM_AVGRESPONSE_SMS_TEMPLATE,
                AuditAlarmDetailResp.AvgResponseDetail::getAvg_response_time
        );
    }

    @Override
    public void newTaskAlarmNotice() {
        processAlarmNotice(
                "新进任务告警",
                auditAlarmService::getNewTaskList,
                AuditAlarmSmsNoticeMessageEnum.NEWTASK,
                Constant.MONITOR_ALARM_NEWTASK_SMS_TEMPLATE,
                AuditAlarmTaskDetail::getInterval_time
        );
    }

    @Override
    public void waitTaskAlarmNotice() {
        processAlarmNotice(
                "单笔任务等待时长告警",
                auditAlarmService::getTaskWaitDetailList,
                AuditAlarmSmsNoticeMessageEnum.WAITTASK,
                Constant.MONITOR_ALARM_WAITTASK_SMS_TEMPLATE,
                AuditAlarmTaskDetail::getWait_time
        );
    }

    @Override
    public void failedTaskAlarmNotice() {
        processAlarmNotice(
                "单笔任务分配失败告警",
                auditAlarmService::getFailedTaskAssignmentList,
                AuditAlarmSmsNoticeMessageEnum.FAILEDTASK,
                Constant.MONITOR_ALARM_FAILEDTASK_SMS_TEMPLATE,
                AuditAlarmTaskDetail::getFail_assignment_num
        );
    }

    @Override
    public void operatorIdleAlarmNotice() {
        processAlarmNotice(
                "操作员空闲告警",
                auditAlarmService::getOperatorDetailList,
                AuditAlarmSmsNoticeMessageEnum.OPERATORIDLE,
                Constant.MONITOR_ALARM_OPERATOR_SMS_TEMPLATE,
                AuditAlarmOperatorDetail::getIdle_time
        );
    }

    /**
     * 通用告警通知处理方法
     *
     * @param alarmType      告警类型描述
     * @param dataSupplier   数据获取函数
     * @param messageEnum    消息枚举
     * @param templateKey    模板配置key
     * @param valueExtractor 值提取函数
     */
    private <T> void processAlarmNotice(String alarmType,
                                        java.util.function.Supplier<List<T>> dataSupplier,
                                        AuditAlarmSmsNoticeMessageEnum messageEnum,
                                        String templateKey,
                                        java.util.function.Function<T, Integer> valueExtractor) {
        CompletableFuture.runAsync(() -> {
            try {
                // 获取数据列表
                List<T> dataList = dataSupplier.get();
                if (CollectionUtils.isEmpty(dataList)) {
                    log.debug("{}数据列表为空，跳过告警处理", alarmType);
                    return;
                }

                T firstItem = dataList.get(0);
                if (firstItem == null || !isAlarmRequired(firstItem)) {
                    log.debug("{}数据为空或无需告警，跳过处理", alarmType);
                    return;
                }

                // 检查是否在告警时间内
                if (!componentWorkTimeService.isWorkTime(Constant.WORK_TIME_AUDIT_ALARM)) {
                    log.info("非告警通知时间，不发送短信");
                    return;
                }

                // 检查是否已发送过告警
                String redisKey = CacheKeyConfig.AUDIT_ALARM_REDIS_KEY + messageEnum.getCode();
                if (redisKeyExists(redisKey)) {
                    log.info("{}短信通知已发送，不重复发送", alarmType);
                    return;
                }

                // 发送告警短信
                String smsTemplate = compositePropertySources.getProperty(templateKey, messageEnum.getValue());
                Integer value = valueExtractor.apply(firstItem);
                String message = String.format(smsTemplate, value);
                sendMessage(message);
            } catch (Exception e) {
                log.error("{}通知处理异常", alarmType, e);
            }
        }, monitorExecutor);
    }

    /**
     * 判断是否需要告警
     */
    private boolean isAlarmRequired(Object item) {
        if (item instanceof AuditAlarmTaskDetail) {
            return StringUtils.equals(Constant.ONE_STR, ((AuditAlarmTaskDetail) item).getType());
        } else if (item instanceof AuditAlarmOperatorDetail) {
            return StringUtils.equals(Constant.ONE_STR, ((AuditAlarmOperatorDetail) item).getType());
        } else if (item instanceof AuditAlarmDetailResp.PendingTaskDetail) {
            return StringUtils.equals(Constant.ONE_STR, ((AuditAlarmDetailResp.PendingTaskDetail) item).getType());
        } else if (item instanceof AuditAlarmDetailResp.AvgResponseDetail) {
            return StringUtils.equals(Constant.ONE_STR, ((AuditAlarmDetailResp.AvgResponseDetail) item).getType());
        }
        return false;
    }

    /**
     * redis是否存在该key
     * 并处理
     */
    private boolean redisKeyExists(String redisKey) {
        // redis是否存在该key
        if (redisTemplate.hasKey(redisKey)) {
            return true;
        }
        // 通知间隔时间：分钟，默认60
        int intervalMinutes = Integer.parseInt(compositePropertySources.getProperty(Constant.MONITOR_ALARM_SMS_INTERVAL_TIME, "60"));
        // 设置redis缓存时间为通知间隔时间（分钟）
        redisTemplate.opsForValue().set(redisKey, "1", intervalMinutes, TimeUnit.MINUTES);
        return false;
    }

    /**
     * 发送短信
     */
    private void sendMessage(String message) {
        // 获取手机号
        String mobile_tel = compositePropertySources.getProperty(Constant.MONITOR_ALARM_SMS_NOTICE_MOBILE);
        if (StringUtils.isEmpty(mobile_tel)) {
            log.warn("mobile_tel is empty!");
            return;
        }
        // 手机号多个用逗号隔开，需要多次发送
        String[] mobile_tel_array = mobile_tel.split(Constant.COMMA);
        for (String mobile_tel_item : mobile_tel_array) {
            if (StringUtils.isEmpty(mobile_tel_item)) {
                continue;
            }
            // 短信发送
            NoticeSendSmsRequest noticeSendSmsRequest = new NoticeSendSmsRequest();
            noticeSendSmsRequest.setMsg_content(message);
            noticeSendSmsRequest.setSend_type(Constant.SEND_TYPE);
            noticeSendSmsRequest.setMobile_tel(mobile_tel_item);
            noticeSendSmsRequest.setChannel_type(Constant.CHANNEL_TYPE);
            noticeSendSmsRequest.setService_vender(Constant.KAFKA_SERVICE_VENDER);
            noticeSendSmsRequest.setBranch_no(Constant.SPECIAL_BRANCH);
            noticeSendSmsRequest.setUp_branch_no(Constant.SPECIAL_BRANCH);
            log.info("监控告警短信信息：{}", JSON.toJSONString(noticeSendSmsRequest));
            smsDubboService.noticeSendSms(noticeSendSmsRequest);
        }
    }
}
