package com.cairh.cpe.monitor.worker.data.service.impl;

import com.cairh.cpe.monitor.worker.data.entity.BranchInfo;
import com.cairh.cpe.monitor.worker.data.mapper.BranchInfoMapper;
import com.cairh.cpe.monitor.worker.data.service.IBranchInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * Description：
 * Author： slx
 * Date： 2024/4/23 下午1:43
 */
@Service
public class BranchInfoServiceImpl implements IBranchInfoService {

    @Autowired
    private BranchInfoMapper branchInfoMapper;

    @Override
    public List<BranchInfo> selectBranchInfoList() {
        List<BranchInfo> branchInfos = branchInfoMapper.selectBranchInfoList();
        if (!CollectionUtils.isEmpty(branchInfos)) {
            return branchInfos;
        }
        return Collections.emptyList();
    }
}
