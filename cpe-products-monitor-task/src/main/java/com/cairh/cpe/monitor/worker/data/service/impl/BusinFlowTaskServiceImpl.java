package com.cairh.cpe.monitor.worker.data.service.impl;

import com.cairh.cpe.monitor.worker.data.entity.BusinFlowTask;
import com.cairh.cpe.monitor.worker.data.mapper.BusinFlowTaskMapper;
import com.cairh.cpe.monitor.worker.data.service.IBusinFlowTaskService;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description：见证任务表处理服务
 * Author： slx
 * Date： 2024/4/23 下午1:24
 */
@Service
public class BusinFlowTaskServiceImpl implements IBusinFlowTaskService {

    @Autowired
    private BusinFlowTaskMapper businFlowTaskMapper;

    @Override
    public List<BusinFlowTask> selectUserLoginJourByDateAndOperatorNoList(Date date) {
        String dateTime = DateFormatUtils.format(date, "yyyy-MM-dd");
        List<BusinFlowTask> businFlowTasks = businFlowTaskMapper.selectBusinFlowTaskByDateTime(dateTime);
        if (!CollectionUtils.isEmpty(businFlowTasks)) {
            // 筛选去除deal_datetime或finish_datetime为空的数据
            return businFlowTasks
                    .stream()
                    .filter(businFlowTask -> businFlowTask.getDeal_datetime() != null && businFlowTask.getFinish_datetime() != null)
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
}
