package com.cairh.cpe.monitor.worker.data.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.monitor.worker.data.entity.BusinProcessRequestAuditTrailStatistics;
import com.cairh.cpe.monitor.worker.data.mapper.BusinProcessRequestAuditTrailMapper;
import com.cairh.cpe.monitor.worker.data.service.IBusinProcessRequestAuditTrailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/11 13:21
 */
@Slf4j
@Service
public class BusinProcessRequestAuditTrailServiceImpl extends ServiceImpl<BusinProcessRequestAuditTrailMapper, BusinProcessRequestAuditTrailStatistics>
        implements IBusinProcessRequestAuditTrailService {

    @Override
    public List<BusinProcessRequestAuditTrailStatistics> queryDayList(Date date) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("queryDayList start");
        log.info("查询queryDayList date={}的数据", JSON.toJSON(date));
        if (date == null) {
            return Collections.emptyList();
        }

        try {
            String dateTime = DateFormatUtils.format(date, "yyyy-MM-dd HH:mm:ss");
            List<BusinProcessRequestAuditTrailStatistics> businProcessRequestAuditTrailStatisticsList = this.baseMapper.countDayByDateTime(dateTime);
            if (!businProcessRequestAuditTrailStatisticsList.isEmpty()) {
                stopWatch.stop();
                log.info("queryDayList size：{},{}", businProcessRequestAuditTrailStatisticsList.size(), stopWatch.prettyPrint());
                return businProcessRequestAuditTrailStatisticsList;
            }
        } catch (Exception e) {
            log.error("查询queryDayList date={}的数据异常", JSON.toJSON(date), e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<BusinProcessRequestAuditTrailStatistics> queryMonthList(Date date) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("queryMonthList start");
        log.info("查询queryMonthList date={}的数据", JSON.toJSON(date));
        if (date == null) {
            return Collections.emptyList();
        }

        try {
            String dateTime = DateFormatUtils.format(date, "yyyy-MM-dd HH:mm:ss");
            List<BusinProcessRequestAuditTrailStatistics> businProcessRequestAuditTrailStatisticsList = this.baseMapper.countMonthByDateTime(dateTime);
            if (!businProcessRequestAuditTrailStatisticsList.isEmpty()) {
                stopWatch.stop();
                log.info("queryMonthList size：{},{}", businProcessRequestAuditTrailStatisticsList.size(), stopWatch.prettyPrint());
                return businProcessRequestAuditTrailStatisticsList;
            }
        } catch (Exception e) {
            log.error("查询queryMonthList date={}的数据异常", JSON.toJSON(date), e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<BusinProcessRequestAuditTrailStatistics> queryYearList(Date date) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("queryYearList start");
        log.info("查询queryYearList date={}的数据", JSON.toJSON(date));
        if (date == null) {
            return Collections.emptyList();
        }
        try {
            String dateTime = DateFormatUtils.format(date, "yyyy-MM-dd HH:mm:ss");
            List<BusinProcessRequestAuditTrailStatistics> businProcessRequestAuditTrailStatisticsList = this.baseMapper.countYearByDateTime(dateTime);
            if (!businProcessRequestAuditTrailStatisticsList.isEmpty()) {
                stopWatch.stop();
                log.info("queryYearList size：{},{}", businProcessRequestAuditTrailStatisticsList.size(), stopWatch.prettyPrint());
                return businProcessRequestAuditTrailStatisticsList;
            }
        } catch (Exception e) {
            log.error("查询queryYearList date={}的数据异常", JSON.toJSON(date), e);
        }
        return Collections.emptyList();
    }
}
