package com.cairh.cpe.monitor.worker.data.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.common.util.DateUtil;
import com.cairh.cpe.db.config.IdGenerator;
import com.cairh.cpe.monitor.worker.data.entity.BusinProcessRequestAuditTrailStatistics;
import com.cairh.cpe.monitor.worker.data.entity.BusinReviewComplete;
import com.cairh.cpe.monitor.worker.data.mapper.BusinReviewCompleteMapper;
import com.cairh.cpe.monitor.worker.data.service.IBusinProcessRequestAuditTrailService;
import com.cairh.cpe.monitor.worker.data.service.IBusinReviewCompleteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 审核通过业务量报表服务实现
 *
 * <AUTHOR>
 * @since 2025/2/11 11:28
 */
@Slf4j
@Service
public class BusinReviewCompleteServiceImpl extends ServiceImpl<BusinReviewCompleteMapper, BusinReviewComplete>
        implements IBusinReviewCompleteService {

    // 日期分隔符
    private static final String DATE_SPLIT = "~";

    @Autowired
    private IBusinProcessRequestAuditTrailService businProcessRequestAuditTrailService;
    @Resource
    private IdGenerator idGenerator;

    @Override
    public void saveBusinReviewComplete(String dateParam) {
        if (StringUtils.isEmpty(dateParam)) {
            dateParam = DateFormatUtils.format(getYesterdayDate(), "yyyy-MM-dd");
        }
        List<String> dateList = getDateList(dateParam);

        for (String date : dateList) {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("task_id job start");
            batchHandleData(date);
            stopWatch.stop();
            log.info("task_id job {}", stopWatch.prettyPrint());
        }
    }

    private void batchHandleData(String date) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("save BusinReviewComplete start");

        Date statisticsDate = null;
        if (StringUtils.isNotEmpty(date)) {
            try {
                statisticsDate = DateUtil.formatStrDateTime(date, "yyyy-MM-dd");
            } catch (Exception e) {
                log.error("[saveBusinReviewComplete]日期格式错误，date={}", date);
            }
        } else {
            // 如果没有指定日期，则获取昨天的日期
            statisticsDate = getYesterdayDate();
        }
        log.info("save BusinReviewComplete date：{}", JSON.toJSONString(date));

        // 获取指定年份的数据
        List<BusinProcessRequestAuditTrailStatistics> queryYearList = businProcessRequestAuditTrailService.queryYearList(statisticsDate);
        if (queryYearList.isEmpty()) {
            log.info("没有获取到{}所在年份的数据", JSON.toJSON(date));
            return;
        }
        // 获取指定日期的数据
        List<BusinProcessRequestAuditTrailStatistics> queryDayList = businProcessRequestAuditTrailService.queryDayList(statisticsDate);
        // 获取指定月份的数据
        List<BusinProcessRequestAuditTrailStatistics> queryMonthList = businProcessRequestAuditTrailService.queryMonthList(statisticsDate);

        String formattedDate = DateFormatUtils.format(statisticsDate, "yyyyMMdd");
        // 将数据封装到BusinReviewComplete实体中
        List<BusinReviewComplete> businReviewCompletes = getBusinReviewCompletes(queryDayList, queryMonthList, queryYearList, formattedDate);
        try {
            // 先删除旧数据
            LambdaQueryWrapper<BusinReviewComplete> deleteWrapper = new LambdaQueryWrapper<BusinReviewComplete>()
                    .eq(BusinReviewComplete::getCurr_date, Integer.parseInt(formattedDate));
            if (remove(deleteWrapper)) {
                log.info("删除旧数据成功，删除日期：{}", JSON.toJSON(date));
            }
            saveBatch(businReviewCompletes);
            stopWatch.stop();
            log.info("save BusinReviewComplete end, cost time: {}, data size: {} ", stopWatch.getTotalTimeMillis(), businReviewCompletes.size());
        } catch (Exception e) {
            log.error("保存BusinReviewComplete异常", e);
        }
    }


    private List<BusinReviewComplete> getBusinReviewCompletes(List<BusinProcessRequestAuditTrailStatistics> queryDayList, List<BusinProcessRequestAuditTrailStatistics> queryMonthList,
                                                              List<BusinProcessRequestAuditTrailStatistics> queryYearList, String formattedDate) {
        // 将数据封装到BusinReviewComplete实体中
        return queryYearList
                .stream()
                .map(statistics -> {
                    BusinReviewComplete businReviewComplete = new BusinReviewComplete();
                    businReviewComplete.setSerial_id(idGenerator.nextUUID(null));
                    businReviewComplete.setBranch_no(statistics.getUser_branch_no());
                    businReviewComplete.setBranch_name(statistics.getUser_branch_name());
                    businReviewComplete.setUp_branch_no(statistics.getUser_up_branch_no());
                    businReviewComplete.setUp_branch_name(statistics.getUser_up_branch_name());
                    businReviewComplete.setBusin_type(statistics.getBusin_type());

                    // 匹配当月数据
                    BusinProcessRequestAuditTrailStatistics monthStatistics = queryMonthList.stream()
                            .filter(s -> s.getUser_branch_no().equals(statistics.getUser_branch_no())
                                    && s.getUser_branch_name().equals(statistics.getUser_branch_name())
                                    && s.getUser_up_branch_no().equals(statistics.getUser_up_branch_no())
                                    && s.getUser_up_branch_name().equals(statistics.getUser_up_branch_name())
                                    && s.getBusin_type().equals(statistics.getBusin_type())
                            ).findFirst().orElse(null);
                    // 匹配当日数据
                    BusinProcessRequestAuditTrailStatistics dayStatistics = queryDayList.stream()
                            .filter(s -> s.getUser_branch_no().equals(statistics.getUser_branch_no())
                                    && s.getUser_branch_name().equals(statistics.getUser_branch_name())
                                    && s.getUser_up_branch_no().equals(statistics.getUser_up_branch_no())
                                    && s.getUser_up_branch_name().equals(statistics.getUser_up_branch_name())
                                    && s.getBusin_type().equals(statistics.getBusin_type())
                            ).findFirst().orElse(null);
                    businReviewComplete.setYear_complete_num(statistics.getBranch_num());
                    businReviewComplete.setMonth_complete_num(Objects.nonNull(monthStatistics) ? monthStatistics.getBranch_num() : 0);
                    businReviewComplete.setComplete_num(Objects.nonNull(dayStatistics) ? dayStatistics.getBranch_num() : 0);
                    // curr_date格式需要为yyyymmdd
                    businReviewComplete.setCurr_date(Integer.parseInt(formattedDate));
                    businReviewComplete.setCreate_datetime(new Date());
                    return businReviewComplete;
                })
                .collect(Collectors.toList());
    }

    // 将 LocalDate 转换为 Date 的工具方法
    private Date convertToDate(LocalDate localDate) {
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    private List<String> getDateList(String dateParam) {
        List<String> dateList = new ArrayList<>(Collections.singletonList(dateParam));
        if (!dateParam.contains(DATE_SPLIT)) {
            return dateList;
        }
        dateList.clear();
        String[] dateSplit = dateParam.split(DATE_SPLIT);
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        LocalDate startDate = LocalDate.parse(dateSplit[0], formatter);
        LocalDate endDate = LocalDate.parse(dateSplit[1], formatter);

        // 计算两个日期之间的所有日期
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            dateList.add(currentDate.format(formatter));
            currentDate = currentDate.plusDays(1);
        }
        return dateList;
    }

    private Date getYesterdayDate() {
        // 获取上一天的日期date
        LocalDate previousDate = LocalDate.now().minusDays(1);
        return convertToDate(previousDate);
    }

}
