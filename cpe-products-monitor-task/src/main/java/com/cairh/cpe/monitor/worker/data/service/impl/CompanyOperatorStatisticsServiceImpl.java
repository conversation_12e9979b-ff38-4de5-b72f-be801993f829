package com.cairh.cpe.monitor.worker.data.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.monitor.worker.constant.Constant;
import com.cairh.cpe.monitor.worker.data.entity.BranchInfo;
import com.cairh.cpe.monitor.worker.data.entity.CompanyOperatorStatistics;
import com.cairh.cpe.monitor.worker.data.entity.OperatorInfo;
import com.cairh.cpe.monitor.worker.data.mapper.CompanyOperatorStatisticsMapper;
import com.cairh.cpe.monitor.worker.data.service.IBranchInfoService;
import com.cairh.cpe.monitor.worker.data.service.ICompanyOperatorStatisticsService;
import com.cairh.cpe.monitor.worker.data.service.IOperatorInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分公司在岗坐席统计表服务实现
 *
 * <AUTHOR>
 * @since 2025/6/17 14:41
 */
@Slf4j
@Service
public class CompanyOperatorStatisticsServiceImpl extends ServiceImpl<CompanyOperatorStatisticsMapper, CompanyOperatorStatistics> implements ICompanyOperatorStatisticsService {

    @Resource
    private IBranchInfoService branchInfoService;
    @Resource
    private IOperatorInfoService operatorInfoService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveCompanyOperatorStatistics() {
        log.info("【{}】开始保存分公司在岗坐席统计表！", new Date());
        // 获取所有操作员
        List<OperatorInfo> operatorInfos = operatorInfoService.selectOperatorInfoList();
        if (CollectionUtils.isEmpty(operatorInfos)) {
            log.warn("查询operatorinfo为空！");
            return;
        }
        // 获取所有营业部
        List<BranchInfo> branchInfos = branchInfoService.selectBranchInfoList();
        if (CollectionUtils.isEmpty(branchInfos)) {
            log.warn("查询allBranch为空！");
            return;
        }
        try {
            Map<String, BranchInfo> branchInfoMap = branchInfos.stream().collect(Collectors.toMap(BranchInfo::getBranch_no, Function.identity()));
            Map<String, CompanyOperatorStatistics> companyOperatorStatisticsMap = new HashMap<>();
            operatorInfos.forEach(operatorInfo -> {
                BranchInfo branchInfo = branchInfoMap.get(operatorInfo.getBranch_no());
                if (Objects.isNull(branchInfo)) {
                    log.warn("查询操作员：{} 所在营业部:{} branchinfo为空！", operatorInfo.getStaff_no(), operatorInfo.getBranch_no());
                    return;
                }
                // 营运中心 or 当前营业部为子公司并且营业部类型为2 or 当前营业部为总部18
                if (StringUtils.equals(Constant.SPECIAL_BRANCH, operatorInfo.getBranch_no())
                        || StringUtils.equals(Constant.DEFAULT_KH_BRANCH_CODE, branchInfo.getUp_branch_no())
                        && StringUtils.equals(Constant.LEVEL_BRANCH_OFFICE, branchInfo.getBranch_type())
                        || StringUtils.equals(Constant.DEFAULT_KH_BRANCH_CODE, branchInfo.getBranch_no())) {
                    companyOperatorStatisticsMap.put(branchInfo.getBranch_no(), buildCompanyOperatorStatistics(operatorInfo, branchInfo, companyOperatorStatisticsMap));
                    return;
                }
                // 获取上级营业部信息（子公司）
                BranchInfo upBranchInfo = branchInfoMap.get(branchInfo.getUp_branch_no());
                if (Objects.isNull(upBranchInfo)) {
                    log.warn("查询操作员：{} 所在营业部:{} 的上级营业部：{} branchinfo为空！", operatorInfo.getStaff_no(), operatorInfo.getBranch_no(), branchInfo.getUp_branch_no());
                    return;
                }
                // 上级营业部为子公司并且营业部类型为2
                if (StringUtils.equals(Constant.DEFAULT_KH_BRANCH_CODE, upBranchInfo.getUp_branch_no())
                        && StringUtils.equals(Constant.LEVEL_BRANCH_OFFICE, upBranchInfo.getBranch_type())) {
                    companyOperatorStatisticsMap.put(upBranchInfo.getBranch_no(), buildCompanyOperatorStatistics(operatorInfo, upBranchInfo, companyOperatorStatisticsMap));
                }

            });
            // 加入创建时间以及创建人，转成list
            List<CompanyOperatorStatistics> companyOperatorStatisticsList = companyOperatorStatisticsMap.values().stream().peek(companyOperatorStatistics -> {
                companyOperatorStatistics.setCreate_datetime(new Date());
                companyOperatorStatistics.setCreate_by("system");
            }).collect(Collectors.toList());
            log.info("【{}】分公司在岗坐席统计: {}", new Date(), JSON.toJSONString(companyOperatorStatisticsList));
            // 先删除原表数据，再保存新数据，全表删除
            remove(new QueryWrapper<>());
            boolean saveBatch = saveBatch(companyOperatorStatisticsList);
            if (saveBatch) {
                log.info("【{}】分公司在岗坐席统计保存成功！", new Date());
            } else {
                log.warn("【{}】分公司在岗坐席统计保存失败！", new Date());
            }
        } catch (Exception e) {
            log.error("【{}】分公司在岗坐席统计保存失败！", new Date(), e);
        }
        log.info("【{}】结束分公司在岗坐席统计！", new Date());
    }

    private CompanyOperatorStatistics buildCompanyOperatorStatistics(OperatorInfo operatorInfo, BranchInfo branchInfo, Map<String, CompanyOperatorStatistics> companyOperatorStatisticsMap) {
        CompanyOperatorStatistics companyOperatorStatistics = new CompanyOperatorStatistics();
        CompanyOperatorStatistics oldCompanyOperatorStatistics = companyOperatorStatisticsMap.get(branchInfo.getBranch_no());
        if (Objects.nonNull(oldCompanyOperatorStatistics)) {
            BeanUtils.copyProperties(oldCompanyOperatorStatistics, companyOperatorStatistics);
            // 冻结
            if (StringUtils.equals(Constant.OPERATOR_STATUS_7, operatorInfo.getStatus())) {
                Integer operatorFrozenNum = oldCompanyOperatorStatistics.getOperator_frozen_num();
                companyOperatorStatistics.setOperator_frozen_num(Objects.isNull(operatorFrozenNum) ? 1 : operatorFrozenNum + 1);
            }
            // 正常
            if (StringUtils.equals(Constant.OPERATOR_STATUS_8, operatorInfo.getStatus())) {
                Integer operatorNormalNum = oldCompanyOperatorStatistics.getOperator_normal_num();
                companyOperatorStatistics.setOperator_normal_num(Objects.isNull(operatorNormalNum) ? 1 : operatorNormalNum + 1);
            }
            // 注销
            if (StringUtils.equals(Constant.OPERATOR_STATUS_9, operatorInfo.getStatus())) {
                Integer operatorInactiveNum = oldCompanyOperatorStatistics.getOperator_inactive_num();
                companyOperatorStatistics.setOperator_inactive_num(Objects.isNull(operatorInactiveNum) ? 1 : operatorInactiveNum + 1);
            }
            if (auditRole(operatorInfo)) {
                Integer auditOperatorNum = oldCompanyOperatorStatistics.getAudit_operator_num();
                companyOperatorStatistics.setAudit_operator_num(Objects.isNull(auditOperatorNum) ? 1 : auditOperatorNum + 1);
            }
        } else {
            companyOperatorStatistics.setBranch_no(branchInfo.getBranch_no());
            companyOperatorStatistics.setBranch_name(branchInfo.getBranch_name());
            // 冻结
            if (StringUtils.equals(Constant.OPERATOR_STATUS_7, operatorInfo.getStatus())) {
                companyOperatorStatistics.setOperator_frozen_num(1);
            }
            // 正常
            if (StringUtils.equals(Constant.OPERATOR_STATUS_8, operatorInfo.getStatus())) {
                companyOperatorStatistics.setOperator_normal_num(1);
            }
            // 注销
            if (StringUtils.equals(Constant.OPERATOR_STATUS_9, operatorInfo.getStatus())) {
                companyOperatorStatistics.setOperator_inactive_num(1);
            }
            if (auditRole(operatorInfo)) {
                companyOperatorStatistics.setAudit_operator_num(1);
            }
        }
        return companyOperatorStatistics;
    }

    /**
     * 判断操作员包含为见证角色
     */
    private boolean auditRole(OperatorInfo operatorInfo) {
        return StringUtils.contains(operatorInfo.getEn_roles() + ",", ",1,");
    }
}
