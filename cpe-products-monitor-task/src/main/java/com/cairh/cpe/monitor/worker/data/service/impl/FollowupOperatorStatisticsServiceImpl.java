package com.cairh.cpe.monitor.worker.data.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.cairh.cpe.common.util.DateUtil;
import com.cairh.cpe.db.config.IdGenerator;
import com.cairh.cpe.monitor.worker.constant.Constant;
import com.cairh.cpe.monitor.worker.data.entity.FollowupOnlineRecord;
import com.cairh.cpe.monitor.worker.data.entity.FollowupOperatorStatistics;
import com.cairh.cpe.monitor.worker.data.mapper.FollowupOnlineRecordMapper;
import com.cairh.cpe.monitor.worker.data.mapper.FollowupOperatorStatisticsMapper;
import com.cairh.cpe.monitor.worker.data.mapper.FollowupTaskMapper;
import com.cairh.cpe.monitor.worker.data.service.IFollowupOperatorStatisticsService;
import com.cairh.cpe.monitor.worker.util.DateConvertUtil;
import com.cairh.cpe.monitor.worker.util.LoginCalculateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FollowupOperatorStatisticsServiceImpl implements IFollowupOperatorStatisticsService {
    @Resource
    private IdGenerator idGenerator;
    @Autowired
    private FollowupOperatorStatisticsMapper followupOperatorStatisticsMapper;
    @Autowired
    private FollowupOnlineRecordMapper followupOnlineRecordMapper;
    @Autowired
    private FollowupTaskMapper followupTaskMapper;

    @Override
    public void operatorDailyStatistics(List<String> dates) {
        if (CollUtil.isNotEmpty(dates)) {
            for (String date : dates) {
                operatorDailyStatistics(date);
            }
        } else {
            operatorDailyStatistics(DateUtil.getYesterdayDate());
        }
    }


    /**
     * 坐席每日统计
     *
     * @param date
     */
    @Override
    public void operatorDailyStatistics(String date) {
        if (StrUtil.isBlank(date)) {
            log.info("operatorDailyStatistics date is blank");
            return;
        }
        log.info("operatorDailyStatistics date = {} start ", date);
        String startTime = DateConvertUtil.appendStartTime(date);
        String endTime = DateConvertUtil.appendEndTime(date);

        //统计今天有任务的坐席
        List<FollowupOperatorStatistics> result = followupTaskMapper.statisticsOperatorTask(startTime, endTime);

        if (CollUtil.isEmpty(result)) {
            log.info("operatorDailyStatistics Operator_no isEmpty date = {}", date);
            log.info("operatorDailyStatistics date = {}  end ", date);
            return;
        }

        //查询今天有任务的坐席的在线记录
        List<String> operatorNos = result.stream().map(FollowupOperatorStatistics::getOperator_no).collect(Collectors.toList());

        Map<String, FollowupOperatorStatistics> operatorMap = result.stream().collect(Collectors.toMap(FollowupOperatorStatistics::getOperator_no, ele -> ele, (a, b) -> a));

        for (String operatorNo : operatorNos) {
            List<FollowupOnlineRecord> followupOnlineRecords = followupOnlineRecordMapper.selectByOperatorNoAndCreateDatetime(operatorNo, startTime, endTime);
            if (CollUtil.isEmpty(followupOnlineRecords)) {
                log.info("operatorDailyStatistics followupOnlineRecords isEmpty date = {}", date);
                continue;
            }

            List<FollowupOnlineRecord> sortRecords = followupOnlineRecords.stream().sorted(Comparator.comparing(FollowupOnlineRecord::getCreate_datetime)).collect(Collectors.toList());
            FollowupOperatorStatistics insertDb = operatorMap.get(operatorNo);
            //最早上线时间
            sortRecords.stream()
                    .filter(ele -> Objects.equals(ele.getStatus(), Constant.ONLINE_RECORD_LOGIN))
                    .min(Comparator.comparing(FollowupOnlineRecord::getCreate_datetime))
                    .ifPresent(ele -> insertDb.setEarliest_login_time(ele.getCreate_datetime()));
            //最晚下线时间
            sortRecords.stream()
                    .filter(ele -> Objects.equals(ele.getStatus(), Constant.ONLINE_RECORD_LOGOUT))
                    .max(Comparator.comparing(FollowupOnlineRecord::getCreate_datetime))
                    .ifPresent(ele -> insertDb.setLatest_logout_time(ele.getCreate_datetime()));
            //计算在线时长
            Map<String, Integer> totalOnlineTimeMap = LoginCalculateUtil.calculateTotalOnlineTime(sortRecords);
            insertDb.setTotal_online_seconds(totalOnlineTimeMap.get(operatorNo));

            //删除当天的数据
            followupOperatorStatisticsMapper.deleteByStatDateAndOperatorNo(date, operatorNo);
            insertDb.setSerial_id(idGenerator.nextUUID(null));
            followupOperatorStatisticsMapper.insert(insertDb);
        }

        log.info("operatorDailyStatistics date = {}  end ", date);
    }
}
