package com.cairh.cpe.monitor.worker.data.service.impl;

import com.cairh.cpe.monitor.worker.data.entity.HandupDetails;
import com.cairh.cpe.monitor.worker.data.mapper.HandupDetailsMapper;
import com.cairh.cpe.monitor.worker.data.service.IHandupDetailsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;


/**
 * Description：操作任务流水表服务实现
 * Author： slx
 * Date： 2024/4/15 下午3:52
 */
@Slf4j
@Service
public class HandupDetailsServiceImpl implements IHandupDetailsService {

    @Autowired
    private HandupDetailsMapper mapper;


    @Override
    public List<HandupDetails> selectHandupDetailsListByDateAndOperatorNoList(List<String> operatorNoList, Date date) {
        String dateTime = DateFormatUtils.format(date, "yyyy-MM-dd");
        List<HandupDetails> handupDetailsList =
                mapper.selectHandupDetailsListByDateAndOperatorNoList(operatorNoList, dateTime);
        if (!CollectionUtils.isEmpty(handupDetailsList)) {
            return handupDetailsList;
        }
        return Collections.emptyList();
    }

}
