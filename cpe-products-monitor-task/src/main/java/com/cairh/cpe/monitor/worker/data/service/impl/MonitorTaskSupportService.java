package com.cairh.cpe.monitor.worker.data.service.impl;

import cn.hutool.core.map.MapUtil;
import com.cairh.cpe.common.constant.MonitorErrorCode;
import com.cairh.cpe.context.BizException;
import com.cairh.cpe.monitor.worker.constant.Fields;
import com.cairh.cpe.monitor.worker.data.entity.QueueLevelSpeed;
import com.cairh.cpe.monitor.worker.data.mapper.GeneralXmlMapper;
import com.cairh.cpe.monitor.worker.data.mapper.QueueLevelSpeedMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Slf4j
@Service
public class MonitorTaskSupportService {

    @Autowired
    private GeneralXmlMapper generalXmlMapper;

    @Autowired
    private QueueLevelSpeedMapper queueLevelSpeedMapper;

    public void calculateHourWorkload() {
        String sql = "insert into crh_monitor.hourworkload\n" +
                "select \n" +
                "    sysdate,\n" +
                "    count(1) total_num,\n" +
                "    nvl(sum(decode(a.task_status, '3', 1, '4', 1, '8', 1, 0)), 0) as excute_num \n" +
                "from crh_ac.businflowtask a\n" +
                "where a.create_datetime >= sysdate - 1/24\n";
        Object result;
        try {
            log.info("执行sql：" + sql);
            //result = generalXmlMapper.query(sql);
            result = MapUtil.builder().put(Fields.SIZE, generalXmlMapper.insert(sql)).map();
        } catch (Exception e) {
            throw new BizException(MonitorErrorCode.SQL_EXECUTE_IS_ERROR, "sql执行错误", e);
        }
        //4.返回结果
        log.info("result：" + result);
    }

    public void calculateDailyWorkload() {
        String sql = "insert into crh_monitor.dailyworkload\n" +
                "select \n" +
                "    trunc(sysdate-1) as query_day, \n" +
                "    0 as notg_total_num,\n" +
                "    count(1) as tg_total_num  \n" +
                "from  crh_ac.flowtaskrecorddetails\n" +
                "where trunc(create_datetime) = trunc(sysdate-1) \n" +
                "and   branch_no != '3' \n" +
                "and   invalid_flag = '0' \n";
        Object result;
        try {
            log.info("执行sql：" + sql);
            //result = generalXmlMapper.query(sql);
            result = MapUtil.builder().put(Fields.SIZE, generalXmlMapper.insert(sql)).map();
        } catch (Exception e) {
            throw new BizException(MonitorErrorCode.SQL_EXECUTE_IS_ERROR, "sql执行错误", e);
        }
        //4.返回结果
        log.info("result：" + result);
    }

    public void calculateDailyReject() {
        String sql = "insert into  crh_monitor.rejectstatistics(query_day,total_num,zb_total_num,zb_audit_reject_num,zb_review_reject_num," +
                "tg_total_num,tg_audit_reject_num,tg_review_reject_num,zb_audit_total_num,zb_review_total_num,tg_audit_total_num,tg_review_total_num)\n" +
                "select\n" +
                "    trunc(sysdate-1) as query_day,\n" +
                "    count(1) as total_num,\n" +
                "    nvl(sum(decode(t1.operator_branch_no, '3', 1, 0)), 0) as zb_total_num,\n" +
                "    nvl(sum(decode(t1.operator_branch_no, '3', decode(t1.task_type ||'-'||t1.task_status, 'audit-4', 1, 'audit-8', 1, 0), 0)), 0) as zb_audit_reject_num,\n" +
                "    nvl(sum(decode(t1.operator_branch_no, '3', decode(t1.task_type ||'-'||t1.task_status, 'review-4', 1, 0), 0)), 0) as zb_review_reject_num,\n" +
                "    nvl(sum(decode(t1.operator_branch_no, '3', 0, 1)), 0) as tg_total_num,\n" +
                "    nvl(sum(decode(t1.operator_branch_no, '3', 0, decode(t1.task_type ||'-'||t1.task_status, 'audit-4', 1, 'audit-8', 1, 0))), 0) as tg_audit_reject_num,\n" +
                "    nvl(sum(decode(t1.operator_branch_no, '3', 0, decode(t1.task_type ||'-'||t1.task_status, 'review-4', 1, 0))), 0) as tg_review_reject_num,\n" +
                "    nvl(sum(decode(t1.operator_branch_no, '3', decode(t1.task_type, 'audit', 1, 0), 0)), 0) as zb_audit_total_num,\n" +
                "    nvl(sum(decode(t1.operator_branch_no, '3', decode(t1.task_type, 'review', 1, 0), 0)), 0) as zb_review_total_num,\n" +
                "    nvl(sum(decode(t1.operator_branch_no, '3', 0, decode(t1.task_type, 'audit', 1, 0))), 0) as tg_audit_total_num,\n" +
                "    nvl(sum(decode(t1.operator_branch_no, '3', 0, decode(t1.task_type, 'review', 1, 0))), 0) as tg_review_total_num\n" +
                "from crh_ac.flowtaskrecorddetails t1\n" +
                "where t1.invalid_flag = '0' \n" +
                "and   t1.operator_no != ' '\n" +
                "and   trunc(t1.finish_datetime) = trunc(sysdate-1)";
        Object result;
        try {
            log.info("执行sql：" + sql);
            //result = generalXmlMapper.query(sql);
            result = MapUtil.builder().put(Fields.SIZE, generalXmlMapper.insert(sql)).map();
        } catch (Exception e) {
            throw new BizException(MonitorErrorCode.SQL_EXECUTE_IS_ERROR, "sql执行错误", e);
        }
        //4.返回结果
        log.info("result：" + result);
    }

    public void calculateQueueLevelSpeed() {
        List result = (List) getQueueLevelSpeedRecords();
        String queue_level0_num = "0";
        String queue_level1_num = "0";
        String queue_level2_num = "0";
        String queue_level3_num = "0";
        String queue_level4_num = "0";
        if (CollectionUtils.isNotEmpty(result)) {
            HashMap<String, Object> map = (HashMap<String, Object>) result.get(0);
            queue_level0_num = map.get("QUEUE_LEVEL0_NUM").toString();
            queue_level1_num = map.get("QUEUE_LEVEL1_NUM").toString();
            queue_level2_num = map.get("QUEUE_LEVEL2_NUM").toString();
            queue_level3_num = map.get("QUEUE_LEVEL3_NUM").toString();
            queue_level4_num = map.get("QUEUE_LEVEL4_NUM").toString();
        }
        QueueLevelSpeed queueLevelSpeed = new QueueLevelSpeed();
        queueLevelSpeed.setCreate_datetime(new Date());
        queueLevelSpeed.setQueue_level0_num(queue_level0_num);
        queueLevelSpeed.setQueue_level1_num(queue_level1_num);
        queueLevelSpeed.setQueue_level2_num(queue_level2_num);
        queueLevelSpeed.setQueue_level3_num(queue_level3_num);
        queueLevelSpeed.setQueue_level4_num(queue_level4_num);
        queueLevelSpeedMapper.insert(queueLevelSpeed);
    }

    private Object getQueueLevelSpeedRecords() {
        String sql = "select\n" +
                "    nvl(sum(decode(queue_level, 1, 1, 0)), 0) as queue_level0_num,\n" +
                "    nvl(sum(decode(queue_level, 2, 1, 0)), 0) as queue_level1_num,\n" +
                "    nvl(sum(decode(queue_level, 3, 1, 0)), 0) as queue_level2_num,\n" +
                "    nvl(sum(decode(queue_level, 4, 1, 0)), 0) as queue_level3_num,\n" +
                "    nvl(sum(decode(queue_level, 5, 1, 0)), 0) as queue_level4_num\n" +
                "from crh_ads.dispatchtask\n" +
                "where subsys_id = '24'\n" +
                "and   dispatch_datetime > (sysdate -  (select property_value from crh_monitor.syspropertyconfig where property_key = 'monitor.ac.statistics.unit.time') /1440)\n";
        Object result;
        try {
            log.info("执行sql：" + sql);
            result = generalXmlMapper.query(sql);
        } catch (Exception e) {
            throw new BizException(MonitorErrorCode.SQL_EXECUTE_IS_ERROR, "sql执行错误", e);
        }
        //4.返回结果
        return result;
    }

    public void calculateHourAvgNoHandupTime() {
        String sql = "insert into crh_monitor.houravgnohanduptime\n" +
                "select\n" +
                "    sysdate,\n" +
                "    decode(l.finish_no_handup_sum , 0 , 0 , round(l.no_handup_deal_time/l.finish_no_handup_sum)) as avg_no_handup_time\n" +
                "from (\n" +
                "    select\n" +
                "        nvl(sum(decode(e.flag, '2', 1, '4', 1, 0)), 0) as finish_no_handup_sum,\n" +
                "        nvl(round(sum(decode(e.flag, '2', e.first_deal_datetime - e.request_datetime, '4', e.first_deal_datetime - e.request_datetime, 0)) * 24 * 60 *60), 0) as no_handup_deal_time\n" +
                "    from (\n" +
                "        select \n" +
                "            a.request_datetime,\n" +
                "            a.first_deal_datetime,\n" +
                "            decode(audit_finish_datetime, null, a.pause_flag, decode(a.review_finish_datetime, null, decode(a.pause_flag, '0', '2', '1', '3', '6'), decode(a.pause_flag, '0', '4', '1', '5', '6'))) as flag\n" +
                "        from crh_ac.businprocessrequestaudittrail a\n" +
                "        where a.user_up_branch_no != '3'\n" +
                "        and   a.user_up_branch_no != ' '\n" +
                "        and   a.activity_no not in ('201809_GXMSY', '201906_JXYMT', '201905_NXYXB')\n" +
                "        and   a.request_datetime >= sysdate - 1/24\n" +
                "    ) e\n" +
                ") l";
        Object result;
        try {
            log.info("执行sql：" + sql);
            //result = generalXmlMapper.query(sql);
            result = MapUtil.builder().put(Fields.SIZE, generalXmlMapper.insert(sql)).map();
        } catch (Exception e) {
            throw new BizException(MonitorErrorCode.SQL_EXECUTE_IS_ERROR, "sql执行错误", e);
        }
        //4.返回结果
        log.info("result：" + result);
    }

    public void calculateDailyAvgNoHandupTime() {
        String sql = "insert into crh_monitor.dailyavgnohanduptime\n" +
                "select\n" +
                "    sysdate - 1 as query_date,\n" +
                "    decode(l.finish_no_handup_sum , 0 , 0 , round(l.no_handup_deal_time/l.finish_no_handup_sum)) as avg_no_handup_time\n" +
                "from (\n" +
                "    select\n" +
                "        nvl(sum(decode(e.flag, '2', 1, '4', 1, 0)), 0) as finish_no_handup_sum,\n" +
                "        nvl(round(sum(decode(e.flag, '2', e.first_deal_datetime - e.request_datetime, '4', e.first_deal_datetime - e.request_datetime, 0)) * 24 * 60 *60), 0) as no_handup_deal_time\n" +
                "    from (\n" +
                "        select \n" +
                "            a.request_datetime,\n" +
                "            a.first_deal_datetime,\n" +
                "            decode(audit_finish_datetime, null, a.pause_flag, decode(a.review_finish_datetime, null, decode(a.pause_flag, '0', '2', '1', '3', '6'), decode(a.pause_flag, '0', '4', '1', '5', '6'))) as flag\n" +
                "        from crh_ac.businprocessrequestaudittrail a\n" +
                "        where a.user_up_branch_no != '3'\n" +
                "        and   a.user_up_branch_no != ' '\n" +
                "        and   a.time_property = '1'\n" +
                "        and   a.activity_no not in ('201809_GXMSY', '201906_JXYMT', '201905_NXYXB')\n" +
                "        and   a.request_datetime >= trunc(sysdate) - 1\n" +
                "        and   a.request_datetime <= trunc(sysdate)\n" +
                "    ) e\n" +
                ") l";
        Object result;
        try {
            log.info("执行sql：" + sql);
            //result = generalXmlMapper.query(sql);
            result = MapUtil.builder().put(Fields.SIZE, generalXmlMapper.insert(sql)).map();
        } catch (Exception e) {
            throw new BizException(MonitorErrorCode.SQL_EXECUTE_IS_ERROR, "sql执行错误", e);
        }
        //4.返回结果
        log.info("result：" + result);
    }
}
