package com.cairh.cpe.monitor.worker.data.service.impl;

import com.cairh.cpe.monitor.worker.data.entity.OperatorInfo;
import com.cairh.cpe.monitor.worker.data.mapper.OperatorInfoMapper;
import com.cairh.cpe.monitor.worker.data.service.IOperatorInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * Description：查询用户信息服务实现
 * Author： slx
 * Date： 2024/4/24 下午4:05
 */
@Service
public class OperatorInfoServiceImpl implements IOperatorInfoService {

    @Autowired
    private OperatorInfoMapper operatorInfoMapper;

    @Override
    public List<OperatorInfo> selectOperatorInfoList() {
        List<OperatorInfo> operatorInfos = operatorInfoMapper.selectOperatorInfoList();
        if (!CollectionUtils.isEmpty(operatorInfos)) {
            return operatorInfos;
        }
        return Collections.emptyList();
    }
}
