package com.cairh.cpe.monitor.worker.data.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cairh.cpe.common.util.DateUtil;
import com.cairh.cpe.monitor.worker.constant.Constant;
import com.cairh.cpe.monitor.worker.data.entity.*;
import com.cairh.cpe.monitor.worker.data.mapper.PersonalOnDutyStatisticsMapper;
import com.cairh.cpe.monitor.worker.data.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


/**
 * Description：个人在岗统计服务实现
 * Author： slx
 * Date： 2024/4/18 下午4:56
 */
@Slf4j
@Service
public class PersonalOnDutyStatisticsServiceImpl extends ServiceImpl<PersonalOnDutyStatisticsMapper, PersonalOnDutyStatistics>
        implements IPersonalOnDutyStatisticsService {

    private static final Map<String, BranchInfo> allBranchInfoMap = new ConcurrentHashMap<>(128);
    private static final Map<String, String> allOperatorInfoMap = new ConcurrentHashMap<>(128);
    @Autowired
    private IHandupDetailsService handupDetailsService;
    @Autowired
    private IUserLoginJourService userLoginJourService;
    @Autowired
    private IBusinFlowTaskService businFlowTaskService;
    @Autowired
    private IBranchInfoService branchInfoService;
    @Autowired
    private IOperatorInfoService operatorInfoService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void savePersonalOnDutyStatistics(String date) {
        Date statisticsDate = getYesterday();
        if (StringUtils.isNotEmpty(date)) {
            try {
                statisticsDate = DateUtil.formatStrDateTime(date, "yyyy-MM-dd");
            } catch (Exception e) {
                log.error("[savePersonalOnDutyStatistics]日期格式错误，date={}", date);
            }
        }
        // 初始化branch
        init();
        List<BusinFlowTask> businFlowTasks = businFlowTaskService.selectUserLoginJourByDateAndOperatorNoList(statisticsDate);
        if (CollectionUtils.isEmpty(businFlowTasks)) {
            log.info("日期={}，任务为空！", statisticsDate);
            return;
        }
        // 当天在岗人员
        List<PersonInfo> personInfoList = getPersonal(businFlowTasks);
        // 操作员编号集合
        List<String> operatorNoList = personInfoList.stream().map(PersonInfo::getOperator_no).collect(Collectors.toList());

        // 获取挂起、回收、转交数量,表HandupDetails
        CompletableFuture<List<HandupDetails>> handupDetailsFuture = fetchHandupDetailsAsync(operatorNoList, statisticsDate);
        // 获取操作员当天最早下线时间、当天最晚下线时间、全天在线时长,表UserLoginJour
        CompletableFuture<List<UserLoginJourStatistics>> userLoginJourStatisticsFuture = fetchUserLoginJourStatisticsAsync(operatorNoList, statisticsDate);

        log.info("开始统计个人在岗信息[savePersonalOnDutyStatistics]，日期：{}", statisticsDate);
        try {
            CompletableFuture.allOf(handupDetailsFuture, userLoginJourStatisticsFuture).join();
            List<HandupDetails> handupDetailsList = handupDetailsFuture.getNow(null);
            List<UserLoginJourStatistics> userLoginJourStatistics = userLoginJourStatisticsFuture.getNow(null);

            Date finalStatisticsDate = statisticsDate;
            List<PersonalOnDutyStatistics> personalOnDutyStatisticsList = personInfoList.stream().map(personInfo -> {
                // 通过操作员获取当天第一笔处理时间、当天最后一笔处理时间
                Date firstDealTime = getFirstDealTime(personInfo, businFlowTasks);
                Date endDealTime = getEndDealTime(personInfo, businFlowTasks);
                // 参与审核的所有数据，统计该操作员的审核总时长
                int auditTimeSum = getAuditTimeSum(personInfo, businFlowTasks);

                UserLoginJourStatistics jourStatistics = userLoginJourStatistics
                        .stream()
                        .filter(userLoginJourStatistic -> personInfo.getOperator_no().equals(userLoginJourStatistic.getStaff_no()))
                        .findFirst().orElse(null);
                // 获取当天最早上线时间
                Date beginOnlineTime = jourStatistics != null ? jourStatistics.getBegin_online_time() : null;
                // 获取当天最晚下线时间
                Date endOnlineTime = jourStatistics != null ? jourStatistics.getEnd_online_time() : null;
                // 计算全天在线时长
                int onlineTimeSum = jourStatistics != null ? jourStatistics.getOnline_time_sum() : 0;

                HandupDetails statisticsSum = findHandupDetailsByOperatorNo(handupDetailsList, personInfo.getOperator_no());
                // 挂起
                int suspendStatisticsSum = statisticsSum != null ? statisticsSum.getSuspend_sum() : 0;
                // 回收
                int recoveryStatisticsSum = statisticsSum != null ? statisticsSum.getRecovery_sum() : 0;
                // 转交
                int transferStatisticsSum = statisticsSum != null ? statisticsSum.getTransfer_sum() : 0;

                return createPersonalOnDutyStatistics(personInfo, finalStatisticsDate, firstDealTime, endDealTime, auditTimeSum,
                        beginOnlineTime, endOnlineTime, onlineTimeSum, suspendStatisticsSum, recoveryStatisticsSum,
                        transferStatisticsSum);
            }).collect(Collectors.toList());
            // 指定日期需要先删后增，防止数据重复
            if (StringUtils.isNotEmpty(date)) {
                LambdaQueryWrapper<PersonalOnDutyStatistics> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.in(PersonalOnDutyStatistics::getOperator_no, operatorNoList)
                        .eq(PersonalOnDutyStatistics::getReport_date, DateUtil.convertUtilDateToLocalDate(statisticsDate));
                remove(queryWrapper);
            }
            saveBatch(personalOnDutyStatisticsList);
            log.info("结束统计个人在岗信息[savePersonalOnDutyStatistics]，日期：{}，数量：{}", statisticsDate, personalOnDutyStatisticsList.size());
        } catch (Exception e) {
            log.error("保存个人在岗统计信息失败！", e);
            throw new RuntimeException("保存个人在岗统计信息失败！", e);
        }
    }

    private CompletableFuture<List<HandupDetails>> fetchHandupDetailsAsync(List<String> operatorNoList, Date yesterday) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return handupDetailsService.selectHandupDetailsListByDateAndOperatorNoList(operatorNoList, yesterday);
            } catch (Exception e) {
                log.error("查询个人在岗统计信息（HandupDetails）失败！", e);
                throw new RuntimeException("查询个人在岗统计信息[HandupDetails]失败", e);
            }
        });
    }

    private CompletableFuture<List<UserLoginJourStatistics>> fetchUserLoginJourStatisticsAsync(List<String> operatorNoList, Date yesterday) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return userLoginJourService.selectUserLoginJourByDateAndOperatorNoList(operatorNoList, yesterday);
            } catch (Exception e) {
                log.error("查询个人在岗统计信息（UserLoginJourStatistics）失败！", e);
                throw new RuntimeException("查询个人在岗统计信息[UserLoginJourStatistics]失败", e);
            }
        });
    }

    private PersonalOnDutyStatistics createPersonalOnDutyStatistics(PersonInfo personInfo, Date reportDate, Date firstDealTime,
                                                                    Date endDealTime, int auditTimeSum, Date beginOnlineTime,
                                                                    Date endOnlineTime, int onlineTimeSum, int suspendStatisticsSum,
                                                                    int recoveryStatisticsSum, int transferStatisticsSum) {

        return new PersonalOnDutyStatistics()
                .setOperator_no(personInfo.getOperator_no())
                .setOperator_name(personInfo.getOperator_name())
                .setBranch_no(personInfo.getBranch_no())
                .setBranch_name(personInfo.getBranch_name())
                .setUp_branch_no(personInfo.getUp_branch_no())
                .setUp_branch_name(personInfo.getUp_branch_name())
                .setReport_date(DateUtil.convertUtilDateToLocalDate(reportDate))
                .setBegin_deal_time(firstDealTime)
                .setEnd_deal_time(endDealTime)
                .setSum_deal_time(auditTimeSum)
                .setBegin_online_time(beginOnlineTime)
                .setEnd_online_time(endOnlineTime)
                .setOnline_time_sum(onlineTimeSum)
                .setPause_sum(suspendStatisticsSum)
                .setRecycle_sum(recoveryStatisticsSum)
                .setDeliver_sum(transferStatisticsSum)
                .setCreate_datetime(new Date());
    }


    /**
     * 根据操作员操作的任务挂起、回收、转交数量统计对象
     *
     * @param handupDetailsList 任务记录数据
     * @param operatorNo        操作员
     * @return 对象
     */
    private HandupDetails findHandupDetailsByOperatorNo(List<HandupDetails> handupDetailsList, String operatorNo) {
        // 检查handupDetailsList是否为null
        if (CollectionUtils.isEmpty(handupDetailsList)) {
            log.error("handupDetailsList不能为空！");
            return null;
        }

        // 检查operatorNo是否为空
        if (StringUtils.isEmpty(operatorNo)) {
            log.error("operatorNo不能为空！");
            return null;
        }
        return handupDetailsList
                .stream()
                .filter(handupDetails -> StringUtils.equals(handupDetails.getOperator_no(), operatorNo))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取第一次处理时间
     *
     * @param personInfo     个人信息
     * @param businFlowTasks 任务信息
     * @return 第一次处理时间
     */
    private Date getFirstDealTime(PersonInfo personInfo, List<BusinFlowTask> businFlowTasks) {
        // 检查输入参数是否为null
        if (personInfo == null || businFlowTasks == null) {
            log.error("PersonInfo和businFlowTasks都不能为空！");
            return null;
        }
        return businFlowTasks
                .stream()
                .filter(businFlowTask -> StringUtils.equals(personInfo.getOperator_no(), businFlowTask.getOperator_no()))
                .min(Comparator.comparing(BusinFlowTask::getDeal_datetime))
                .map(BusinFlowTask::getDeal_datetime)
                .orElse(null);
    }

    /**
     * 获取最后处理时间
     *
     * @param personInfo     个人信息
     * @param businFlowTasks 任务信息
     * @return 最后处理时间
     */
    private Date getEndDealTime(PersonInfo personInfo, List<BusinFlowTask> businFlowTasks) {
        // 检查输入参数是否为null
        if (personInfo == null || businFlowTasks == null) {
            log.error("PersonInfo和businFlowTasks都不能为空！");
            return null;
        }
        return businFlowTasks
                .stream()
                .filter(businFlowTask -> StringUtils.equals(personInfo.getOperator_no(), businFlowTask.getOperator_no()))
                .max(Comparator.comparing(BusinFlowTask::getDeal_datetime))
                .map(BusinFlowTask::getDeal_datetime)
                .orElse(null);
    }

    /**
     * 计算审核总时长
     *
     * @param personInfo     个人信息
     * @param businFlowTasks 任务信息
     * @return 审核时长
     */
    private int getAuditTimeSum(PersonInfo personInfo, List<BusinFlowTask> businFlowTasks) {
        // 输入参数校验
        if (personInfo == null || businFlowTasks == null) {
            log.error("PersonInfo和businFlowTasks都不能为空！");
            return 0;
        }
        return businFlowTasks
                .stream()
                .filter(businFlowTask -> StringUtils.equals(personInfo.getOperator_no(), businFlowTask.getOperator_no()))
                .mapToInt(this::calculateAuditDuration)
                .sum();
    }

    private int calculateAuditDurationAsOperator(BusinFlowTask businFlowTask) {
        // 操作员的审核时长计算逻辑
        return calculateAuditDuration(businFlowTask);
    }

    /**
     * 计算审核时长
     *
     * @param businFlowTask 对象
     * @return 审核时长
     */
    private int calculateAuditDuration(BusinFlowTask businFlowTask) {
        LocalDateTime dealTime = Optional.ofNullable(businFlowTask.getDeal_datetime())
                .map(this::convertToLocalDateTime)
                .orElseGet(this::getCurrentDateTime);
        LocalDateTime finishTime = Optional.ofNullable(businFlowTask.getFinish_datetime())
                .map(this::convertToLocalDateTime)
                .orElseGet(this::getCurrentDateTime);
        return calculate(dealTime, finishTime);
    }

    private LocalDateTime convertToLocalDateTime(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    private LocalDateTime getCurrentDateTime() {
        return LocalDateTime.now();
    }

    /**
     * 计算时间差
     *
     * @param dealTime   处理时间
     * @param finishTime 完成时间
     * @return 时间差
     */
    public int calculate(LocalDateTime dealTime, LocalDateTime finishTime) {
        if (dealTime == null || finishTime == null) {
            log.error("传入的时间参数不能为空!");
            return 0;
        }
        if (finishTime.equals(dealTime)) {
            return 0;
        }
        // 确保结束时间晚于开始时间
        if (finishTime.isBefore(dealTime)) {
            log.error("结束时间={}必须晚于开始时间={}!", finishTime, dealTime);
            return 0;
        }

        // 转换为 ZonedDateTime 并指定时区，这里以系统默认时区为例
        ZoneId systemZoneId = ZoneId.systemDefault();
        ZonedDateTime dealTimeWithZone = dealTime.atZone(systemZoneId);
        ZonedDateTime finishTimeWithZone = finishTime.atZone(systemZoneId);

        // 计算时间差
        Duration duration = Duration.between(dealTimeWithZone, finishTimeWithZone);

        // 返回时间差，以秒为单位
        return (int) duration.getSeconds();
    }

    private Date getYesterday() {
        Calendar calendar = Calendar.getInstance();
        // 考虑时区
        calendar.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        return calendar.getTime();
    }

    /**
     * 获取员工信息集合
     *
     * @param businFlowTasks 任务数据集合
     * @return 获取员工信息集合
     */
    private List<PersonInfo> getPersonal(List<BusinFlowTask> businFlowTasks) {
        // 提取用户信息
        Map<String, PersonInfo> personInfoMap = new ConcurrentHashMap<>(128);
        businFlowTasks.forEach(businFlowTask -> {
            PersonInfo personInfo = new PersonInfo();
            String branchNo = allOperatorInfoMap.get(businFlowTask.getOperator_no());
            BranchInfo operatorBranchInfo = allBranchInfoMap.get(branchNo);
            personInfo.setBranch_no(branchNo)
                    .setBranch_name(getValue(operatorBranchInfo.getBranch_name()));
            if (StringUtils.equalsAny(operatorBranchInfo.getBranch_type(), Constant.LEVEL_HEADQUARTERS, Constant.LEVEL_SUBSIDIARY_COMPANY, Constant.LEVEL_BRANCH_OFFICE)) {
                personInfo.setUp_branch_no(branchNo);
                personInfo.setUp_branch_name(operatorBranchInfo.getBranch_name());
            } else {
                String upBranchNo = StringUtils.isNotBlank(operatorBranchInfo.getUp_branch_no()) ? operatorBranchInfo.getUp_branch_no() : branchNo;
                BranchInfo upOperatorBranchInfo = allBranchInfoMap.get(upBranchNo);

                personInfo.setUp_branch_no(upBranchNo)
                        .setUp_branch_name(getValue(upOperatorBranchInfo.getBranch_name()));
            }
            processOperatorInfo(personInfoMap, personInfo,
                    businFlowTask.getOperator_no(),
                    businFlowTask.getOperator_name());
        });
        return personInfoMap.values().stream().distinct().collect(Collectors.toList());
    }

    private String getValue(String value) {
        return StringUtils.isEmpty(value) ? StrUtil.SPACE : value;
    }

    private void processOperatorInfo(Map<String, PersonInfo> personInfoMap, PersonInfo personInfo,
                                     String operatorNo, String operatorName) {
        if (StringUtils.isNoneBlank(operatorNo) && !personInfoMap.containsKey(operatorNo)) {
            personInfo.setOperator_no(operatorNo)
                    .setOperator_name(operatorName);
            personInfoMap.put(operatorNo, personInfo);
        }
    }

    private void init() {
        // 初始化branch信息
        initBranchInfo();
        // 初始化operator信息
        initOperatorInfo();
    }

    private void initBranchInfo() {
        try {
            List<BranchInfo> branchInfoList = branchInfoService.selectBranchInfoList();
            if (CollectionUtils.isEmpty(branchInfoList)) {
                log.warn("查询allBranch为空！");
                return;
            }
            branchInfoList.forEach(branchInfo -> allBranchInfoMap.put(branchInfo.getBranch_no(), branchInfo));
        } catch (Exception e) {
            log.error("branch数据初始化异常!", e);
            throw new RuntimeException("branch数据初始化失败", e);
        }
    }

    private void initOperatorInfo() {
        try {
            List<OperatorInfo> operatorInfos = operatorInfoService.selectOperatorInfoList();
            if (CollectionUtils.isEmpty(operatorInfos)) {
                log.warn("查询operatorinfo为空！");
                return;
            }
            operatorInfos.forEach(operatorInfo -> {
                allOperatorInfoMap.put(operatorInfo.getStaff_no(), operatorInfo.getBranch_no());
            });
        } catch (Exception e) {
            log.error("operator数据初始化异常!", e);
            throw new RuntimeException("operator数据初始化失败", e);
        }
    }
}
