package com.cairh.cpe.monitor.worker.data.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.cairh.cpe.esb.base.rpc.IVBaseDictDubboService;
import com.cairh.cpe.esb.base.rpc.dto.req.VBaseDictQryRequest;
import com.cairh.cpe.esb.base.rpc.dto.resp.VBaseDictQryResponse;
import com.cairh.cpe.monitor.worker.constant.QueryStatisticsDataConstant;
import com.cairh.cpe.monitor.worker.data.dto.AiAuditRuleTopDto;
import com.cairh.cpe.monitor.worker.data.dto.AiAuditRuleTopRedisDto;
import com.cairh.cpe.monitor.worker.data.dto.RejectReasonTopDto;
import com.cairh.cpe.monitor.worker.data.dto.RejectReasonTopRedisDto;
import com.cairh.cpe.monitor.worker.data.mapper.QueryStatisticsDataMapper;
import com.cairh.cpe.monitor.worker.data.service.IQueryStatisticsDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Service
@Slf4j
public class QueryStatisticsDataServiceImpl implements IQueryStatisticsDataService {

    @Autowired
    private QueryStatisticsDataMapper queryStatisticsDataMapper;
    @Autowired
    private RedisTemplate<String,String> redisTemplate;
    @DubboReference
    private IVBaseDictDubboService baseDictDubboService;;


    @Override
    public void getRejectReasonTop() {
        List<RejectReasonTopDto> reasonToplist = queryStatisticsDataMapper.queryRejectReasonTop();
        if (CollectionUtils.isEmpty(reasonToplist)){
            log.info("未查询到见证复核驳回数据");
            redisTemplate.opsForValue().set(QueryStatisticsDataConstant.REDIS_REJECT_REASON_KEY,"",30, TimeUnit.MINUTES);
            return;
        }
        // 处理数据
        // 获取最大值
        int sum = reasonToplist.stream().mapToInt(RejectReasonTopDto::getCountNum).sum();
        // 按分组数量倒序获取前五条
        List<RejectReasonTopDto> reasonTopDtos = reasonToplist.stream().sorted((x, y) -> Integer.compare(y.getCountNum(), x.getCountNum())).limit(5).collect(Collectors.toList());
        // 获取占比并组装结果数据
        List<RejectReasonTopRedisDto> redisDtoList = new ArrayList<>();
        BigDecimal bigDecimal = new BigDecimal(String.valueOf(sum));
        reasonTopDtos.forEach(x->{
            RejectReasonTopRedisDto redisDto = new RejectReasonTopRedisDto();
            BeanUtils.copyProperties(x,redisDto);
            BigDecimal radio = new BigDecimal(String.valueOf(x.getCountNum()*100)).divide(bigDecimal, 2, RoundingMode.HALF_UP);
            redisDto.setRadio(radio);
            redisDtoList.add(redisDto);
        });
        // 获取字典数据
        VBaseDictQryRequest vBaseDictQryRequest = new VBaseDictQryRequest();
        vBaseDictQryRequest.setDict_code(QueryStatisticsDataConstant.REJECT_REASON_DICT_CODE);
        List<VBaseDictQryResponse> vBaseDictQryResponses = baseDictDubboService.baseDataQryDict(vBaseDictQryRequest);
        //  将驳回原因编码与字典数据映射
        if (CollUtil.isNotEmpty(vBaseDictQryResponses)){
            Map<String, VBaseDictQryResponse> vBaseDictQryResponseMap = vBaseDictQryResponses.stream().collect(Collectors.toMap(VBaseDictQryResponse::getSub_code, res -> res));
            redisDtoList.forEach(x -> {
                VBaseDictQryResponse vBaseDictQryResponse = vBaseDictQryResponseMap.get(x.getReasonName());
                if (ObjectUtil.isNotEmpty(vBaseDictQryResponse)) {
                    x.setReasonName(vBaseDictQryResponse.getSub_name());
                }else {
                    log.info("驳回原因top5匹配字典时，{}未能匹配到字典映射的subName",x.getReasonName());
                }
            });
        }
        // 结果写入redis
        redisTemplate.opsForValue().set(QueryStatisticsDataConstant.REDIS_REJECT_REASON_KEY,JSONUtil.toJsonStr(redisDtoList),30, TimeUnit.MINUTES);
    }


    @Override
    public void getAiAuditRuleTop() {
        List<AiAuditRuleTopDto> aiAuditRuleToplist = queryStatisticsDataMapper.queryAiAuditRuleTop();
        // 过滤空数据
        List<AiAuditRuleTopDto> midData = aiAuditRuleToplist.stream().filter(x -> ObjectUtil.isNotEmpty(x.getRuleName())).collect(Collectors.toList());
        if (CollUtil.isEmpty(midData)){
            log.info("未查询到智能审核命中规则的数据");
            redisTemplate.opsForValue().set(QueryStatisticsDataConstant.AUDIT_RULE_TOP_KEY,"",30, TimeUnit.MINUTES);
            return;
        }
        List<AiAuditRuleTopRedisDto> aiAuditRuleTopList = new ArrayList<>();
        // 获取ruleType分组的分别总数量
        Map<String, List<AiAuditRuleTopDto>> ruleTypeCollect = midData.stream().collect(Collectors.groupingBy(AiAuditRuleTopDto::getRuleType));
        Map<String, Integer> map = new HashMap<>();
        ruleTypeCollect.forEach((key,value)->{
            map.put(key,value.stream().mapToInt(AiAuditRuleTopDto::getCountNum).sum());
        });
        // 处理数据
        Map<String, List<AiAuditRuleTopDto>> collect = midData.stream().collect(Collectors.groupingBy(AiAuditRuleTopDto::getRuleType));
        collect.forEach((key,value)->{
            // 获取ruleType分组数量最大的前五条数据
            List<AiAuditRuleTopDto> list = value.stream().sorted(Comparator.comparing(AiAuditRuleTopDto::getCountNum).reversed())
                    .limit(5).collect(Collectors.toList());
            // 获取比重与具体数量
            list.forEach(x->{
                BigDecimal radio = new BigDecimal(String.valueOf(x.getCountNum()*100)).divide(new BigDecimal(String.valueOf(map.get(key))), 2, RoundingMode.HALF_UP);
                AiAuditRuleTopRedisDto aiAuditRuleTopRedisDto = new AiAuditRuleTopRedisDto();
                aiAuditRuleTopRedisDto.setRuleType(key);
                aiAuditRuleTopRedisDto.setRuleName(x.getRuleName());
                aiAuditRuleTopRedisDto.setCountNum(x.getCountNum());
                aiAuditRuleTopRedisDto.setRadio(radio);
                aiAuditRuleTopList.add(aiAuditRuleTopRedisDto);
            });
        });
        // 结果写入redis
        redisTemplate.opsForValue().set(QueryStatisticsDataConstant.AUDIT_RULE_TOP_KEY,JSONUtil.toJsonStr(aiAuditRuleTopList),30, TimeUnit.MINUTES);
    }

}
