package com.cairh.cpe.monitor.worker.data.service.impl;

import com.cairh.cpe.monitor.worker.constant.Constant;
import com.cairh.cpe.monitor.worker.data.entity.UserLoginJour;
import com.cairh.cpe.monitor.worker.data.entity.UserLoginJourStatistics;
import com.cairh.cpe.monitor.worker.data.mapper.UserLoginJourMapper;
import com.cairh.cpe.monitor.worker.data.service.IUserLoginJourService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Description：
 * Author： slx
 * Date： 2024/4/19 下午3:50
 */
@Slf4j
@Service
public class UserLoginJourServiceImpl implements IUserLoginJourService {

    @Autowired
    private UserLoginJourMapper mapper;

    @Override
    public List<UserLoginJourStatistics> selectUserLoginJourByDateAndOperatorNoList(List<String> operatorNoList, Date date) {
        String dateTime = DateFormatUtils.format(date, "yyyy-MM-dd");
        List<UserLoginJour> loginJourList = mapper.selectUserLoginJourByDateAndOperatorNoList(operatorNoList, dateTime);
        if (CollectionUtils.isEmpty(loginJourList)) {
            log.info("查询[selectUserLoginJourByDateAndOperatorNoList]用户登录信息为空！");
            return Collections.emptyList();
        }
        return operatorNoList.stream().map(operatorNo -> {
            UserLoginJourStatistics statistics = new UserLoginJourStatistics();
            statistics.setStaff_no(operatorNo);
            List<UserLoginJour> collect = loginJourList
                    .stream()
                    .filter(loginJour -> operatorNo.equals(loginJour.getStaff_no()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(collect)) {
                log.warn("用户staff_no={}, 在线数据为空！", operatorNo);
                return statistics;
            }

            int onlineTimeSum = 0;
            Date lastLoginTime = null;

            // 如果第一条是登出，设置当天00:00:00为登录时间
            if (Constant.LOGOUT_SYS_JOUR_CODE.equals(collect.get(0).getLogin_type())) {
                Calendar cal = Calendar.getInstance();
                cal.setTime(collect.get(0).getCreate_datetime());
                cal.set(Calendar.HOUR_OF_DAY, 0);
                cal.set(Calendar.MINUTE, 0);
                cal.set(Calendar.SECOND, 0);
                cal.set(Calendar.MILLISECOND, 0);
                lastLoginTime = cal.getTime();
                statistics.setBegin_online_time(lastLoginTime);
            }

            for (int i = 0; i < collect.size(); i++) {
                UserLoginJour current = collect.get(i);
                Date currentTime = current.getCreate_datetime();

                if (Constant.LOGIN_SYS_JOUR_CODE.equals(current.getLogin_type())) {
                    // 设置最早登录时间
                    if (Objects.isNull(statistics.getBegin_online_time())) {
                        statistics.setBegin_online_time(currentTime);
                    }

                    // 如果上一次有登录时间，说明遇到了连续登录
                    // 将当前登录时间作为上一次登录的登出时间来计算一段在线时长
                    if (lastLoginTime != null && lastLoginTime.before(currentTime)) {
                        onlineTimeSum += (int) ((currentTime.getTime() - lastLoginTime.getTime()) / 1000);
                    }

                    lastLoginTime = currentTime;
                } else if (Constant.LOGOUT_SYS_JOUR_CODE.equals(current.getLogin_type())) {
                    statistics.setEnd_online_time(currentTime);
                    // 如果有上一次登录时间，计算在线时长
                    if (lastLoginTime != null && lastLoginTime.before(currentTime)) {
                        onlineTimeSum += (int) ((currentTime.getTime() - lastLoginTime.getTime()) / 1000);
                    }

                    // 如果下一条记录是登出，则当前登出时间作为一次登录时间
                    if (i + 1 < collect.size() &&
                            Constant.LOGOUT_SYS_JOUR_CODE.equals(collect.get(i + 1).getLogin_type())) {
                        lastLoginTime = currentTime;
                    } else {
                        lastLoginTime = null;
                    }
                }
            }

            // 如果最后一条是登录，设置当天23:59:59为登出时间
            if (Constant.LOGIN_SYS_JOUR_CODE.equals(collect.get(collect.size() - 1).getLogin_type())) {
                Calendar cal = Calendar.getInstance();
                cal.setTime(collect.get(collect.size() - 1).getCreate_datetime());
                cal.set(Calendar.HOUR_OF_DAY, 23);
                cal.set(Calendar.MINUTE, 59);
                cal.set(Calendar.SECOND, 59);
                cal.set(Calendar.MILLISECOND, 999);
                Date endTime = cal.getTime();
                statistics.setEnd_online_time(endTime);
                // 计算最后一段在线时长
                if (lastLoginTime != null && lastLoginTime.before(endTime)) {
                    onlineTimeSum += (int) ((endTime.getTime() - lastLoginTime.getTime()) / 1000);
                }
            }

            statistics.setOnline_time_sum(onlineTimeSum);
            return statistics;
        }).collect(Collectors.toList());
    }
}
