package com.cairh.cpe.monitor.worker.schedule;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.StringUtils;
import com.cairh.cpe.common.constant.Constant;
import com.cairh.cpe.core.autoconfiure.env.CompositePropertySources;
import com.cairh.cpe.monitor.worker.data.service.*;
import com.cairh.cpe.monitor.worker.data.service.impl.MonitorTaskSupportService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class MonitorScheduledTasks {

    @Autowired
    protected CompositePropertySources compositePropertySources;
    @Autowired
    private MonitorTaskSupportService monitorTaskSupportService;
    @Autowired
    private IPersonalOnDutyStatisticsService personalOnDutyStatisticsService;
    @Autowired
    private IBusinReviewCompleteService businReviewCompleteService;
    @Autowired
    private IQueryStatisticsDataService queryStatisticsDataService;
    @Autowired
    private IFollowupOperatorStatisticsService followupOperatorStatisticsService;
    @Autowired
    private ICompanyOperatorStatisticsService companyOperatorStatisticsService;
    @Autowired
    private IAuditAlarmSmsNoticeService auditAlarmSmsNoticeService;

    /**
     * 统计见证系统小时工作量，每小时执行一次
     */
    @XxlJob("calculateHourWorkloadJobHandler")
    public void calculateHourWorkloadJobHandler() {
        monitorTaskSupportService.calculateHourWorkload();
    }

    /**
     * 统计见证系统每日工作量，每日凌晨1点统计上一日的数据
     */
    @XxlJob("calculateDailyWorkloadJobHandler")
    public void calculateDailyWorkloadJobHandler() {
        monitorTaskSupportService.calculateDailyWorkload();
    }

    /**
     * 统计见证系统每日驳回率，每日凌晨1点统计上一日的数据
     */
    @XxlJob("calculateDailyRejectJobHandler")
    public void calculateDailyRejectJobHandler() {
        monitorTaskSupportService.calculateDailyReject();
    }

    /**
     * 统计派单队列流速，每小时执行一次
     */
    @XxlJob("calculateQueueLevelSpeedJobHandler")
    public void calculateQueueLevelSpeedJobHandler() {
        monitorTaskSupportService.calculateQueueLevelSpeed();
    }

    /**
     * 统计在岗个人信息，每日凌晨2点统计上一日的数据
     */
    @XxlJob("personalOnDutyStatisticsJobHandler")
    public void personalOnDutyStatisticsJobHandler() {
        personalOnDutyStatisticsService.savePersonalOnDutyStatistics("");
    }

    /**
     * 统计在岗个人信息-指定日期date
     */
    @XxlJob("personalOnDutyStatisticsByDateJob")
    public void personalOnDutyStatisticsByDateJobHandler() {
        // 获取参数
        String date = XxlJobHelper.getJobParam();
        personalOnDutyStatisticsService.savePersonalOnDutyStatistics(date);
    }

    /**
     * 统计见证系统小时未暂存数据响应平均时长，每小时执行一次
     */
    @XxlJob("calculateHourAvgNoHandupTimeJobHandler")
    public void calculateHourAvgNoHandupTimeJobHandler() {
        monitorTaskSupportService.calculateHourAvgNoHandupTime();
    }

    /**
     * 统计见证系统每日未暂存数据响应平均时长，每日凌晨1点统计上一日的数据
     */
    @XxlJob("calculateDailyAvgNoHandupTimeJobHandler")
    public void calculateDailyAvgNoHandupTimeJobHandler() {
        monitorTaskSupportService.calculateDailyAvgNoHandupTime();
    }

    /**
     * 统计审核通过业务量报表，每日凌晨2点统计上一日的数据
     */
    @XxlJob("businReviewCompleteJobHandler")
    public void businReviewCompleteJobHandler() {
        // 获取参数
        String date = XxlJobHelper.getJobParam();
        log.info("businReviewCompleteJobHandler入参 date={}", JSON.toJSONString(date));
        businReviewCompleteService.saveBusinReviewComplete(date);
    }


    /**
     * 统计驳回原因top5
     */
    @XxlJob("rejectReasonTopJobHandler")
    public void rejectReasonTop5() {
        queryStatisticsDataService.getRejectReasonTop();
    }

    /**
     * 智能审核规则命中top5
     */
    @XxlJob("auditRuleTopJobHandler")
    public void auditRuleTop5() {
        queryStatisticsDataService.getAiAuditRuleTop();
    }

    /**
     * 坐席每日在岗统计
     * 每日凌晨2点统计上一日的数据
     * 参数：date，格式：2024-01-01,2024-01-02
     */
    @XxlJob("operatorDailyStatisticsJobHandler")
    public void operatorDailyStatistics() {
        log.info("operatorDailyStatisticsJobHandler start");
        // 获取参数
        String date = XxlJobHelper.getJobParam();
        List<String> dates = new ArrayList<>();
        if (StrUtil.isNotBlank(date)) {
            dates = CollUtil.toList(date.split(","));
        }
        followupOperatorStatisticsService.operatorDailyStatistics(dates);
        log.info("operatorDailyStatisticsJobHandler  end");
    }

    /**
     * 统计分公司在岗坐席，每日凌晨2点统计
     */
    @XxlJob("companyOperatorStatisticsJobHandler")
    public void companyOperatorStatisticsJobHandler() {
        companyOperatorStatisticsService.saveCompanyOperatorStatistics();
    }

    /**
     * 监控告警短信通知，每分钟执行一次
     */
    @XxlJob("auditAlarmSmsNoticeJobHandler")
    public void auditAlarmSmsNoticeJobHandler() {
        // 监控告警通知开关
        boolean smsNoticeSwitch = StringUtils.equals("1", compositePropertySources.getProperty(Constant.MONITOR_ALARM_SMS_NOTICE_SWITCH, "0"));
        if (!smsNoticeSwitch) {
            log.info("监控告警通知开关未开启，不执行短信通知任务");
            return;
        }
        auditAlarmSmsNoticeService.pendingTaskAlarmNotice();
        auditAlarmSmsNoticeService.avgResponseAlarmNotice();
        auditAlarmSmsNoticeService.newTaskAlarmNotice();
        auditAlarmSmsNoticeService.waitTaskAlarmNotice();
        auditAlarmSmsNoticeService.failedTaskAlarmNotice();
        auditAlarmSmsNoticeService.operatorIdleAlarmNotice();
        auditAlarmSmsNoticeService.autoRejectTaskAlarmNotice();
    }
}
