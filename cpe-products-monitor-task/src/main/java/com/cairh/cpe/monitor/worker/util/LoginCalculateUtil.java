package com.cairh.cpe.monitor.worker.util;

import com.cairh.cpe.monitor.worker.constant.Constant;
import com.cairh.cpe.monitor.worker.data.entity.FollowupOnlineRecord;

import java.util.*;

/**
 * 登录计算工具
 */
public class LoginCalculateUtil {
    /**
     * 计算每个操作员的全天在线时长
     *
     * @param records 在线记录列表
     * @return 操作员编号到在线时长（秒）的映射
     */
    public static Map<String, Integer> calculateTotalOnlineTime(List<FollowupOnlineRecord> records) {
        // 用于存储每个操作员的在线时长
        Map<String, Integer> totalOnlineTimeMap = new HashMap<>();
        // 用于存储每个操作员的最新签入时间
        Map<String, Date> lastLoginTimeMap = new HashMap<>();
        // 对记录按操作员编号和操作时间排序
        records.sort(Comparator.comparing(FollowupOnlineRecord::getOperator_no)
                .thenComparing(FollowupOnlineRecord::getCreate_datetime));

        for (FollowupOnlineRecord record : records) {
            String operatorNo = record.getOperator_no();
            if (Objects.equals(record.getStatus(), Constant.ONLINE_RECORD_LOGIN)) { // 签入
                lastLoginTimeMap.put(operatorNo, record.getCreate_datetime());
            } else if (Objects.equals(record.getStatus(), Constant.ONLINE_RECORD_LOGOUT)) { // 签出
                Date lastLoginTime = lastLoginTimeMap.get(operatorNo);
                if (lastLoginTime != null) {
                    Integer onlineTime = (int) ((record.getCreate_datetime().getTime() - lastLoginTime.getTime()) / 1000);
                    totalOnlineTimeMap.put(operatorNo, totalOnlineTimeMap.getOrDefault(operatorNo, 0) + onlineTime);
                    lastLoginTimeMap.remove(operatorNo);
                }
            }
        }
        return totalOnlineTimeMap;
    }


}
