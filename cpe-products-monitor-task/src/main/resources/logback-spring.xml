<?xml version="1.0" encoding="UTF-8"?>
<!-- Copyright 2010-2011 The myBatis Team Licensed under the Apache License, 
	Version 2.0 (the "License"); you may not use this file except in compliance 
	with the License. You may obtain a copy of the License at http://www.apache.org/licenses/LICENSE-2.0 
	Unless required by applicable law or agreed to in writing, software distributed 
	under the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES 
	OR CONDITIONS OF ANY KIND, either express or implied. See the License for 
	the specific language governing permissions and limitations under the License. -->
<!-- Level: TRACE、DEBUG、INFO、WARN 和 ERROR -->
<configuration debug="false" scan="true" scanPeriod="60 seconds">
	<include resource="org/springframework/boot/logging/logback/defaults.xml"/>
	<property name="log.default_pattern"
			  value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5p [%X{_mdc_server_name}][%X{_mdc_webapp_name}][%X{_mdc_device_id}][%X{_mdc_trace_id}][%X{_mdc_busi_id}][%thread] : %m %X{_mdc_param} - [%logger{60}.%method:%line] %X{req.userAgent} %X{_mdc_crh_user} %rEx{full}%n"/>
	<property name="log.default_charset" value="utf-8"/>
	<property name="log.home" value="./logs"/>

	<appender name="console"
			  class="ch.qos.logback.core.ConsoleAppender">
		<encoder
				class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>${log.default_pattern}</pattern>
		</encoder>
	</appender>

	<appender name="file"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${log.home}/cpe-products-monitor-task.log</file>
		<rollingPolicy
			class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<fileNamePattern>${log.home}/%d{yyyy-MM-dd}/cpe-products-monitor-task-%d{yyyy-MM-dd}.%i.zip</fileNamePattern>
			<!-- 文件达到100M压缩一个ZIP -->
			<maxFileSize>100MB</maxFileSize>
			<!-- 保留1天的历史日志文件(过期自动删除) -->
			<maxHistory>60</maxHistory>
			<!-- 当产生的压缩文件累计达到100GB时开始滚动记录(自动删除早于100GB之前的历史日志文件) -->
			<!-- <totalSizeCap>100GB</totalSizeCap> -->
		</rollingPolicy>
		<encoder
			class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>${log.default_pattern}</pattern>
		</encoder>
	</appender>

	<!-- cairh -->
	<logger name="com.cairh.cpe" level="info" />
	<!-- netflix eureka -->
	<logger name="com.netflix.discovery" level="error" />
	<logger name="com.netflix.eureka.registry" level="error" />
	<!--spring	-->
	<logger name="org.springframework.boot.autoconfigure" level="error" />

	<!-- myibatis -->
	<logger name="org.apache.ibatis" level="info" />
	<logger name="java.sql.Connection" level="info" />
	<logger name="java.sql.Statement" level="info" />
	<logger name="java.sql.PreparedStatement" level="info" />

	<!-- hibernate -->
	<logger name="org.hibernate.type.descriptor.sql.BasicBinder" level="trace" />
	<logger name="org.hibernate.type.descriptor.sql.BasicExtractor" level="debug" />
	<logger name="org.hibernate.SQL" level="debug" />
	<logger name="org.hibernate.engine.QueryParameters" level="debug" />
	<logger name="org.hibernate.engine.query.HQLQueryPlan" level="debug" />


	<root level="info">
		<appender-ref ref="console" />
		<appender-ref ref="file" />
	</root>

</configuration>