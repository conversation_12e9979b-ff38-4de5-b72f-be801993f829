<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cairh.cpe.monitor.worker.data.mapper.BusinFlowTaskMapper">

    <select id="selectBusinFlowTaskByDateTime"
            resultType="com.cairh.cpe.monitor.worker.data.entity.BusinFlowTask">
        select
        <include refid="field"/>
        from crh_ac.businflowtask t
        left join crh_ac.businflowrequest r
        on t.request_no = r.request_no
        where trunc(t.finish_datetime) = to_date(#{dateTime}, 'yyyy-mm-dd');
    </select>

    <sql id="field">
        t.request_no,
        t.task_id,
        t.serial_id,
        t.task_status,
        t.create_datetime,
        t.deal_datetime,
        t.finish_datetime,
        t.task_type,
        t.operator_no,
        t.operator_name,
        t.op_content,
        r.branch_no
    </sql>
</mapper>
