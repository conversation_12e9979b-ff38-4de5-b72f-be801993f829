<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cairh.cpe.monitor.worker.data.mapper.BusinProcessRequestAuditTrailMapper">

    <select id="countDayByDateTime"
            resultType="com.cairh.cpe.monitor.worker.data.entity.BusinProcessRequestAuditTrailStatistics">
        WITH DateRanges AS (SELECT trunc(to_date(#{dateTime}, 'yyyy-mm-dd hh24:mi:ss')) AS start_of_day,
                                   trunc(to_date(#{dateTime}, 'yyyy-mm-dd hh24:mi:ss')) + INTERVAL '1' DAY -
                                   INTERVAL '1' SECOND                                  AS end_of_day
                            FROM dual),
             UnionAllData AS (SELECT b.user_branch_no,
                                     b.user_branch_name,
                                     b.user_up_branch_no,
                                     b.user_up_branch_name,
                                     b.busin_type
                              FROM CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL b,
                                   DateRanges
                              WHERE b.review_finish_datetime <![CDATA[>=]]> start_of_day
                                AND b.review_finish_datetime <![CDATA[<=]]> end_of_day
                                AND b.request_status = 'review-3'
                              UNION ALL
                              SELECT b.user_branch_no,
                                     b.user_branch_name,
                                     b.user_up_branch_no,
                                     b.user_up_branch_name,
                                     b.busin_type
                              FROM CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL b,
                                   DateRanges
                              WHERE b.sec_rv_finish_datetime <![CDATA[>=]]> start_of_day
                                AND b.sec_rv_finish_datetime <![CDATA[<=]]> end_of_day
                                AND b.request_status = 'secondary_review-3')
        SELECT COUNT(*) as branch_num,
               u.user_branch_no,
               u.user_branch_name,
               u.user_up_branch_no,
               u.user_up_branch_name,
               u.busin_type
        FROM UnionAllData u
        GROUP BY u.user_branch_no,
                 u.user_branch_name,
                 u.user_up_branch_no,
                 u.user_up_branch_name,
                 u.busin_type;
    </select>

    <select id="countMonthByDateTime"
            resultType="com.cairh.cpe.monitor.worker.data.entity.BusinProcessRequestAuditTrailStatistics">
        WITH DateRanges AS (SELECT trunc(to_date(#{dateTime}, 'yyyy-mm-dd hh24:mi:ss'), 'MM') AS start_of_month,
                                   trunc(to_date(#{dateTime}, 'yyyy-mm-dd hh24:mi:ss')) + INTERVAL '1' DAY -
                                   INTERVAL '1' SECOND                                        AS end_of_month
                            FROM dual),
             UnionAllData AS (SELECT b.user_branch_no,
                                     b.user_branch_name,
                                     b.user_up_branch_no,
                                     b.user_up_branch_name,
                                     b.busin_type
                              FROM CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL b,
                                   DateRanges
                              WHERE b.review_finish_datetime <![CDATA[>=]]> start_of_month
                                AND b.review_finish_datetime <![CDATA[<=]]> end_of_month
                                AND b.request_status = 'review-3'
                              UNION ALL
                              SELECT b.user_branch_no,
                                     b.user_branch_name,
                                     b.user_up_branch_no,
                                     b.user_up_branch_name,
                                     b.busin_type
                              FROM CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL b,
                                   DateRanges
                              WHERE b.sec_rv_finish_datetime <![CDATA[>=]]> start_of_month
                                AND b.sec_rv_finish_datetime <![CDATA[<=]]> end_of_month
                                AND b.request_status = 'secondary_review-3')
        SELECT COUNT(*) as branch_num,
               u.user_branch_no,
               u.user_branch_name,
               u.user_up_branch_no,
               u.user_up_branch_name,
               u.busin_type
        FROM UnionAllData u
        GROUP BY u.user_branch_no,
                 u.user_branch_name,
                 u.user_up_branch_no,
                 u.user_up_branch_name,
                 u.busin_type;
    </select>

    <select id="countYearByDateTime"
            resultType="com.cairh.cpe.monitor.worker.data.entity.BusinProcessRequestAuditTrailStatistics">
        WITH DateRanges AS (SELECT trunc(to_date(#{dateTime}, 'yyyy-mm-dd hh24:mi:ss'), 'YYYY') AS start_of_year,
                                   trunc(to_date(#{dateTime}, 'yyyy-mm-dd hh24:mi:ss')) + INTERVAL '1' DAY -
                                   INTERVAL '1' SECOND                                          AS end_of_year
                            FROM dual),
             UnionAllData AS (SELECT b.user_branch_no,
                                     b.user_branch_name,
                                     b.user_up_branch_no,
                                     b.user_up_branch_name,
                                     b.busin_type
                              FROM CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL b,
                                   DateRanges
                              WHERE b.review_finish_datetime <![CDATA[>=]]> start_of_year
                                AND b.review_finish_datetime <![CDATA[<=]]> end_of_year
                                AND b.request_status = 'review-3'
                              UNION ALL
                              SELECT b.user_branch_no,
                                     b.user_branch_name,
                                     b.user_up_branch_no,
                                     b.user_up_branch_name,
                                     b.busin_type
                              FROM CRH_AC.BUSINPROCESSREQUESTAUDITTRAIL b,
                                   DateRanges
                              WHERE b.sec_rv_finish_datetime <![CDATA[>=]]> start_of_year
                                AND b.sec_rv_finish_datetime <![CDATA[<=]]> end_of_year
                                AND b.request_status = 'secondary_review-3')
        SELECT COUNT(*) as branch_num,
               u.user_branch_no,
               u.user_branch_name,
               u.user_up_branch_no,
               u.user_up_branch_name,
               u.busin_type
        FROM UnionAllData u
        GROUP BY u.user_branch_no,
                 u.user_branch_name,
                 u.user_up_branch_no,
                 u.user_up_branch_name,
                 u.busin_type
    </select>
</mapper>
