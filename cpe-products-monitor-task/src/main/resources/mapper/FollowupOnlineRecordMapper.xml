<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cairh.cpe.monitor.worker.data.mapper.FollowupOnlineRecordMapper">
    <select id="selectByOperatorNoAndCreateDatetime"
            resultType="com.cairh.cpe.monitor.worker.data.entity.FollowupOnlineRecord">
        select *
        from CRH_FUS.FOLLOWUPONLINERECORD f
        where CREATE_DATETIME >= TO_DATE(#{startTime}, 'YYYY-MM-DD HH24:MI:SS')
          AND CREATE_DATETIME <![CDATA[ <= ]]> TO_DATE(#{endTime}, 'YYYY-MM-DD HH24:MI:SS')
          AND OPERATOR_NO = #{operatorNo}
    </select>
</mapper>
