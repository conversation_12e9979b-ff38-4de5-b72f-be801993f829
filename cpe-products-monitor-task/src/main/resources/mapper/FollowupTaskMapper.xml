<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cairh.cpe.monitor.worker.data.mapper.FollowupTaskMapper">

    <select id="statisticsOperatorTask"
            resultType="com.cairh.cpe.monitor.worker.data.entity.FollowupOperatorStatistics">
        SELECT ACTUAL_OPERATOR_NO                             AS operator_no,
               max(ACTUAL_OPERATOR_NAME)                      AS operator_name,
               min(DEAL_DATETIME)                             AS first_process_time,
               max(DEAL_DATETIME)                             AS last_process_time,
               NVL(sum((FINISH_DATETIME - DEAL_DATETIME) * 24 * 60 * 60), 0) AS total_process_dur_seconds,
               NVL(sum(CALL_NUM), 0)                                  AS total_call_num,
               TO_CHAR(max(CREATE_DATETIME), 'YYYY-MM-DD')    AS stat_date
        FROM CRH_FUS.FOLLOWUPTASK f
        WHERE CREATE_DATETIME >= TO_DATE(#{startTime}, 'YYYY-MM-DD HH24:MI:SS')
          AND CREATE_DATETIME <![CDATA[ <= ]]> TO_DATE(#{endTime}, 'YYYY-MM-DD HH24:MI:SS')
          AND ACTUAL_OPERATOR_NO!=' '
        GROUP BY ACTUAL_OPERATOR_NO;
    </select>
</mapper>
