<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cairh.cpe.monitor.worker.data.mapper.HandupDetailsMapper">

    <select id="selectHandupDetailsListByDateAndOperatorNoList"
            resultType="com.cairh.cpe.monitor.worker.data.entity.HandupDetails">
        SELECT
        h.operator_no,
        COUNT(CASE WHEN h.handup_type = '1' THEN 1 END) AS suspend_sum,
        COUNT(CASE WHEN h.handup_type = '2' THEN 1 END) AS recovery_sum,
        COUNT(CASE WHEN h.handup_type = '3' THEN 1 END) AS transfer_sum
        FROM
        crh_ac.handupdetails h
        WHERE
        h.operator_no in
        <foreach collection="operatorNoList" item="operatorNo" open="(" separator="," close=")">
            #{operatorNo}
        </foreach>
        and trunc(h.create_datetime) = to_date(#{dateTime}, 'yyyy-mm-dd')
        GROUP BY
        h.operator_no;
    </select>
</mapper>
