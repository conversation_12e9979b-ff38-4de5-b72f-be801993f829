<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cairh.cpe.monitor.worker.data.mapper.OperatorInfoMapper">

    <select id="selectOperatorInfoList"
            resultType="com.cairh.cpe.monitor.worker.data.entity.OperatorInfo">
        select o.staff_no,
               o.branch_no,
               o.en_roles,
               u.status
        from crh_user.operatorinfo o
        left join crh_user.users u on o.staff_no = u.staff_no;
    </select>
</mapper>
