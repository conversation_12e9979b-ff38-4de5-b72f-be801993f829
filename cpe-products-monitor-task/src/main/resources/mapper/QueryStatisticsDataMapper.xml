<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cairh.cpe.monitor.worker.data.mapper.QueryStatisticsDataMapper">



    <select id="queryRejectReasonTop" resultType="com.cairh.cpe.monitor.worker.data.dto.RejectReasonTopDto">
        <![CDATA[
        select
            t1.reason_name as reasonName,
            count(1) as countNum,
            sum(decode(t1.task_type, 'audit', 1, 0)) as auditNum,
            sum(decode(t1.task_type, 'audit', 0, 1)) as reviewNum
        from (
                 select
                     a.task_type,
                     a.create_datetime,
                     b.reason_name
                 from crh_ac.businflowtask a, crh_ac.taskreasonrecord b
                 where   a.finish_datetime >= trunc(sysdate)
                   and     a.finish_datetime < trunc(sysdate) +1
                   and 	a.task_status = '4'
                   and 	a.task_id = b.task_id
             ) t1
        group by t1.reason_name
        order by count(1) desc
        ]]>
    </select>
    <select id="queryAiAuditRuleTop" resultType="com.cairh.cpe.monitor.worker.data.dto.AiAuditRuleTopDto">
        <![CDATA[
        SELECT
            decode(item_identity, '9', 0, '3', 1, '4', '1', '5', 1, '2')  as ruleType,
            RULE_NAME as ruleName,
            COUNT(1) as countNum
        FROM  CRH_AIAUDIT.AIAUDITBUSINRECORD
        where match_result ='0'
           and  end_datetime >= trunc(sysdate)
           and   end_datetime < trunc(sysdate) +1
        GROUP BY decode(item_identity, '9', 0, '3', 1, '4', '1', '5', 1, '2'),RULE_NAME
        ]]>
    </select>


</mapper>