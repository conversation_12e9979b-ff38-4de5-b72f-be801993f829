<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cairh.cpe.monitor.worker.data.mapper.UserLoginJourMapper">

    <select id="selectUserLoginJourByDateAndOperatorNoList"
            resultType="com.cairh.cpe.monitor.worker.data.entity.UserLoginJour">
        select trunc(b.create_datetime) as report_date,
        b.staff_no,
        b.create_datetime,
        b.login_type
        from crh_user.userloginjour b
        where trunc(b.create_datetime) = to_date(#{dateTime}, 'yyyy-mm-dd')
        and b.staff_no in
        <foreach collection="operatorNoList" item="operatorNo" open="(" separator="," close=")">
            #{operatorNo}
        </foreach>
        order by b.staff_no, b.create_datetime;
    </select>
</mapper>
