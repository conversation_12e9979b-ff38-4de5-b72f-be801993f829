import java.time.LocalDateTime

apply plugin: "maven-publish"
apply plugin: "signing"

sourceCompatibility = JavaVersion.VERSION_1_8

def getGitVersion() {
    return 'git rev-parse --short HEAD'.execute().text.trim()
}

jar {
    into("META-INF/") {
        from rootProject.file("LICENSE")
    }
    into("META-INF/maven/$project.group/$project.name") {
        from { generatePomFileForMavenJavaPublication }
        rename ".*", "pom.xml"
    }
    afterEvaluate {
        manifest {
            attributes 'Implementation-Title': archiveBaseName
            attributes 'Implementation-Version': archiveVersion
            attributes 'Implementation-Git-Version': getGitVersion()
            attributes 'Implementation-Vendor': '杭州财人汇网络股份有限公司'
            attributes 'Implementation-Vendor-Id': 'CRH'
            attributes 'Built-Gradle': gradle.gradleVersion
            attributes 'Bundle-DocURL': 'https://cpe-framework/'
            attributes 'Build-OS': System.getProperty("os.name")
            attributes 'Built-By': System.getProperty("user.name")
            attributes 'Build-Jdk': System.getProperty("java.version")
            attributes 'Build-Timestamp': LocalDateTime.now().format("yyyy-MM-dd HH:mm:ss")
        }
    }
}

//noinspection GroovyAssignabilityCheck
task sourcesJar(type: Jar) {
    archiveClassifier = 'sources'
    from sourceSets.main.allSource
}

task javadocJar(type: Jar) {
    archiveClassifier = 'javadoc'
    from javadoc
}

javadoc {
    afterEvaluate {
        configure(options) {
            encoding "UTF-8"
            charSet 'UTF-8'
            author true
            version true
            failOnError false
            links "http://docs.oracle.com/javase/8/docs/api"
        }
    }
}


publishing {
    repositories {
        maven {

            name 'nexus'
            allowInsecureProtocol true
            url "http://maven.cairenhui.com/nexus/content/repositories/crh_dev"
            credentials {
                username rootProject.nexus_username
                password rootProject.nexus_password
            }
        }
    }

    publications {

        mavenJava(MavenPublication) {
            from components.java

            artifact sourcesJar
//            artifact javadocJar

            versionMapping {
                usage('java-api') {
                    fromResolutionOf('runtimeClasspath')
                }
                usage('java-runtime') {
                    fromResolutionResult()
                }
            }

            pom {
                name = 'cpe-framework'
                packaging 'jar'
                description = 'cpe-framework'
                url = 'http://git.cairenhui.com/cairh-cpe/cpe-framework'

                scm {
                    connection = 'scm:*********************:cairh-cpe/cpe-framework.git'
                    developerConnection = 'scm:*********************:cairh-cpe/cpe-framework.git'
                    url = 'http://git.cairenhui.com/cairh-cpe/cpe-framework'
                }

                developers {
                    developer {
                        id = 'cairh'
                        name = 'leizhieji'
                        email = '<EMAIL>'
                    }
                }

                withXml {
                    def root = asNode()
                    root.dependencies.'*'.findAll {
                        def d = it
                        d.scope.text() == 'runtime' && project.configurations.findByName("implementation").allDependencies.find { dep ->
                            dep.name == it.artifactId.text()
                        }.each() {
                            d.scope*.value = 'compile'
                            d.appendNode('optional', true)
                        }
                    }
                }
            }
        }
    }
}

//signing {
//    sign publishing.publications.maven
//}
