#!/bin/bash

java -version
echo "CLASSPATH"=$CLASSPATH
echo "SPRING_PROFILES_ACTIVE"=$SPRING_PROFILES_ACTIVE
echo "SPRING_CONFIG_ADDITIONAL_LOCATION"=$SPRING_CONFIG_ADDITIONAL_LOCATION

echo " +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+"
echo " |c|p|e|-|p|r|o|d|u|c|t|s|-|m|o|n|i|t|o|r|-|b|a|c|k|e|n|d|"
echo " +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+"

nohup java -Xms512m -Xmx1024m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -Duser.language=zh -Duser.country=CN org.springframework.boot.loader.JarLauncher $(pwd) & >  startup.log
sleep 1s
tail -n 10 startup.log
echo "END"
